name: PR CI pipeline
on: 
  workflow_dispatch:
  pull_request: 
jobs:
  check-pr-title:
    name: Check PR title
    if: ${{ github.actor != 'dependabot[bot]' }}
    uses: Ebury/github-tools/.github/workflows/check-pr-title-reusable-workflow.yml@master
  check-pr-description:
    runs-on: ubuntu-latest
    name: Check PR description
    steps:
      - uses: actions/checkout@v4  
        with:
          fetch-depth: 0
          persist-credentials: false
          ref: ${{ github.head_ref }} 
      - env:
         GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}   
        run: |
          PR_DESCRIPTION=$(gh pr view --json body --jq '.body')
          PR_DESCRIPTION_LENGTH=${#PR_DESCRIPTION}
          if [ $PR_DESCRIPTION_LENGTH -lt 1 ]; then
            echo "Missing PR description."
            exit 1
          fi
  generate-codeowners-root:   
    name: Generate CODEOWNERS root
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task generate-root-codeowners-file
  lint:   
    name: <PERSON>t
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task lint
  validate-packages-folder-structure:   
    name: Validate packages folder structure
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task validate-packages-folder-structure
  validate-json-schemas:    
    name: Validate JSON Schemas
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task validate-json-schemas
  validate-package-jsons:    
    name: Validate Package JSONs
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task validate-package-jsons
  ts-check-changed:  
    name: Run Typescript Type Checking Monorepo-wide
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task ts-check
  test-build-changed-processes:   
  # NOTE: for security reasons, GAS processes won't be pushed to the server so we don't need to store credentials in the CI; they will still tested for bundling in the CI
    name: Test Build Changed Processes
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task test-build-changed-processes
  format:
    name: Format Code
    needs: [generate-codeowners-root, lint, validate-packages-folder-structure, validate-json-schemas, validate-package-jsons, ts-check-changed, test-build-changed-processes]
    # run this job even if the previous jobs fail, but still only after the previous jobs finish
    if: ${{ always() }} 
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: task format
  commit-changes:
    name: Commit and push changes
    needs: [format]
    # run this job even if the previous jobs fail, but still only after the previous jobs finish
    if: ${{ always() }} 
    uses: ./.github/workflows/run-deno-command.yaml
    with:
      deno_command: run ./src/environment/build-scripts/ci/commit-detected-changes.ts commit-changes
  # note: this last step is necessary for simplifying the branch protection setup, so that when we specify the required checks we don't need to specify one by one all the checks; also the name could be changed in the future - if and when we separate the CD part - but for now it's left like this to match the name in the branch protection setup and avoid disruptions
  ci-cd-pipeline:
    needs: ["check-pr-title","check-pr-description","generate-codeowners-root","lint","validate-packages-folder-structure","validate-json-schemas","validate-package-jsons","ts-check-changed","test-build-changed-processes","format","commit-changes"]
    runs-on: ubuntu-latest
    steps:
      - name: CI Pipeline Success
        run: echo "CI Pipeline Success"

        