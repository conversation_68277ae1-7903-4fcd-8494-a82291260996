on:
    workflow_call:
      inputs:
        deno_command:
          required: true
          type: string 

jobs:
    run-deno-command: 
        runs-on: ubuntu-latest
        permissions: write-all 
        env:  
          DEBUG: ${{ vars.DEBUG }}
          DENO_NO_PACKAGE_JSON: "1" 
          DENO_COMMAND: ${{ inputs.deno_command }}
        steps:
            - uses: actions/checkout@v4
              with:
                fetch-depth: 0 
                ref: ${{ github.head_ref }}
                token: ${{ secrets.GITHUB_TOKEN }}             
            - uses: fregante/setup-git-user@024bc0b8e177d7e77203b48dab6fb45666854b35
              # aquasec requires pinning to commit sha
            - uses: denoland/setup-deno@4606d5cc6fb3f673efd4f594850e3f4b3e9d29cd
              with:        
                deno-version: "2.2.0" 
            - name: Run Command  
              env:
                DENO_COMMAND: ${{ inputs.deno_command }}
              run: ./deno.sh $DENO_COMMAND

              

