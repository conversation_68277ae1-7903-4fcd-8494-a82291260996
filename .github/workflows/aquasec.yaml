---
name: Aquasec
on: # yamllint disable-line rule:truthy
  # As in our repository we don't allow direct pushes to main, removing this part as Aquasec checks will be already enforced in the PR. 
  # This change has been agreed with SecEng team, see approval in the PR where the change was made https://github.com/Ebury/business-process-automation/pull/1065
  # push:
  #   branches:
  #     - main
  pull_request:
    types: [ opened, reopened, edited, synchronize ]
jobs:
  Aquasec:
    if: ${{ github.actor != 'dependabot[bot]' }}
    uses: Ebury/seceng/.github/workflows/aquasec-reusable-workflow.yaml@master
    secrets: inherit


