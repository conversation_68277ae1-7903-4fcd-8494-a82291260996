# CI/CD pipelines

The CI pipeline is implemented using [GitHub Actions](https://github.com/actions). The pipeline is defined in this folder.

## Aquasec vulnerability scanning

The [aquasec](./workflows/aquasec.yml) workflow is triggered on every pull request to the `main` branch, and it runs a reusable job that scans the Docker images for vulnerabilities using [Aqua Security](https://www.aquasec.com/); the results are posted as a comment to the PR.

This is an implementation by the Security Engineering team, see [the related RFC](https://github.com/Ebury/ebury-blueprints/blob/master/docs/components/ssdlc/ssdlc-aquasec-deployment.md) for more details.

## CI Pipeline

The CI pipeline is triggered on every pull request to the `main` branch, and it runs the following steps:

- Set the Git user;
- Check if the PR has a description as first step;
- Install dependencies (and caching them);
- Generate the root `CODEOWNERS` file, in case of changes automatically commit them;
- Lint the code, in case of changes automatically commit them;
- Validate the folder structure of the packages;
- Validate packages JSONs against their schemas, in case of changes (updated list of processes names) automatically commit them;
- Install pnpm and dependencies;
- Type check the code;
- Test the changed processes' ability to build.
- Format the code, in case of changes automatically commit them;
- In case of errors, post a comment to the PR with the issues found; the pipeline will then fail, blocking the merge of the PR;
- Set commit status to success.
- When the PR is approved and merged to main, it updates the impacts metrics.

## Auto-commits

> The auto-commit runs after each step that may generate changes; this logic was chosen against running the auto-commit only at the end of the pipeline, because there are steps that may fail (e.g. linting), thus preventing previous changes to be committed (e.g. formatting). See [the CI pipeline](./workflows/pull-request-ci.yml) for more details.

Auto-commits are co-authored by github-actions[bot] and the user that committed last.

## Checks and outcomes

Using the [continue-on-error Github Action](https://github.com/marketplace/actions/continue-on-error-comment), the CI will:

- proceed through all the checks;
- if any of them fails, the error is temporary ignored and a comment about the issue is added to the PR;
- finally, all the outcomes from the previous steps are evaluated and, if any of them is a failure, the PR validation will fail too.

This should result in an improved Developer eXperience and a lot of saved time and frustration, as well as less computation for the CI:

- issues with the PR are immediately evident without having to inspect the Github Action's execution;
- no need for consecutive runs, all the issues are immediately detected, so a single commit should be enough to fix them all (hopefully).

## CD Pipeline

[!NOTE]
Currently all CD for all platforms is disabled due to porting to Deno.

### Google Apps Script (GAS)
The CD pipeline is triggered after the CI pipeline, and it runs the following steps:

- handles the credentials file that is stored as a repository secret, saving it as a file in the runner to be used by [clasp](https://github.com/google/clasp);
- identifies the environment to deploy to, based on the name of the branch and if the PR is merged or not: it will only deploy in "production" if the target branch is "main";
- detects the processes that have changed, and for each of them:
  - bundles the code and uploads it to the Apps Script project;
  - creates a new version of the Apps Script project;
  - deploys the new version of the Apps Script project;
- adds a comment to the PR with the link to the Apps Script project and a reminder to authorize the project (not possible to automate this step).

