# Dependabot is a GitHub service that automatically creates pull requests to update dependencies to their latest version. It is configured in the dependabot.yml file.
version: 2
updates:
  # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "daily" # every weekday, Monday to Friday.
    target-branch: "main"
    open-pull-requests-limit: 0 # This disables PRs for version updates, as we target only security updates with dependabot 

  # Maintain dependencies for npm
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "daily" # every weekday, Monday to Friday.
    target-branch: "main"
    open-pull-requests-limit: 0 # This disables PRs for version updates, as we target only security updates with dependabot
