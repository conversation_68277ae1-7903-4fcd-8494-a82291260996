import "jsr:@std/dotenv@0.224.0/load";
// import { Octokit } from "npm:octokit";
// import { SimpleGit, simpleGit, SimpleGitOptions } from "npm:simple-git";
// import { getCurrentCommitURL } from "../utilities/get-current-commit.ts";
// import { getDiff } from "./get-diff.ts";
import { BUILD_SCRIPT_CONSTANTS } from "./constants.ts";
import { develop } from "./develop.ts";
import { deployGas } from "./deploy/deploy-gas.ts";
import { deployCloudRunprocess } from "./deploy/deploy-cloud-run-deno.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Deploys a process.
 */
export const deploy = async (options?: {
  processToDevelop?: string;
  stageToDevelop?: string;
  skipSetup?: boolean;
}) => {
  Logger.info(
    `${BUILD_SCRIPT_CONSTANTS.SEPARATOR} DEPLOYING ${BUILD_SCRIPT_CONSTANTS.SEPARATOR}`,
  );
  const result = await develop({
    shouldWatch: false,
    skipSetup: options?.skipSetup,
    isDeploying: true,
  });
  console.log({ result });
  if (!result) {
    throw new Error("Bundle failed, aborting deployment");
  }
  Logger.info("Bundle successful");
  if (typeof result === "boolean") {
    return;
  }
  if (result.platform === "gas") {
    await deployGas({
      scriptURL: result.scriptURL,
    });
  }
  if (result.platform === "cloud-run") {
    await deployCloudRunprocess({
      processName: result.processName,
      stageName: result.stage,
    });
  }
  return result;
};

if (Deno.args.includes("--exec-deploy")) {
  await deploy();
  Deno.exit();
}

/**
 * CURRENTLY THE AUTOMATED DEPLOYMENT TO PRODUCTION IS DISABLED DUE TO THE BIG REFACTORING TO DENO.
 */

// const options: Partial<SimpleGitOptions> = {
//   baseDir: Deno.cwd(),
//   binary: "git",
//   maxConcurrentProcesses: 6,
//   trimmed: false,
// };

// const CD_SUPPORTED_PLATFORMS = ["gas"];

// export const pushToProdAutomated = async () => {
//   const git: SimpleGit = simpleGit(options);
//   const currentBranch = await git.branchLocal();
//   const currentBranchName = currentBranch.current;
//   const isCurrentBranchMain = currentBranchName === "main";
//   const changedFilesPaths = await getDiff(currentBranchName);
//   // identify the changed Denoes from the `file` property of the changed files; the Deno id is identified in this way `src/packages/Denoes/{processId}/...`
//   const changedDenoesRaw = changedFilesPaths.files
//     .filter((filePath) => filePath.file.startsWith("src/packages/Denoes/"))
//     .map((filePath) => filePath.file.split("/")[3])
//     .filter((processId) => processId);
//   const changedDenoes = [...new Set(changedDenoesRaw)];
//   console.log({ changedDenoes });
//   const changedDenoesExisting = changedDenoes.filter((processId) => {
//     const DenoExists = fs.existsSync(`src/packages/Denoes/${processId}`);
//     if (!DenoExists) {
//       Logger.warn(`Deno ${processId} does not exist, skipping`);
//     }
//     return DenoExists;
//   });
//   console.log({ changedDenoesExisting });
//   if (changedDenoesExisting.length === 0) {
//     Logger.info("No Denoes changed, skipping deployment");
//     return;
//   }
//   const octokit = new Octokit({
//     auth: Deno.env.GITHUB_TOKEN,
//   });
//   const owner = "Ebury";
//   const repo = "business-Deno-automation";
//   // get the pr number from the current commit sha
//   const commitSha = await git.revparse(["HEAD"]);
//   const {
//     data: { items: prs },
//   } = await octokit.rest.search.issuesAndPullRequests({
//     q: `repo:${owner}/${repo} is:pr ${commitSha}`,
//   });
//   const prNumber = prs[0]?.number;
//   const stageToDevelop = isCurrentBranchMain ? "production" : "staging";
//   console.log({
//     commitSha,
//     prNumber,
//     isTargetBranchMain: isCurrentBranchMain,
//     stageToDevelop,
//   });
//   for (const processId of changedDenoesExisting) {
//     const DenoPackageJSONPath =
//       `src/packages/Denoes/${processId}/package.json`;
//     const DenoPackageJSON = JSON.parse(
//       fs.readFileSync(DenoPackageJSONPath, "utf8"),
//     );
//     const platform = DenoPackageJSON.platform;
//     if (!CD_SUPPORTED_PLATFORMS.includes(platform)) {
//       console.log(
//         `Skipping ${processId} due to unsupported platform (${platform})`,
//       );
//       continue;
//     }
//     console.log(`Deploying ${processId} to prod`);
//     const result = await deploy({
//       DenoToDevelop: processId,
//       stageToDevelop,
//       skipSetup: true,
//     });
//     const isResultEmpty = !result;
//     const isPrNumberEmpty = !prNumber;
//     const isResultOrPrEmpty = isResultEmpty || isPrNumberEmpty;
//     if (isResultOrPrEmpty) {
//       continue;
//     }
//     const { scriptUrl, newVersionNumber } = result;
//     // add a comment to the current pr using octokit
//     const comment =
//       `Successfully deployed **${processId} [${stageToDevelop}]** (version ${newVersionNumber}).\n📌Remember to authorize the script to run! ${scriptUrl}`;
//     await octokit.rest.issues.createComment({
//       owner: owner,
//       repo: repo,
//       issue_number: prNumber,
//       body: comment,
//     });
//   }
// };
