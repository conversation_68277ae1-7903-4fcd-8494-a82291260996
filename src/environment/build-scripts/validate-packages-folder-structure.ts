import { join } from "https://deno.land/std@0.220.1/path/join.ts";
import { Logger } from "modules/logger/index.ts";

type PackageDirectorysSchema = {
  fileNames: string[];
  conditions?: {
    if: {
      path: string;
      key: string;
      value: string;
    };
    then: string[];
  };
};

/**
 * The map of the package types and their directory structure.
 * The key is the package type, the value is the schema.
 * The schema contains the file names that must be present in the package directory.
 * The schema also contains the conditions that must be met in order for the files to be present in the package directory.
 * The conditions are used when the directory structure depends on the package.json file.
 * For example, the appsscript.json file is only present in the packages that are meant to be deployed to Google Apps Script.
 * The conditions are defined in the if property, and the files that must be present in the package directory if the conditions are met are defined in the then property.
 */
const packageDirectorysSchemas: Record<string, PackageDirectorysSchema> = {
  processes: {
    fileNames: ["package.json", "readme.md"],
    conditions: {
      if: {
        path: "package.json",
        key: "platform",
        value: "gas",
      },
      then: ["appsscript.json"],
    },
  },
} as const;

const throwErrorWithDirPath = (message: string, dirPath: string) => {
  throw new Error(`${message}\n${dirPath}`);
};
/**
 * Validates that the packages directory structure is correct.
 * Depending on the package type, the directory structure is different; the structure for each package type is defined in the variable packageDirectorysSchemas.
 * The map is then used to validate the directory structure.
 * If the directory structure is not correct, the script will throw an error.
 */
export const validatePackagesDirectoryStructure = async () => {
  const packagesPath = `${Deno.cwd()}/src/packages/`;
  for await (const packageContainerEntry of Deno.readDir(packagesPath)) {
    if (!packageContainerEntry.isDirectory) {
      continue;
    }
    const packageType = packageContainerEntry
      .name as keyof typeof packageDirectorysSchemas;
    const packageRequiredConfig = packageDirectorysSchemas[packageType];
    if (!packageRequiredConfig) {
      Logger.debug(
        `Skipping validation for package type "${packageType}" because there is no configuration for it.`,
      );
      continue;
    }
    Logger.info(`Validating package type "${packageType}"...`);
    const packageTypeDirectoryPath = join(
      packagesPath,
      packageContainerEntry.name,
    );
    for await (const packageEntry of Deno.readDir(packageTypeDirectoryPath)) {
      if (!packageEntry.isDirectory) {
        continue;
      }
      const dirName = packageEntry.name;
      const dirPath = join(packageTypeDirectoryPath, dirName);
      const isDirNameValid = validateDirectoryName(dirName);
      if (!isDirNameValid) {
        throwErrorWithDirPath(
          `Directory name "${dirName}" is not valid. Directory names must be lowercase and hyphen-separated.`,
          dirPath,
        );
      }
      const missingRequiredFiles = [...packageRequiredConfig.fileNames];
      for await (const availableFile of Deno.readDir(dirPath)) {
        const availableFileIndex = missingRequiredFiles.indexOf(
          availableFile.name.toLowerCase(),
        );
        if (availableFileIndex > -1) {
          missingRequiredFiles.splice(availableFileIndex, 1);
        }
      }
      if (missingRequiredFiles.length > 0) {
        throwErrorWithDirPath(
          `Directory "${dirName}" is missing the following required files: ${
            missingRequiredFiles.join(
              ", ",
            )
          }.`,
          dirPath,
        );
      }
      if (packageRequiredConfig.conditions) {
        const packageEntryPath = join(packageTypeDirectoryPath, dirName);
        const { if: ifCondition, then: thenCondition } = packageRequiredConfig.conditions;
        const ifPath = join(packageEntryPath, ifCondition.path);
        const ifFile = JSON.parse(await Deno.readTextFile(ifPath));
        const hasAdditionalCondition = ifFile[ifCondition.key] === ifCondition.value;
        if (!hasAdditionalCondition) {
          continue;
        }
        const missingAdditionalRequiredFiles = thenCondition;
        for await (const availableFile of Deno.readDir(packageEntryPath)) {
          const requiredFileNameIndex = missingAdditionalRequiredFiles.indexOf(
            availableFile.name.toLowerCase(),
          );
          if (requiredFileNameIndex > -1) {
            missingAdditionalRequiredFiles.splice(requiredFileNameIndex, 1);
          }
        }
        if (missingAdditionalRequiredFiles.length > 0) {
          throwErrorWithDirPath(
            `Directory "${dirName}" is missing the following required files: ${
              missingAdditionalRequiredFiles.join(
                ", ",
              )
            }`,
            dirPath,
          );
        }
      }
    }
    Logger.info(`Validated package type "${packageType}"`);
  }
  Logger.info("Validated all package types");
};
/**
 * Validates the directory name: it must be all lowercase and hyphen-separated.
 * @param directoryName The directory name to validate.
 * @returns Whether the directory name is valid.
 */
export const validateDirectoryName = (directoryName: string) => {
  const isDirectoryNameValid = /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(directoryName);
  return isDirectoryNameValid;
};

if (Deno.args[0] === "validate") {
  await validatePackagesDirectoryStructure();
}
