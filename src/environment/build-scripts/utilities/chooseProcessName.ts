import { colorText } from "utilities/color-text/index.ts";
import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";
import { listProcessNames } from "./list-process-names.ts";
import prompts from "prompts";
import { Logger } from "modules/logger/index.ts";

export const chooseProcessName = async (specifiedProcessName?: string): Promise<string> => {
  if (specifiedProcessName) {
    Logger.info(`Specified process "${specifiedProcessName}"`);
    const existingProcessNames = await listProcessNames();
    const isSpecifiedProcessNameValid = existingProcessNames.includes(
      specifiedProcessName,
    );
    if (!isSpecifiedProcessNameValid) {
      throw new Error(`Process "${specifiedProcessName}" does not exist`);
    }
    return specifiedProcessName;
  }
  Logger.info("Choosing process name.");
  const processNames = await listProcessNames();
  const processesAbsolutePath = new URL(
    BUILD_SCRIPT_CONSTANTS.PROCESSES_PATH,
    import.meta.url,
  ).pathname;
  // using prompts, ask the user to select a process to watch;
  const { process } = (await prompts({
    type: "autocomplete",
    name: "process",
    message: `${
      colorText(
        `Choose a process to watch (start typing to filter)\n`,
        "magenta",
      )
    }If you don't see the process you want, create a new folder in\n${processesAbsolutePath}`,
    choices: processNames.map((process) => ({
      title: process,
      value: process,
    })),
  })) as { process: string };
  // if the user didn't select a process, exit
  if (!process) {
    throw new Error("No process selected");
  }
  return process;
};
