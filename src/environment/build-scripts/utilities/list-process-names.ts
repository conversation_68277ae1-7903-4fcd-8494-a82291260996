import { Logger } from "modules/logger/index.ts";
import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";

const cachedVarId = "__PROCESS_NAMES";
const separator = ",";

export const listProcessNames = async (): Promise<string[]> => {
  const processNamesCached: string[] = Deno.env.get(cachedVarId)?.split(separator) ?? [];
  Logger.info("Listing process names.");
  const areProcessNamesCached = processNamesCached.length > 0;
  if (areProcessNamesCached) {
    Logger.debug("Using cached process names.");
    return processNamesCached;
  }
  // list an array of folders in the PROCESSES_PATH folder
  const children = Deno.readDir(
    new URL(
      `../../../../${BUILD_SCRIPT_CONSTANTS.PROCESSES_PATH}`,
      import.meta.url,
    )
      .pathname,
  );
  for await (const child of children) {
    if (child.isDirectory) {
      processNamesCached.push(child.name);
    }
  }
  const sorted = processNamesCached.sort();
  Deno.env.set(cachedVarId, sorted.join(separator));
  return sorted;
};
