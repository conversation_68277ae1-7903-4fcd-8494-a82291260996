import { ProcessSchema } from "types/process-schema.ts";

export const generateEnvironmentVariablesString = (options: {
  environmentVariables: ProcessSchema.CloudRunEnvironmentVariablesProperty;
  stageName: ProcessSchema.StageName;
  variableType: ProcessSchema.CloudRunEnvironmentVariable["type"] | ProcessSchema.CloudRunEnvironmentSecret["type"];
}) => {
  const { environmentVariables, stageName, variableType } = options;
  const envVarNames = Object.keys(environmentVariables) as string[];
  const chosenVariableNames = envVarNames
    .filter((envVarName) => environmentVariables[envVarName].type === variableType);
  const variables = chosenVariableNames
    .map((thisTypeVariableName) => {
      const variable = environmentVariables[thisTypeVariableName];
      let valueForStage;
      switch (variableType) {
        case "variable":
          valueForStage = (variable as ProcessSchema.CloudRunEnvironmentVariable).value[stageName];
          break;
        case "secret":
          valueForStage = (variable as ProcessSchema.CloudRunEnvironmentSecret).secretName[stageName];
          break;
      }
      if (!valueForStage) {
        return null;
      }
      const ifSecretWithVersion = variableType === "variable" ? valueForStage : `${valueForStage}:latest`;
      return `${thisTypeVariableName}=${ifSecretWithVersion}`;
    })
    .filter((secret) => !!secret) as string[];
  return variables.join(",");
};
