import { Logger } from "modules/logger/index.ts";

export const cleanDevFolders = async (): Promise<void> => {
  const devFolders = ["dist"];
  Logger.info(`Cleaning up dev folders: "${devFolders.join('", "')}"`);
  for (const folder of devFolders) {
    try {
      await Deno.remove(folder, {
        recursive: true,
      });
    } catch (error) {
      const isNotFound = error instanceof Deno.errors.NotFound;
      if (!isNotFound) {
        throw error;
      }
    }
  }
  Logger.info("Cleaned up dev folders");
};

if (Deno.args[0] === "clean-dev-folders") {
  await cleanDevFolders();
}
