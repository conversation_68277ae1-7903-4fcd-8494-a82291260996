import { getCurrentBranchName } from "utilities/get-current-branch-name.ts";
import { BundleScripts } from "../bundle/types.ts";
import prompts from "prompts";
import { Logger } from "modules/logger/index.ts";
import { ProcessSchema } from "types/process-schema.ts";

export const chooseStage = async (
  packageJson: BundleScripts.CloudRunProcessJSONTemplate,
  specifiedStageName?: string,
) => {
  if (specifiedStageName) {
    Logger.info(`Specified stage "${specifiedStageName}"`);
    const availableStageNames = Object.keys(
      packageJson.stages,
    ) as (keyof typeof packageJson.stages)[];
    const isSpecifiedStageNameValid = availableStageNames.includes(
      specifiedStageName as typeof availableStageNames[number],
    );
    if (!isSpecifiedStageNameValid) {
      throw new Error(
        `Stage "${specifiedStageName}" does not exist in process "${packageJson.name}"`,
      );
    }
    const isStageNameProduction = specifiedStageName === "production";
    if (isStageNameProduction) {
      const currentBranchName = await getCurrentBranchName();
      const isCurrentBranchMain = currentBranchName === "main";
      const isStatusActive = packageJson.status === "active";
      const isBranchMainAndStatusActive = isCurrentBranchMain &&
        isStatusActive;
      const warnMessages = {
        currentBranchNotMain: "The `production` stage is only available on the main branch",
        statusNotActive: "Production stage is only available when the `status` in the package.json is active",
      };
      const warnMessage = isCurrentBranchMain ? warnMessages.statusNotActive : warnMessages.currentBranchNotMain;
      if (!isBranchMainAndStatusActive) {
        throw new Error(warnMessage);
      }
    }
    return specifiedStageName as ProcessSchema.StageName;
  }
  // if the current branch name is not "main", disable the production stage
  const currentBranchName = await getCurrentBranchName();
  const isCurrentBranchMain = currentBranchName === "main";
  const isStatusActive = packageJson.status === "active";
  const isProductionReady = isCurrentBranchMain && isStatusActive;
  const warnMessages = {
    currentBranchNotMain: "The `production` stage is only available on the main branch",
    statusNotActive: "Production stage is only available when the `status` in the package.json is active",
  };
  const warnMessage = !isCurrentBranchMain ? warnMessages.currentBranchNotMain : warnMessages.statusNotActive;
  // ask the user to chose the developing stage from the stages available in the package.json file
  const stageNames = Object.keys(
    packageJson.stages,
  ) as (keyof typeof packageJson.stages)[];
  const isStageAvailable = stageNames.length > 0;
  if (!isStageAvailable) {
    throw new Error("No stages available");
  }
  const { stage: stageName } = (await prompts({
    type: "select",
    name: "stage",
    message: `Choose a stage`,
    warn: warnMessage,
    choices: stageNames.map((stageName) => {
      const isStageNameProduction = stageName === "production";
      const isDisabled = isStageNameProduction && !isProductionReady;
      return {
        title: stageName,
        value: stageName,
        disabled: isDisabled,
      };
    }),
  })) as { stage: keyof typeof packageJson.stages };
  // if the user didn't select a stage, exit
  if (!stageName) {
    throw new Error("No stage selected");
  }
  return stageName;
};
