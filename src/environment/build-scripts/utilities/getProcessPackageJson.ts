import { Logger } from "modules/logger/index.ts";
import { BundleScripts } from "../bundle/types.ts";
import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";

export const getProcessPackageJson = async (processName: string) => {
  const processFolderPath = `${BUILD_SCRIPT_CONSTANTS.PROCESSES_PATH}/${processName}`;
  const processAbsolutePath = new URL(`../../../${processFolderPath}`, import.meta.url).pathname;
  Logger.info([
    `Choosen process: ${processName}`,
    processAbsolutePath.toString(),
  ]);
  const packageJsonPath = `${processFolderPath}/package.json`;
  const fileContent = await Deno.readTextFile(packageJsonPath);
  const packageJson = JSON.parse(
    fileContent,
  ) as unknown as (BundleScripts.CloudRunProcessJSONTemplate | BundleScripts.ProcessJSONTemplate | BundleScripts.GasProcessJSONTemplate | BundleScripts.MakeProcessJSONTemplate);
  return {
    packageJson,
    processFolderPath,
    processAbsolutePath,
  };
};
