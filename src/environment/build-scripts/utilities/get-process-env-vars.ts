import { ProcessSchema } from "types/process-schema.ts";

export const getProcessEnvVars = (
  packageJSON: ProcessSchema.CloudRunDenoProcess,
) => {
  const environmentVariables = packageJSON.deploymentOptions?.environmentVariables || {};
  const environmentVariablesArray = Object.entries(environmentVariables) as [string, ProcessSchema.CloudRunEnvironmentVariable | ProcessSchema.CloudRunEnvironmentSecret][];
  const envVars = environmentVariablesArray
    .filter(([_, variable]) => variable.type === "variable")
    .map(([key, variable]) => ({ key, ...variable }) as ProcessSchema.CloudRunEnvironmentVariable & { key: string });
  const secrets = environmentVariablesArray
    .filter(([_, variable]) => variable.type === "secret")
    .map(([key, variable]) => ({ key, ...variable }) as ProcessSchema.CloudRunEnvironmentSecret & { key: string });
  return {
    envVars,
    secrets,
  };
};
