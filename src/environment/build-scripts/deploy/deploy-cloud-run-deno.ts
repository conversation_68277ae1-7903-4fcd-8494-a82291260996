import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";
import passwordlessAuthAPIJSON from "processes/passwordless-authentication-api/package.json" with { type: "json" };
import periodicReviewsAPIJSON from "processes/periodic-reviews-api/package.json" with { type: "json" };
import reverseProxyJSON from "processes/reverse-proxy/package.json" with { type: "json" };
import bpaAPIDocsJSON from "processes/bpa-api-docs/package.json" with { type: "json" };

import { executeCommand } from "utilities/execute-command.ts";
import { ProcessSchema } from "types/process-schema.ts";
import { getProcessEnvVars } from "../utilities/get-process-env-vars.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Only the passwordless auth API is allowed to be unauthenticated, meaning exposed to the internet, as it will function as a broker to our other services that will be available only within GCP.
 * As of porting to Deno, the periodic reviews API is also exposed to the internet as otherwise we'd have to entirely rewrite the authentication system for periodic-reviews-interface.
 */
const ALLOWED_UNAUTHENTICATED = [
  passwordlessAuthAPIJSON.name,
  periodicReviewsAPIJSON.name,
  reverseProxyJSON.name,
  bpaAPIDocsJSON.name,
];

export const deployCloudRunprocess = async (options: {
  processName: string;
  stageName: string;
}) => {
  const { processName, stageName } = options;
  Logger.info(
    `Deploying Cloud Run process "${processName}-${stageName}"`,
  );
  const allowUnauthenticated = ALLOWED_UNAUTHENTICATED.includes(processName);
  const allowUnauthenticatedStr = `--${!allowUnauthenticated ? "no-" : ""}allow-unauthenticated`;
  const processPath = new URL(`../../../../src/packages/processes/${processName}/package.json`, import.meta.url).pathname;
  const processPackageJSON = (await import(processPath, { with: { type: "json" } })).default as ProcessSchema.CloudRunDenoProcess;
  const {
    port = 8080,
    memory = "1024Mi",
    cpu = "0.5",
  } = processPackageJSON?.deploymentOptions || {};
  const typedStageName = stageName as ProcessSchema.StageName;
  const allEnvVars = getProcessEnvVars(processPackageJSON);
  const environmentVariablesString = allEnvVars.envVars
    .filter((envVar) => envVar.value[typedStageName] !== null)
    .map((envVar) => `${envVar.key}=${envVar.value[typedStageName]}`)
    .join(",");
  const secretsString = allEnvVars.secrets
    .filter((secret) => secret.secretName[typedStageName] !== null)
    .map((secret) => `${secret.key.trim()}=${secret.secretName[typedStageName]?.trim()}:latest`)
    .join(",");
  const shouldDeployAsJob = processPackageJSON?.deploymentOptions?.deployAsJob;
  const mainCommandParts = shouldDeployAsJob ? ["beta", "run", "jobs"] : ["run"];
  const commonParts = [
    `${processName}-${stageName}`,
    "--project=appscript-296515",
    "--source=./dist",
    "--region=europe-west1",
    `--memory=${memory}`,
    `--cpu=${cpu}`,
  ];
  const specificParts = shouldDeployAsJob ? [] : [
    `--port=${port}`,
    allowUnauthenticatedStr,
    // `--revision-suffix=${Date.now()}`, // For some reason suddenly gcloud run deploy is failing into generating a new revison name. This PR adds a random revision suffix to overcome this issue. // now it works again, but leaving it here for now, in case it breaks again.
  ];
  const commandParts = [
    "gcloud",
    ...mainCommandParts,
    "deploy",
    ...commonParts,
    ...specificParts,
  ];
  if (secretsString.length > 0) commandParts.push(`--set-secrets=${secretsString}`);
  if (environmentVariablesString.length > 0) commandParts.push(`--set-env-vars=${environmentVariablesString}`);
  await executeCommand({
    commandParts,
  });
  Logger.info(
    `${BUILD_SCRIPT_CONSTANTS.SEPARATOR} DEPLOYED ${BUILD_SCRIPT_CONSTANTS.SEPARATOR}`,
  );
  return {
    scriptUrl: "https://console.cloud.google.com/run",
    newVersionNumber: 1,
  };
};
