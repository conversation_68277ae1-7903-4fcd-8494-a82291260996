import { getCurrentCommitURL } from "../../utilities/get-current-commit.ts";
import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";
import { Impersonator } from "../../../packages/modules/cloud-run-impersonator/index.ts";
import { fetchAutomationCredentials } from "utilities/fetchCredentials.ts";
import { Logger } from "modules/logger/index.ts";

export const deployGas = async (options: {
  scriptURL: string;
}) => {
  const { scriptURL } = options!;
  const scriptId = scriptURL.split("https://script.google.com/home/<USER>/")[1].split(
    "/",
  )[0];
  const credentials = await fetchAutomationCredentials();

  const impersonator = new Impersonator({
    scopes: [
      "https://www.googleapis.com/auth/script.projects",
      "https://www.googleapis.com/auth/script.deployments",
    ],
    subject: "<EMAIL>",
    credentials,
  });
  const appsScriptclient = impersonator.getAppsScriptClient();
  Logger.debug(`creating new version`);
  const newVersion = await appsScriptclient.projects.versions.create({
    scriptId,
    requestBody: {
      description: await getCurrentCommitURL(),
    },
  });
  const isNewVersionSuccessful = newVersion.status === 200;
  if (!isNewVersionSuccessful) {
    throw new Error(JSON.stringify(newVersion));
  }
  const newVersionNumber = newVersion.data.versionNumber;
  Logger.debug(`new version number: ${newVersionNumber}`);
  Logger.debug(`getting active deployments`);
  const existingDeploymentsRequest = await appsScriptclient.projects
    .deployments.list({
      scriptId,
    });
  const isExistingDeploymentsRequestSuccessful = existingDeploymentsRequest.status === 200;
  if (!isExistingDeploymentsRequestSuccessful) {
    throw new Error(JSON.stringify(existingDeploymentsRequest));
  }
  const activeDeployments = existingDeploymentsRequest.data.deployments!
    .filter(
      (deployment: { deploymentConfig: { versionNumber: number } }) => deployment.deploymentConfig.versionNumber,
    );
  const areThereActiveDeployments = activeDeployments.length > 0;
  if (!areThereActiveDeployments) {
    Logger.debug(`no active deployments found, creating one`);
    const newDeployment = await appsScriptclient.projects.deployments
      .create({
        scriptId,
        requestBody: {
          versionNumber: newVersionNumber,
        },
      });
    activeDeployments.push(newDeployment.data);
  }
  Logger.debug(`${activeDeployments.length} active deployments found`);
  const deploymentPromises = activeDeployments.map((deployment: { deploymentId: string }) => {
    const updateOptions = {
      deploymentId: deployment.deploymentId,
      scriptId,
      requestBody: {
        deploymentConfig: {
          versionNumber: newVersionNumber,
        },
      },
    };
    return appsScriptclient.projects.deployments.update(updateOptions);
  });
  Logger.info(`updating ${deploymentPromises.length} deployments`);
  const failedDeployments = [] as unknown[];
  for (const deploymentPromise of deploymentPromises) {
    try {
      const result = await deploymentPromise;
      const isStatusCodeSuccess = result.status.toString().startsWith(
        "2",
      );
      if (!isStatusCodeSuccess) {
        failedDeployments.push(result.data);
      }
    } catch (e) {
      failedDeployments.push(e);
    }
    // wait between deployments, otherwise the update won't work even if the API returns a 200
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }
  const areThereFailedDeployments = failedDeployments.length > 0;
  if (areThereFailedDeployments) {
    throw new Error(
      `Failed deployments: ${JSON.stringify(failedDeployments, null, 2)}`,
    );
  }
  Logger.info(
    `${BUILD_SCRIPT_CONSTANTS.SEPARATOR} DEPLOYED ${BUILD_SCRIPT_CONSTANTS.SEPARATOR}`,
  );
  Logger.warning([`Remember to authorize the script to run!`, scriptURL]);
  return {
    scriptURL,
    newVersionNumber,
  };
};
