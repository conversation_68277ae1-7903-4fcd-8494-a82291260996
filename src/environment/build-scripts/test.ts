import { BUILD_SCRIPT_CONSTANTS } from "./constants.ts";
import { Logger } from "modules/logger/index.ts";
import { chooseProcessName } from "./utilities/chooseProcessName.ts";
import { BundleScripts } from "./bundle/types.ts";
import { getProcessPackageJson } from "./utilities/getProcessPackageJson.ts";
import { chooseStage } from "./utilities/chooseStage.ts";
import { testProcess } from "./tests/testProcess.ts";

/**
 * Tests a process.
 */
export const test = async (): Promise<void> => {
  Logger.info(
    `${BUILD_SCRIPT_CONSTANTS.SEPARATOR} TESTING ${BUILD_SCRIPT_CONSTANTS.SEPARATOR}`,
  );
  // the flag --process is used to specify the process to develop, f.i. --process=onboarding
  const processNameProvided = Deno.args.find(
    (arg) => arg.startsWith("--process=") || arg.startsWith("-p="),
  )?.split("=")[1];
  const processName = await chooseProcessName(processNameProvided);
  const { packageJson, processFolderPath } = await getProcessPackageJson(processName);
  // the flag --stage is used to specify the stage to develop, f.i. --stage=staging
  const stageNameProvided = Deno.args.find(
    (arg) => arg.startsWith("--stage=") || arg.startsWith("-st="),
  )?.split("=")[1];
  const stageName = await chooseStage(
    packageJson as BundleScripts.CloudRunProcessJSONTemplate,
    stageNameProvided,
  );
  // the flag --watch is used to specify if the process should watch for changes
  const shouldWatch = Deno.args.includes("--watch") || Deno.args.includes("-w");
  // the flag --inspect is used to specify if the process should be inspected
  const traceLeaks = Deno.args.includes("--trace-leaks") || Deno.args.includes("-tl");
  Logger.debug({
    processFolderPath,
    stageName,
    shouldWatch,
    traceLeaks,
  });
  await testProcess({
    processFolderPath,
    stageName,
    shouldWatch,
    traceLeaks,
  });
};

if (Deno.args.includes("--exec-test")) {
  await test();
}
