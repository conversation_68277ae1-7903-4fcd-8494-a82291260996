# Build Scripts

this directory contains scripts used in the development process.

> **Note**: these scripts are not meant to be executed directly; to launch them use the `executeBuildScript` script or the `_execute-build-script` script in the root folder.

## Scripts

- `develop`: this is meant to be the one-stop-shop script to develop new stuff in the monorepo. Currently it supports the development of GAS processes, Deno processes (Cloud Run). It accepts the following flags:[`--skip-setup`(-sk)](#skip-setup), [`--build`(-b)](#build), [--process={processName} (-p=)](#process), [--stage={stageName} (-st=)](#stage), [`--inspect`(-i)](#inspect).
- `test`: this script will run the tests for a process specified in `{processFolder}/src/tests/index.test.ts`. It accepts the following flags: [--process={processName} (-p=)](#process), [--stage={stageName} (-st=)](#stage), [`--watch`(-w)](#watch), [`--trace-leaks`(-tl)](#trace-leaks).
- `deploy`: it deploys the chosen GAS process to the Google Apps Script server or the Deno process to the Cloud Run service; it will first bundle the processes, then it will create a new version of each active deployment for the script (with the commit url as description), and finally it will deploy the new version of the deployment; it accepts the following flags: [`--build`(-b)](#build), [--process={processName} (-p=)](#process), [--stage={stageName} (-st=)](#stage).
- `generate-root-codeowners-file`: This script generates the root CODEOWNERS file by traversing the entire repository and extracting rules from the CODEOWNERS files; it's used in the [CI pipeline](../../../.github/workflows/README.md).
- `format`: this script will format the files that are not excluded in the Deno root config file, leveraging Deno's built-in formatter. It's used in the [CI pipeline](../../../.github/workflows/README.md).
- `lint`: this script will lint the files that are not excluded in the Deno root config file, leveraging Deno's built-in linter. It's used in the [CI pipeline](../../../.github/workflows/README.md).
- `ts-check`: this script will run the Deno's TypeScript checker for all the files not excluded in the Deno root config file. It's used in the [CI pipeline](../../../.github/workflows/README.md).
- `ts-check-debug`: this script will run the Deno's TypeScript checker for all the files not excluded in the Deno root config file, but it will go through the processes one by one to be able to collect all the errors and finally print them (useful for debugging).
- `clean_dependencies`: this script will delete recursively all `node_modules`, delete `pnpm-lock.yaml` and clean the `pnpm store`.
- `test-build-processes`: this is a useful script to test if a process will build correctly. It accepts the following flags: [`--processes={processNames[,...]} (comma separated) - the names of the processes to test`].
- `test-build-all-processes`: this script will test the build of all the processes. It accepts the following flags: [`--start={number} - the index of the first process to test`], [`--number-of-tests={number} - the number of processes to test`].
- `test-build-changed-processes`: same as `test-build-all-processes` but it will only test the processes that have changed compared to the `main` branch.
- `update-impact-metrics`: this script will update the impact metrics for all the processes.
- `validate-json-schemas`: this script will validate all the JSON against their schemas, found in the `schemas` directory.
- `validate-packages-folder-structure`: this script will validate the folder structure of the packages, to make sure that required files are present. Its settings are set in the `packageFoldersSchemas` object in the `package.json` file.

### `--skip-setup` (-s)

This flag is used to skip the setup of the development environment when running the `develop` script. It's useful when you want to run the script multiple times in a row, without having to wait for the setup to complete every time.

Please use it with caution, as there could be some unexpected behavior if the dependencies are not installed, the validatinos are not performed and the bundling cache is not cleared.

### `--build` (-b)

This flag is used to reverse the default behaviour of the `develop` script, which is to watch for changes and bundle them. When this flag is passed, the script will bundle the processes and exit.

### `--process={processName}` (-p=)

This flag is used to directly specify the process without the need of selecting it from the list of available processes. It's useful when you want to run the script multiple times in a row, without having to select the process every time.

### `--stage={stageName}` (-st=)

This flag is used to directly specify the stage without the need of selecting it from the list of available stages. It's useful when you want to run the script multiple times in a row, without having to select the stage every time.

### `--inspect` (-i)

This flag is used to enable the inspector for the process.

### `--watch` (-w)

This flag is used to run the script in watch mode, which means that it will watch for changes in the source files and re-run the script when they change.

### `--trace-leaks` (-tl)

This flag is used to trace memory leaks in the process.

## Dependencies

### Development ([see](src/environment/build-scripts/)):

- "@google/clasp": a command line interface for Google Apps Script projects. It allows you to push and pull projects to and from the Apps Script server, manage deployments, and view logs. We use it as a dependency in our `develop` script;
- "@parcel/core": our bundler of choice, [Parcel](https://parceljs.org/); we're using it as a dependency in our `develop` script;
- "prompts": used in build scripts to ask questions to the user;
- "@swc/core": needed by `tsx` to run blazingly fast;
- "esbuild": used by `tsx`;

### Validation, formatting, linting:

- "ajv\*": utilities for JSON Schema validation;
- "eslint": [EsLint](https://eslint.org/) is our linter of choice, a standard in the industry;
- "eslint-interactive": a useful CLI to run ESLint in interactive mode, especially useful in big refactors;
- "@typescript-eslint/\*": plugins for ESLint to support typeScript;
- "prettier": [Prettier](https://prettier.io/) is our code formatter of choice, a standard in the industry;
