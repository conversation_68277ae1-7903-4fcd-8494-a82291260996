import { getChangedProcessFolders } from "../ci/get-changed-process-folders.ts";
import { testDevelop } from "./develop.test.ts";
/**
 * This test will iterate through the changed processes (comparing with the `main` branch) and check if they will build correctly.
 *
 * The white list is need for legacy processes like the ebury-add-on to be part of the monorepo withoout having to refactor it.
 */

const noCheckProcesses = ["ops-ebury-add-on"];
const changedProcessFolders = await getChangedProcessFolders();

const filteredChangedProcesses = changedProcessFolders.filter((processFolderName) => !noCheckProcesses.includes(processFolderName));

await testDevelop(filteredChangedProcesses);
