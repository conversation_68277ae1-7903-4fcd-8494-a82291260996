import { ProcessSchema } from "types/process-schema.ts";
import { getEnvVarsAndSecrets } from "utilities/get-env-vars-and-secrets.ts";
import { Logger } from "modules/logger/index.ts";
import { BUILD_SCRIPT_CONSTANTS } from "../constants.ts";
import { debounce } from "@std/async";

export const testProcess = async (options: {
  processFolderPath: string;
  stageName: ProcessSchema.StageName;
  traceLeaks?: boolean;
  shouldWatch?: boolean;
}) => {
  const { processFolderPath, stageName, traceLeaks, shouldWatch } = options;
  const processFolderAbsolutePath = new URL(
    `../../../../${processFolderPath}`,
    import.meta.url,
  ).pathname;
  const testEntrypointFileExists = await Deno.stat(
    `${processFolderAbsolutePath}/${BUILD_SCRIPT_CONSTANTS.TEST_ENTRY_POINT}`,
  ).catch(() => {
    Logger.warning(
      `${BUILD_SCRIPT_CONSTANTS.TEST_ENTRY_POINT} file not found in ${processFolderAbsolutePath}, skipping tests...`,
    );
    return;
  });
  if (!testEntrypointFileExists) return;
  await executeTest({ processFolderAbsolutePath, stageName, traceLeaks });
  if (!shouldWatch) {
    Logger.info("Tests completed");
    return;
  }
  Logger.info(`Watch mode enabled, running tests on changes in ${processFolderAbsolutePath}...`);
  const processFolderWatched = new URL(processFolderAbsolutePath, import.meta.url)
    .pathname;
  const processFolderWatcher = Deno.watchFs(processFolderWatched);
  for await (const event of processFolderWatcher) {
    const isEventModify = event.kind === "modify";
    if (!isEventModify) continue;
    debouncedTestExecution({ processFolderAbsolutePath, stageName, traceLeaks });
  }
};

const executeTest = async (options: {
  processFolderAbsolutePath: string;
  stageName: ProcessSchema.StageName;
  traceLeaks?: boolean;
}) => {
  const { processFolderAbsolutePath, stageName, traceLeaks } = options;
  const env = await getEnvVarsAndSecrets({ processFolderAbsolutePath, stageName });
  const args = [
    "test",
    "--config=../../../../deno_settings.json",
    "--allow-all",
    BUILD_SCRIPT_CONSTANTS.TEST_ENTRY_POINT,
  ];
  if (traceLeaks) {
    args.splice(1, 0, "--trace-leaks");
  }
  const executedCommand = new Deno.Command(Deno.execPath(), {
    args: args,
    cwd: processFolderAbsolutePath,
    env,
    clearEnv: false,
    stderr: "inherit",
    stdout: "inherit",
  });
  await executedCommand.output();
};

const debouncedTestExecution = debounce(async (options: {
  processFolderAbsolutePath: string;
  stageName: ProcessSchema.StageName;
  traceLeaks?: boolean;
}) => {
  const { processFolderAbsolutePath, stageName, traceLeaks } = options;
  Logger.info(`Changes detected in ${processFolderAbsolutePath}, restarting tests...`);
  await executeTest({ processFolderAbsolutePath, stageName, traceLeaks });
}, 500);
