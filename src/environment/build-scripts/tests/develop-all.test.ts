import { Logger } from "modules/logger/index.ts";
import { cleanDevFolders } from "../utilities/clean-dev-folders.ts";
import { listProcessNames } from "../utilities/list-process-names.ts";
import { testDevelop } from "./develop.test.ts";

/**
 * This test will iterate through processes in the `processes` folder and check if they will build correctly.
 * It accepts the following flags:
 * --start={number} - the index of the first process to test
 * --number-of-tests={number} - the number of processes to test
 */

const processNames = await listProcessNames();

const startOffset = Deno.args.find((arg) => arg.startsWith("--start"))?.split("=")[1] ?? 0;
const numberOfTests = Deno.args.find((arg) => arg.startsWith("--number-of-tests"))?.split(
  "=",
)[1] ?? processNames.length;
Logger.debug({ startOffset, numberOfTests });
await cleanDevFolders();

const processNamesToTest = processNames.splice(
  Number(startOffset),
  Number(numberOfTests),
);
await testDevelop(processNamesToTest);
