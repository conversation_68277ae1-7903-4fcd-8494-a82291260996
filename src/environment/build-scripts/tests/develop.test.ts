import { cleanDevFolders } from "../utilities/clean-dev-folders.ts";
import { develop } from "../develop.ts";
import { listProcessNames } from "../utilities/list-process-names.ts";
import { z } from "zod";
import { zodValidation } from "../../../packages/modules/zod-validation/index.ts";
import { extractExportedMethods } from "../bundle/bundle-gas/extract-exported-methods.ts";
import { ProcessSchema } from "types/process-schema.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * This test is meant to check if a process will be built correctly.
 * It accepts the following flags:
 * --processes={processNames[,...]} (comma separated) - the names of the processes to test
 */
export const testDevelop = async (processNamesToTest: string[]) => {
  const totalNumberOfTests = processNamesToTest.length;
  console.log({ totalNumberOfTests });
  Deno.test({
    sanitizeResources: true,
    name: `develop ${totalNumberOfTests} processes`,
    fn: async (test) => {
      const processNames = await listProcessNames();
      const uniqueProcessFolders = Array.from(new Set(processNamesToTest));
      const wrongProcessNames = uniqueProcessFolders.filter((processName) => !processNames.includes(processName));
      if (wrongProcessNames.length > 0) {
        Logger.error(`The following specified process names are invalid: ${wrongProcessNames.join(", ")}`);
        return;
      }
      const totalNumberOfTests = uniqueProcessFolders.length;
      let index = 1;
      for (const processName of uniqueProcessFolders) {
        await test.step(
          `develop ${processName}`,
          async (subtest) => {
            Logger.debug(`${index}/${totalNumberOfTests}`);
            await cleanDevFolders();
            const stage = `staging`;
            const shouldSkipSetup = false;
            const shouldWatch = false;
            console.log({ processName, stage, shouldSkipSetup, shouldWatch });
            const developResult = await develop({
              skipSetup: shouldSkipSetup,
              shouldWatch,
              processToDevelop: processName,
              stageToDevelop: stage,
            });
            if (!developResult) {
              throw new Error(`failed to develop ${processName}`);
            }
            const processBasePath = new URL(`../../../packages/processes/${processName}`, import.meta.url).pathname;
            const packageJsonFilePath = new URL(
              `../../../packages/processes/${processName}/package.json`,
              import.meta.url,
            );
            const packageJsonFile = await Deno.readTextFile(packageJsonFilePath);
            const packageJsonFileParsed = JSON.parse(packageJsonFile) as ProcessSchema.EndpointProcess;
            const isPlatformGAS = packageJsonFileParsed.platform === "gas";
            if (isPlatformGAS) {
              const exportFilePath = `${processBasePath}/backend.ts`;
              const exportedMethods = await extractExportedMethods({ entryPointPath: exportFilePath });
              const webAppExportedMethods = ["doGet", "doPost"];
              const hasWebAppExportedMethods = exportedMethods.some((method) => webAppExportedMethods.includes(method));
              // additional checks for GAS with webapp
              if (hasWebAppExportedMethods) {
                /**
                 * ```
                 * "webapp": {
                 *   "executeAs": "USER_DEPLOYING" | "USER_ACCESSING"
                 *   "access": "MYSELF" | "DOMAIN" | "ANYONE" | "ANYONE_ANONYMOUS"
                 * }
                 * ```
                 */
                await subtest.step(
                  `Check if appsscript.json file has the "webapp" property set properly`,
                  async () => {
                    const webappSchema = z.object({
                      webapp: z.object({
                        executeAs: z.union([z.literal("USER_DEPLOYING"), z.literal("USER_ACCESSING")]),
                        access: z.union([z.literal("MYSELF"), z.literal("DOMAIN"), z.literal("ANYONE"), z.literal("ANYONE_ANONYMOUS")]),
                      }),
                    });
                    const appsScriptFilePath = new URL(
                      `../../../packages/processes/${processName}/appsscript.json`,
                      import.meta.url,
                    );
                    const appsScriptFile = await Deno.readTextFile(appsScriptFilePath);
                    zodValidation({
                      objectToTest: JSON.parse(appsScriptFile),
                      schema: webappSchema,
                      errorHeader: `appsscript.json file does not have the "webapp" property set properly`,
                    });
                  },
                );
                await subtest.step(
                  `Check if in package.json, in the selected [stage] property, the sub-property "endpointUrl" is set`,
                  async () => {
                    const packageJsonStage = packageJsonFileParsed.stages[stage];
                    const packageJsonSchema = z.object({
                      endpointUrl: z.string(),
                    });
                    zodValidation({
                      objectToTest: packageJsonStage,
                      schema: packageJsonSchema,
                      errorHeader: `package.json file does not have the "endpointUrl" property set properly`,
                    });
                  },
                );
              }
            }
            index++;
          },
        );
      }
    },
  });
};

const shouldDevelop = Deno.args.includes("--exec");
if (shouldDevelop) {
  const processNamesToTest = Deno.args.find((arg) => arg.startsWith("--processes="))?.split("=")[1]?.split(",");
  // Logger.debug({ processNamesToTest });
  const processNamesSpecified = processNamesToTest && processNamesToTest.length > 0;
  if (!processNamesSpecified) {
    Logger.warning(
      "Please specify the names of the process to test with the flag --processes={processNames[,...]} (comma separated)",
    );
    Deno.exit(1);
  } else {
    await testDevelop(processNamesToTest);
  }
}
