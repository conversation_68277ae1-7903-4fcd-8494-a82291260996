import { walk } from "jsr:@std/fs";
import denoJ<PERSON><PERSON> from "../../../../deno_settings.json" with { type: "json" };
import { executeCommand } from "../../utilities/execute-command.ts";
import { Logger } from "modules/logger/index.ts";

export const typeCheck = async (options?: {
  tsCheckFolders?: string[];
}) => {
  const { exclude } = denoJSON;
  const files = await Array.fromAsync(
    walk("./src/packages", {
      includeDirs: false,
      includeFiles: true,
      match: [/\.ts$/],
      skip: [/node_modules/],
    }),
  );
  const selectedFolders = options?.tsCheckFolders || Deno.args.find((arg) => arg.startsWith("--ts-check-folders="))
    ?.split("=")[1]
    .split(
      ",",
    ) ||
    [];
  const includedFiles = files
    .filter((file) => !exclude.find((excluded) => file.path.startsWith(excluded)));
  const selectedFiles = includedFiles
    .filter((file) => selectedFolders.length > 0 ? selectedFolders.find((folderPath) => file.path.includes(folderPath)) : true);
  const sortedFiles = selectedFiles
    .sort((a, b) => a.path.localeCompare(b.path));
  const filePaths = sortedFiles
    .map((file) => file.path);
  if (filePaths.length === 0) {
    return;
  }
  const errorFolders: string[] = [];
  const warnings: string[] = [];
  const debugFlagActive = Deno.args.find((arg) => arg.startsWith("--ts-check-debug"));
  if (!debugFlagActive) {
    try {
      await executeCommand({
        commandParts: [
          "./deno.sh",
          "check",
          ...filePaths,
        ],
        cwd: "./",
      });
    } catch (_error) {
      console.log(_error);
      Deno.exit(1);
    }
  } else {
    const groupedFilesByContainingFolder = Object.groupBy(
      sortedFiles,
      (file) => {
        const folderPath = file.path.split("/").splice(0, 4).join("/");
        return folderPath;
      },
    );
    const allFolderNames = Object.keys(groupedFilesByContainingFolder);
    const startIndex = Number(
      Deno.args.find((arg) => arg.startsWith("--ts-check-debug-start"))
        ?.split("=")[1],
    ) || 0;
    const howManyFolders = Number(
      Deno.args.find((arg) => arg.startsWith("--ts-check-debug-how-many"))
        ?.split("=")[1],
    ) || allFolderNames.length;
    const folderNames = Object.keys(groupedFilesByContainingFolder)
      .splice(startIndex, howManyFolders);
    const totalFolders = folderNames.length;
    for (const [index, folderName] of folderNames.entries()) {
      const filePaths = groupedFilesByContainingFolder[folderName]?.map((
        file,
      ) => file.path) || [];
      try {
        Logger.debug(`${index + 1}/${totalFolders} ${folderName}`);
        if (filePaths?.length < 1) {
          warnings.push(`No files found in folder: ${folderName}`);
          continue;
        }
        await executeCommand({
          commandParts: [
            "./deno.sh",
            "check",
            ...filePaths,
          ],
          cwd: "./",
        });
      } catch (_error) {
        errorFolders.push(folderName);
      }
    }
    if (warnings.length > 0) {
      Logger.warning(`Found ${warnings.length} warnings\n\n`);
      Logger.warning(warnings.join("\n"));
    }
    if (errorFolders.length > 0) {
      Logger.error(
        `Found errors in ${errorFolders.length} folders.\nHere are the CLI snippets to check, just copy-paste and run in the terminal.\nYou can click on the folder name in the terminal to highlight it in the file explorer.\n\n`,
      );
      Logger.error(
        `Check only the folders with errors with this snippet:\n\ndeno task ts-check --ts-check-folders=${errorFolders.join(",")}\n\n`,
      );
      const errorFoldersBreakdown = errorFolders.map((
        folderName,
        index,
      ) => `${index}\ndeno task ts-check --ts-check-folders=${folderName}`);
      Logger.error(
        `Otherwise, check them separately:\n${errorFoldersBreakdown.join("\n")}`,
      );
      Deno.exit(1);
    }
  }
};

if (Deno.args.find((arg) => arg === "--exec-ts-check")) {
  await typeCheck();
}
