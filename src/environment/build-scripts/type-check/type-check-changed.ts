import { ProcessSchema } from "types/process-schema.ts";
import { getChangedProcessFolders } from "../ci/get-changed-process-folders.ts";
import { typeCheck } from "./type-check.ts";
import { Logger } from "modules/logger/index.ts";

export const typeCheckChanged = async () => {
  const changedProcessFolders = await getChangedProcessFolders();
  const changedDenoProcesses = changedProcessFolders
    .filter((folderName) => folderName !== null);
  if (changedDenoProcesses.length === 0) {
    Logger.info("No changed processes to type check");
    return;
  }
  Logger.info(`Type checking ${changedDenoProcesses.length} processes: ${changedDenoProcesses.join(", ")}`);
  // When the number of processes is large, Deno crashes with an out-of-memory error. Splitting the type check into chunks.
  const chunkSize = 10;
  const chunks = Array.from({ length: Math.ceil(changedDenoProcesses.length / chunkSize) }, (_, i) => changedDenoProcesses.slice(i * chunkSize, (i + 1) * chunkSize));
  for (const chunk of chunks) {
    await typeCheck({
      tsCheckFolders: chunk,
    });
  }
};

if (Deno.args.find((arg) => arg.startsWith("--exec-ts-check-changed"))) {
  await typeCheckChanged();
}
