import { getCurrentBranchName } from "../utilities/get-current-branch-name.ts";
import { bundleProcess } from "./bundle-process.ts";
import { cleanDevFolders } from "./utilities/clean-dev-folders.ts";
import { BUILD_SCRIPT_CONSTANTS } from "./constants.ts";
import { validateJSONSchemas } from "./validate-json-schemas.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Develop a process.
 * Accepts the flag `--skip-setup` to skip the setup process.
 */
export const develop = async (options?: {
  skipSetup?: boolean;
  shouldWatch?: boolean;
  processToDevelop?: string;
  stageToDevelop?: string;
  isDeploying?: boolean;
  isInspected?: boolean;
}): Promise<ReturnType<typeof bundleProcess>> => {
  await validateJSONSchemas();
  await cleanDevFolders();
  const currentBranchName = await getCurrentBranchName();
  const isCurrentBranchMain = currentBranchName === "main";
  const shouldDeploy = isCurrentBranchMain && options?.isDeploying;
  const shouldDevelop = !isCurrentBranchMain;
  const shouldNotProceed = !shouldDevelop && !shouldDeploy;
  if (shouldNotProceed) {
    throw new Error(
      `Developing on the main branch is not allowed. Please create a new branch.`,
    );
  }
  Logger.info(
    `${BUILD_SCRIPT_CONSTANTS.SEPARATOR} DEVELOPING ${BUILD_SCRIPT_CONSTANTS.SEPARATOR}`,
  );
  // the flag --skip-setup is used to skip the setup process
  const skipSetup = Deno.args.includes("--skip-setup") ||
    Deno.args.includes("-sk") || options?.skipSetup;
  // the flag --build is used to avoid watching the files
  const build = Deno.args.includes("--build") || Deno.args.includes("-b");
  const shouldWatch = options?.shouldWatch ?? !build;
  // the flag --process is used to specify the process to develop, f.i. --process=onboarding
  const processArg = Deno.args.find(
    (arg) => arg.startsWith("--process=") || arg.startsWith("-p="),
  );
  const processToDevelop = (processArg ? processArg.split("=")[1] : undefined) ||
    options?.processToDevelop;
  if (processToDevelop) {
    Logger.info(`Specified process "${processToDevelop}"`);
  }
  // the flag --stage is used to specify the stage to develop, f.i. --stage=staging
  const stageArg = Deno.args.find(
    (arg) => arg.startsWith("--stage=") || arg.startsWith("-st="),
  );
  const stageToDevelop = (stageArg ? stageArg.split("=")[1] : undefined) ||
    options?.stageToDevelop;
  if (stageToDevelop) {
    Logger.info(`Specified stage "${stageToDevelop}"`);
  }
  const result = await bundleProcess({
    watch: shouldWatch,
    skipSetup,
    specifiedProcessName: processToDevelop,
    specifiedStageName: stageToDevelop,
    isInspected: options?.isInspected,
  });
  if (!result) {
    throw new Error("Bundle failed, aborting development");
  }
  return result;
};

if (Deno.args.includes("--exec-develop")) {
  if (Deno.args.includes("--inspect")) {
    await develop({
      isInspected: true,
    });
  }
  await develop();
}
