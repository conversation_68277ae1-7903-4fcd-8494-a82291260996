import { walk } from "https://deno.land/std@0.220.1/fs/walk.ts";
import "@std/dotenv/load";
import { Logger } from "modules/logger/index.ts";

/**
 * This script generates the root CODEOWNERS file by traversing the entire repository and extracting rules from the CODEOWNERS files.
 */
export const fixCodeownersFile = async () => {
  const rootCodeownersPath = "CODEOWNERS";
  const content = [
    `# The codeowner of this repository is the Business Process Automation team (BPA).
# IMPORTANT: this file is automatically generated, do not manually edit it.
* @Ebury/BPA`,
  ];
  Logger.info(`Generating CODEOWNERS root file...`);
  for await (
    const entry of walk("src/", {
      skip: [/node_modules/, /google-cloud-sdk/],
      match: [/CODEOWNERS$/], // the '$' sign at the end of the regex means that the regex must match the entire file name, and not just a part of it. Without it, the regex would match any file that contains the word CODEOWNERS, even if it's not the only word in the file name.
    })
  ) {
    const isPathRootCodeowners = entry.path === rootCodeownersPath;
    if (isPathRootCodeowners) continue;
    const codeownersFileContent = await Deno.readTextFile(entry.path);
    const contentWithoutComments = codeownersFileContent
      .split("\n")
      .filter((line) => !line.startsWith("#"))
      .join(" ");
    const pathWithoutCodeowners = entry.path.replace("CODEOWNERS", "");
    content.push(`
# Rule extracted from ${entry.path}
${pathWithoutCodeowners}**/${contentWithoutComments}
`);
  }
  await Deno.writeTextFile(rootCodeownersPath, content.join("\n"));
  Logger.info(`CODEOWNERS file generated successfully!`);
};

if (Deno.args[0] === "generate") {
  await fixCodeownersFile();
}
