import { Ajv, type Plugin } from "npm:ajv@8.17.1";
import ajvFormats, { type FormatsPluginOptions } from "npm:ajv-formats@3.0.1";
import { join } from "https://deno.land/std@0.220.1/path/join.ts";
import { walk } from "https://deno.land/std@0.220.1/fs/walk.ts";
import { basename, dirname, relative } from "https://deno.land/std@0.220.1/path/mod.ts";
import { existsSync } from "https://deno.land/std@0.220.1/fs/exists.ts";
import processesPackageJSONSchema from "../schemas/packages/processes/package.json" with {
  type: "json",
};
import { listProcessNames } from "./utilities/list-process-names.ts";
import { Logger } from "modules/logger/index.ts";

// FIXME: https://github.com/ajv-validator/ajv-formats/issues/85#issuecomment-2377962689
const addFormats = ajvFormats as unknown as Plugin<FormatsPluginOptions>;

/**
 * This script is used to validate `json` files based on schemas in the `schemas` folder.
 * The `schemas` folder contains multiple `json` files, each one representing a schema.
 * The structure of the `schemas` folder corresponds to the structure of the `packages` folder, deeply nested.
 * For example, the `schemas` folder contains a `package.json` file in the `packages/processes` folder, which is the schema for the `package.json` file in the `packages/processes` folder.
 */
type SchemaInfo = {
  /**
   * The name of the file this schema is for.
   */
  schemaFor: string;
  /**
   * The path to the folder containing the schema file.
   * For example, the `package.json` schema is in the `packages/processes/package.json` file.
   * The folder path is `packages/processes`.
   */
  folderRelativePath: string;
  /**
   * The schema object.
   * This is the parsed schema object.
   * For example, the `package.json` schema is in the `packages/processes/package.json` file.
   * The schema object is the parsed `package.json` file.
   */
  schema: object;
};

/**
 * Validates json files based on schemas in the `schemas` folder.
 * The `schemas` folder contains multiple `json` files, each one representing a schema.
 * The structure of the `schemas` folder corresponds to the structure of the `packages` folder, deeply nested.
 * For example, the `schemas` folder contains a `package.json` file in the `packages/processes` folder, which is the schema for the `package.json` file in the `packages/processes` folder.
 */
export const validateJSONSchemas = async () => {
  try {
    Logger.info("Validating schemas...");
    Logger.info("Updating `relatedProcesses` list...");
    await updateRelatedProcessesList();
    // load the validator
    const ajv = new Ajv({
      allowMatchingProperties: true,
      formats: {
        // these are custom formats that are specified in the schema, but are not actually defined, see https://github.com/SchemaStore/schemastore/issues/2861#issuecomment-1514898902
        "match-pattern": true,
        "content-security-policy": true,
        "glob-pattern": true,
        "mime-type": true,
        permission: true,
      },
    });
    addFormats(ajv);
    // get the schemas
    const schemas: SchemaInfo[] = [];
    const schemasPath = join("src", "environment", "schemas");
    for await (
      const file of walk(schemasPath, {
        match: [/.*\.json$/],
      })
    ) {
      const schemaFor = basename(file.path);
      const folderRelativePath = relative(schemasPath, dirname(file.path));
      const schema = JSON.parse(await Deno.readTextFile(file.path)) as object;
      schemas.push({ schemaFor, folderRelativePath, schema });
    }
    // for each schema found, iterate through all the folders in the `relativeFolderPath` folder, and validate the file with the same name as the `schemaFor` property
    schemas.forEach((schema) => {
      const { schemaFor, folderRelativePath, schema: schemaObject } = schema;
      const folderPath = join("src", folderRelativePath);
      const subfiles = Deno.readDirSync(folderPath);
      for (const subfile of subfiles) {
        if (!subfile.isDirectory) {
          continue;
        }
        const fileToBeValidatedPath = join(
          folderPath,
          subfile.name,
          schemaFor,
        );
        const fileToBeValidatedExists = existsSync(fileToBeValidatedPath);
        if (!fileToBeValidatedExists) {
          return;
        }
        const fileToBeValidated = Deno.readFileSync(fileToBeValidatedPath);
        const fileToBeValidatedObject = JSON.parse(
          new TextDecoder().decode(fileToBeValidated),
        );
        const validate = ajv.compile(schemaObject);
        const isValid = validate(fileToBeValidatedObject);
        if (!isValid) {
          throw new Error([
            `The file ${fileToBeValidatedPath} is not valid.\n`,
            ...(validate.errors?.map((error) => `${error.instancePath} ${error.message}`.trim()) ?? []),
            "\n",
          ].join("\n"));
        }
        // Logger.success(`The file ${fileToBeValidatedPath} is valid.`);
      }
    });
    Logger.info("All schemas are valid!");
  } catch (error) {
    throw new Error(error.message);
  }
};

/**
 * Iterates through all the folders in the `packages` folder, and updates the `relatedProcesses` property in the JSON schema file for validation.
 */
const updateRelatedProcessesList = async () => {
  const processesFolderNames = await listProcessNames();
  // update the json schema found in `src/environment/schemas/packages/processes/package.json`
  const processesPackageJSONSchemaPath = join(
    Deno.cwd(),
    "/src/environment/schemas/packages/processes/package.json",
  );
  const isListUpdated = processesPackageJSONSchema.definitions.processNames.enum
    .sort()
    .join() === processesFolderNames.sort().join();
  if (isListUpdated) {
    return;
  }
  processesPackageJSONSchema.definitions.processNames.enum = processesFolderNames;
  await Deno.writeTextFile(
    processesPackageJSONSchemaPath,
    JSON.stringify(processesPackageJSONSchema, null, 2),
  );
};

if (Deno.args[0] === "validate") {
  await validateJSONSchemas();
}
