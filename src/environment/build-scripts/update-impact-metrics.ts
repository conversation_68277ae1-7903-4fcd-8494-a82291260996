import { Impersonator } from "modules/cloud-run-impersonator/index.ts";
import { listProcessNames } from "./utilities/list-process-names.ts";
import { getCommandOutput } from "utilities/get-command-output.ts";
import { fetchAutomationCredentials } from "utilities/fetchCredentials.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * This script is used to update the impact metrics in the metrics sheet.
 * Metrics sheet url https://docs.google.com/spreadsheets/d/1-o83xrYpHUUr54IyRvEpfTmJ9Y2IH7cFTi1ISkCGQAs/edit?authuser=<EMAIL>&gid=1806225217#gid=1806225217.
 * It should be run automatically only on push commits to the main branch.
 */

type ManualWorkSaving = {
  type: "manualWorkSaving";
  description: string;
  hours: number;
  costPerHour: number;
};
type ThirdPartyServicesSaving = {
  type: "thirdPartyServicesSaving";
  description: string;
  serviceCost: number;
  paymentsPerYear: number;
};
type RiskSaving = {
  type: "risk";
  kind: "fine" | "breach";
  description: string;
  costPerFailure: number;
  estimatedTimesPerYear: number;
};
type Metric = ManualWorkSaving | ThirdPartyServicesSaving | RiskSaving;
type Process = {
  name: string;
  impact: Metric[];
};
type MetricType = Metric["type"];

const TableHeaders = [
  "process",
  "type",
  "description",
  "base",
  "multiplier",
  // adds an ArrayFormula to automatically calculate the total savings
  `={"total";ARRAYFORMULA(if((D2:D*E2:E)>0,D2:D*E2:E,""))}`,
] as const;
type MetricsRow = [
  string,
  MetricType,
  string,
  number,
  number,
];
type MetricsTable = [typeof TableHeaders, ...MetricsRow[]];

Logger.info(`Updating impact metrics...`);
const processNames = await listProcessNames();
Logger.debug(`Collecting impact metrics for ${processNames.length} processes...`);
const metrics: MetricsRow[] = (await Promise.all(processNames.map(async (processName) => {
  const processPath = new URL(`../../../src/packages/processes/${processName}/package.json`, import.meta.url).pathname;
  const process = (await import(processPath, { with: { type: "json" } })) as { default: Process };
  const impact = process.default.impact || [];
  return impact
    .map((impact) => {
      switch (impact.type) {
        case "manualWorkSaving":
          return [
            processName,
            impact.type,
            impact.description,
            impact.hours,
            impact.costPerHour,
          ] as MetricsRow;
        case "thirdPartyServicesSaving":
          return [
            processName,
            impact.type,
            impact.description,
            impact.serviceCost,
            impact.paymentsPerYear,
          ] as MetricsRow;
        case "risk":
          return [
            processName,
            impact.type,
            impact.description,
            impact.costPerFailure,
            impact.estimatedTimesPerYear,
          ] as MetricsRow;
      }
    });
})))
  .flat();
const metricsLength = metrics.length;
Logger.debug(`Collected ${metricsLength} metrics...`);
const table: MetricsTable = [
  TableHeaders,
  ...metrics,
];
const credentials = await fetchAutomationCredentials();

const impersonator = new Impersonator({
  scopes: [
    "https://www.googleapis.com/auth/drive",
  ],
  subject: "<EMAIL>",
  credentials,
});
const sheets = impersonator.getSheetsClient();
const spreadsheetId = "1-o83xrYpHUUr54IyRvEpfTmJ9Y2IH7cFTi1ISkCGQAs";
const sheetName = "currentMetrics";
Logger.info(`Updating ${sheetName} with ${metricsLength} rows...`);
await sheets.spreadsheets.values.clear({
  spreadsheetId,
  range: `${sheetName}!A:ZZ`,
});
await sheets.spreadsheets.values.update({
  spreadsheetId,
  range: `${sheetName}!1:${table.length}`,
  valueInputOption: "USER_ENTERED",
  requestBody: {
    values: table,
  },
});
Logger.info(`Updated ${sheetName} with ${metricsLength} rows.`);
Logger.info(`Updating history sheet...`);
const summary: {
  [key in MetricType]: number;
} = metrics.reduce((summary, metric) => {
  const type = metric[1] as MetricType;
  const value = metric[3] * metric[4];
  summary[type] += value;
  return summary;
}, {
  manualWorkSaving: 0,
  thirdPartyServicesSaving: 0,
  risk: 0,
});
const currentDate = new Date();
const currentTime = `${currentDate.toDateString()} ${currentDate.toLocaleTimeString()}`;
const latestCommitMessage = (await getCommandOutput({
  commandParts: ["git", "log", "-1", "--pretty=%B"],
}))?.trim()?.split("\n")[0] || "";
/**
 * Usually the latest commit message (if made on main, it's only through PR) contains the PR number in the format (#{prNumber}), so we can try to extract it.
 */
const prNumber = latestCommitMessage.match(/\(#(\d+)\)/)?.[1];
const reference = prNumber ? `=HYPERLINK("https://github.com/Ebury/business-process-automation/pull/${prNumber}","${latestCommitMessage}")` : latestCommitMessage;
const historyTable = [
  [
    currentTime,
    summary.manualWorkSaving,
    summary.thirdPartyServicesSaving,
    summary.risk,
    summary.manualWorkSaving + summary.thirdPartyServicesSaving + summary.risk,
    reference,
  ],
];
const historySheetName = "history";
await sheets.spreadsheets.values.append({
  spreadsheetId,
  range: `${historySheetName}!A:Z`,
  valueInputOption: "USER_ENTERED",
  requestBody: {
    values: historyTable,
  },
});
Logger.info(`Updated history sheet.`);
Logger.info(`Finished updating impact metrics.`);
