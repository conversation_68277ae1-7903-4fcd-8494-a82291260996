import { getCommandOutput } from "utilities/get-command-output.ts";
import { getCurrentBranchName } from "utilities/get-current-branch-name.ts";
import { Logger } from "modules/logger/index.ts";

export const getChangedFileNames = async () => {
  const currentBranchName = await getCurrentBranchName();
  const diffResultRaw = await getCommandOutput({ commandParts: ["git", "diff", "--name-only", `origin/main..${currentBranchName}`, "--diff-filter=d"] });
  const diffResult = diffResultRaw.trim();
  const changedFiles = diffResult.split("\n");
  Logger.debug({ changedFiles });
  return changedFiles;
};
