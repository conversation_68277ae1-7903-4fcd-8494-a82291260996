import { Logger } from "modules/logger/index.ts";
import { listProcessNames } from "../utilities/list-process-names.ts";
import { getChangedFileNames } from "./get-changed-file-names.ts";

export const getChangedProcessFolders = async () => {
  const PROCESS_FOLDER_BASE_PATH = "src/packages/processes/";
  const files = await getChangedFileNames();
  const filesInProcessFolders = files
    .filter((filePath) =>
      filePath
        .startsWith(PROCESS_FOLDER_BASE_PATH)
    )
    // filter out files, keep only folders
    .filter((filePath) =>
      filePath
        .split(PROCESS_FOLDER_BASE_PATH)[1]
        ?.includes("/")
    );
  const processesPaths = filesInProcessFolders
    .map((filePath) => filePath.split(PROCESS_FOLDER_BASE_PATH)[1].split("/")[0]);
  const uniqueProcessFolders = Array.from(
    new Set(processesPaths),
  );
  Logger.debug({ uniqueProcessFolders });
  const existingProcessFolders = await listProcessNames();
  const existingChangedProcessFolders = uniqueProcessFolders
    .filter((processName) => existingProcessFolders.includes(processName));
  Logger.debug(`Changed process folders: ${existingChangedProcessFolders}`);
  return existingChangedProcessFolders;
};
