import * as core from "npm:@actions/core";
import { SimpleGit, simpleGit, SimpleGitOptions } from "npm:simple-git";

const options: Partial<SimpleGitOptions> = {
  baseDir: Deno.cwd(),
  binary: "git",
  maxConcurrentProcesses: 6,
  trimmed: false,
};

export const commitDetectedChanges = async () => {
  const git: SimpleGit = simpleGit(options);
  const status = await git.status();
  const hasChanges = status.files.length > 0;
  if (hasChanges) {
    await git.addConfig(
      "user.name",
      "BPA Continuous Integration",
      true,
      "global",
    );
    await git.add(".");
    const commit = await git.commit("Commit CI changes");
    const commitSha = commit.commit;
    await git.push();
    console.log(`Committed changes with commit SHA: ${commitSha}`);
    core.setOutput("commit-sha", commitSha);
  }
};

if (Deno.args[0] === "commit-changes") {
  await commitDetectedChanges();
}
