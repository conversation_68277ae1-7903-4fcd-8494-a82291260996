import { bundleGAS } from "./bundle/bundle-gas.ts";
// import { bundleChromeExtension } from "./bundle/bundle-chrome-extension.ts";
import { bundleCloudRun } from "./bundle/bundle-cloud-run.ts";
import { BundleScripts } from "./bundle/types.ts";
import { chooseProcessName } from "./utilities/chooseProcessName.ts";
import { chooseStage } from "./utilities/chooseStage.ts";
import { getProcessPackageJson } from "./utilities/getProcessPackageJson.ts";
import { Logger } from "modules/logger/index.ts";

export const bundleProcess = async (options: {
  watch: boolean;
  skipSetup?: boolean;
  specifiedProcessName?: string;
  specifiedStageName?: string;
  isInspected?: boolean;
}): Promise<BundleScripts.GasBundleResult | BundleScripts.CloudRunResult | boolean> => {
  const {
    watch,
    specifiedProcessName,
    specifiedStageName,
    isInspected,
  } = options;
  const processName = await chooseProcessName(specifiedProcessName);
  const { packageJson, processFolderPath } = await getProcessPackageJson(processName);
  const platform = packageJson
    .platform as unknown as (typeof BundleScripts.SUPPORTED_PLATFORMS)[
      number
    ];
  const isPlatformSupported = BundleScripts.PLATFORMS_AVAILABLE_TO_CHOOSE
    .includes(
      platform as (typeof BundleScripts.PLATFORMS_AVAILABLE_TO_CHOOSE)[
        number
      ],
    );
  if (!isPlatformSupported) {
    Logger.warning(`Platform "${platform}" is not supported`);
    return true;
  }
  Logger.info(`Platform: "${platform}"`);

  const stageName = await chooseStage(
    packageJson as BundleScripts.CloudRunProcessJSONTemplate,
    specifiedStageName,
  ) as "local" | "development" | "staging" | "production";
  const stage = packageJson.stages[stageName as keyof typeof packageJson.stages];

  let result: BundleScripts.GasBundleResult | BundleScripts.CloudRunResult | boolean = false;
  if (platform === "gas") {
    const gasProcessPackageJSON = packageJson as BundleScripts.GasProcessJSONTemplate;
    await bundleGAS({
      processFolderPath,
      workingFiles: gasProcessPackageJSON.workingFiles,
      watch,
      scriptURL: stage.url,
    });
    const gasBundleResult: BundleScripts.GasBundleResult = {
      processName: processName,
      runtime: "gas",
      platform: platform,
      stage: stageName,
      scriptURL: stage.url,
    };
    result = gasBundleResult;
  }
  if (platform === "chrome-extension") {
    Logger.info(`Currently chrome extensions are not supported.`);
    result = true;
    // Removed this implementation as we don't have active chrome extensions yet.
    // Leaving here the reference an almost-working implementation (should replace the parcel bundling with someting similar to gas bundling)
    // https://github.com/Ebury/business-process-automation/blob/73407205ef494384f8f3a53a8fac052078faf26b/src/environment/build-scripts/bundle/bundle-chrome-extension.ts
    // await bundleChromeExtension({
    //   processFolderPath: chosenProcessFolderPath,
    //   watch,
    //   stageName,
    // });
  }
  if (platform === "cloud-run") {
    const cloudRunProcessJsonTemplate = packageJson as BundleScripts.CloudRunProcessJSONTemplate;
    await bundleCloudRun({
      watch,
      runtime: cloudRunProcessJsonTemplate.runtime,
      processName,
      processFolderPath,
      stageName,
      isInspected,
    });
    result = {
      processName,
      runtime: cloudRunProcessJsonTemplate.runtime,
      platform,
      stage: stageName,
    } as BundleScripts.CloudRunResult;
  }
  return result;
};
