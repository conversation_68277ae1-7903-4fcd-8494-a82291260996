import { script_v1 } from "googleapis";
import { Impersonator } from "../../../packages/modules/cloud-run-impersonator/index.ts";
import { fetchAutomationCredentials } from "utilities/fetchCredentials.ts";
import { Logger } from "modules/logger/index.ts";

export const pushToGas = async (
  scriptURL: string,
) => {
  const scriptId = scriptURL.split("https://script.google.com/home/<USER>/")[1].split(
    "/",
  )[0];
  Logger.info(`Pushing to GAS...`);
  const fileContents: script_v1.Schema$File[] = [];
  const basePath = new URL("../../../../dist/", import.meta.url).pathname;
  for await (const file of Deno.readDir(basePath)) {
    if (file.isDirectory) continue;
    const source = await Deno.readTextFile(
      new URL(`${basePath}/${file.name}`, import.meta.url)
        .pathname,
    );
    if (file.name === "appsscript.json") {
      fileContents.push({
        name: `appsscript`,
        type: "JSON",
        source,
      });
      continue;
    }
    if (file.name.endsWith(".html")) {
      fileContents.push({
        name: file.name.replace(".html", ""),
        type: "HTML",
        source,
      });
      continue;
    }
    fileContents.push({
      name: file.name,
      type: "SERVER_JS",
      source,
    });
  }
  const credentials = await fetchAutomationCredentials();

  const impersonator = new Impersonator({
    scopes: [
      "https://www.googleapis.com/auth/script.projects",
    ],
    subject: "<EMAIL>",
    credentials,
  });
  const appsScriptclient = impersonator.getAppsScriptClient();
  const result = await appsScriptclient.projects.updateContent({
    scriptId,
    requestBody: {
      files: fileContents,
    },
  });
  const isSuccessful = result.status === 200;
  if (!isSuccessful) {
    throw new Error(JSON.stringify(result));
  }
  Logger.info(`Successfully pushed to GAS:
        ${scriptURL}`);
  return;
};
