import { ProcessSchema } from "types/process-schema.ts";
import { executeCommand } from "../../utilities/execute-command.ts";
import { BundleScripts } from "./types.ts";
import { copy } from "https://deno.land/std@0.224.0/fs/copy.ts";
import { getEnvVarsAndSecrets } from "utilities/get-env-vars-and-secrets.ts";
import { Logger } from "modules/logger/index.ts";

const SUPPORTED_CLOUD_RUN_RUNTIMES = ["deno"];

export const bundleCloudRun = async (options: {
  watch: boolean;
  runtime: BundleScripts.CloudRunProcessJSONTemplate["runtime"];
  processName: string;
  processFolderPath: string;
  stageName: ProcessSchema.StageName;
  isInspected?: boolean;
}) => {
  const { watch, runtime, processName, processFolderPath, stageName, isInspected } = options;
  const isCloudRunRuntimeSupported = SUPPORTED_CLOUD_RUN_RUNTIMES.includes(
    runtime,
  );
  if (!isCloudRunRuntimeSupported) {
    throw new Error(
      `Runtime "${runtime}" is not supported. Supported runtime options: ${
        SUPPORTED_CLOUD_RUN_RUNTIMES.join(
          ",",
        )
      }.`,
    );
  }
  Logger.info(`Runtime: ${runtime}`);
  await bundleDeno({
    watch,
    processName,
    processFolderPath,
    stageName: stageName,
    isInspected,
  });
};

const bundleDeno = async (options: {
  watch: boolean;
  processName: string;
  processFolderPath: string;
  stageName: ProcessSchema.StageName;
  isInspected?: boolean;
}) => {
  const { watch, processName, processFolderPath, stageName, isInspected } = options;
  Logger.info({ watch });
  if (watch) {
    await watchDeno({ processFolderPath, stageName, isInspected });
    return true;
  }
  Logger.info("Running the 'deno' runtime in the build mode.");
  Logger.info(
    `Copying all the files from the cloud-run template folder.`,
  );
  const templateFolder = new URL(
    "../../../templates/process/cloud-run",
    import.meta.url,
  );
  await copy(templateFolder, "dist", { overwrite: true });
  Logger.info("Copied successfully.");
  Logger.info(
    "Copying all the files from the process folder. This will replace the template ones in case additional customization is needed.",
  );
  await copy(processFolderPath, "dist", { overwrite: true });
  Logger.info("Copied successfully.");
  Logger.info("compiling src/index.ts");
  const entryPoint = new URL(
    `../../../../src/packages/processes/${processName}/src/index.ts`,
    import.meta.url,
  )
    .pathname;
  await executeCommand({
    commandParts: [
      "./deno.sh",
      "compile",
      "--output=./dist/compiled",
      entryPoint,
    ],
  });
  Logger.info("Compiled successfully.");
  return {
    platform: "cloud-run",
    runtime: "deno",
    processName,
  };
};

const watchDeno = async (options: {
  processFolderPath: string;
  stageName: ProcessSchema.StageName;
  isInspected?: boolean;
}) => {
  const { processFolderPath, stageName, isInspected } = options;
  const processFolderAbsolutePath = new URL(
    `../../../../${processFolderPath}`,
    import.meta.url,
  ).pathname;
  Logger.info({ processFolderAbsolutePath });
  Logger.info(`Watching for changes in ${processFolderAbsolutePath}...`);
  const spawnProcess = async () => {
    const env = await getEnvVarsAndSecrets({ processFolderAbsolutePath, stageName });
    const args = [
      "run",
      "--config=../../../../deno_settings.json",
      "--allow-all",
      "--node-modules-dir",
      "src/index.ts",
    ];
    if (isInspected) {
      args.splice(1, 0, "--inspect-brk");
    }
    const executedCommand = new Deno.Command(Deno.execPath(), {
      args: args,
      cwd: processFolderAbsolutePath,
      env,
      clearEnv: false,
      stderr: "inherit",
      stdout: "inherit",
    });

    const child = executedCommand.spawn();
    return child;
  };
  let child = await spawnProcess();
  const processFolderWatcher = Deno.watchFs(
    new URL(processFolderAbsolutePath, import.meta.url)
      .pathname,
  );
  for await (const event of processFolderWatcher) {
    const isEventModify = event.kind === "modify";
    if (!isEventModify) continue;
    child?.kill(); // kill the old process if it exists
    Logger.info(`Restarting...`);
    child = await spawnProcess(); // spawn a new process so that changed files will be picked up, in particular env vars
  }
  return true;
};
