//@deno-types="npm:@types/google-apps-script@1.0.83";
// The following code is injected in order to add functionality to the built-in Google Apps Script methods. The name of the function has been suffixed with an underscore as in this way it won't be directly callable by the GAS editor.
let callCounter = 0;
const logUrlFetchAppCall_ = (fetchURL: string) => {
  const scriptId = ScriptApp.getScriptId();
  const projectURL = "https://script.google.com/home/<USER>/" +
    scriptId +
    "/edit?authuser=<EMAIL>";
  // a separator is needed in order to be able to split the logs later with regex
  const separator = "|||";
  console.log(
    `UrlFetchAppCall${separator}projectURL${projectURL}${separator}fetchURL${fetchURL}`,
  );
  callCounter++;
  console.log(`UrlFetchApp calls: ${callCounter}`);
  // the regex used to split the logs is: /UrlFetchAppCall\|\|\|projectURL(.*)\|\|\|fetchURL(.*)/
};
const originalUrlFetch = UrlFetchApp.fetch;
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore this is made on purpose in order to be able to overload the method
UrlFetchApp.fetch = (url, options) => {
  logUrlFetchAppCall_(url);
  return originalUrlFetch(url, options);
};
const originalUrlFetchAll = UrlFetchApp.fetchAll;
UrlFetchApp.fetchAll = (
  requests: GoogleAppsScript.URL_Fetch.URLFetchRequest[],
) => {
  requests.forEach((request) => {
    logUrlFetchAppCall_(request.url);
  });
  return originalUrlFetchAll(requests);
};
/**
 * This function is used to test the authorization of all the services that are used in the project.
 * It is added to any project to standardize the way the authorization is tested without needing to add such a function to every project.
 */
const testAuthorizationFunction = () => {
  console.log("Running authorization function");
};
