import { type ExportNamedDeclaration, type Identifier, Parser, type VariableDeclaration } from "npm:acorn@8.12.1";
import { full } from "npm:acorn-walk@8.3.3";
import { Logger } from "modules/logger/index.ts";

export const extractExportedMethods = async (options: {
  entryPointPath: string;
}) => {
  const { entryPointPath } = options;
  const exportedMethods: string[] = [];
  const parsedEntryPoint = Parser.parse(
    await Deno.readTextFile(entryPointPath),
    {
      ecmaVersion: "latest",
      sourceType: "module",
    },
  );
  full(parsedEntryPoint, (node) => {
    const isNodeExport = node.type === "ExportNamedDeclaration";
    if (!isNodeExport) return;
    const exportNamedDeclaration = node as ExportNamedDeclaration;
    const isVariableDeclaration = exportNamedDeclaration.declaration?.type === "VariableDeclaration";
    if (isVariableDeclaration) {
      const variableDeclaration = exportNamedDeclaration
        .declaration as VariableDeclaration;
      variableDeclaration.declarations?.forEach((declaration) => {
        const isDeclarationIdentifier = declaration.id.type === "Identifier";
        if (!isDeclarationIdentifier) return;
        exportedMethods.push((declaration.id as Identifier).name);
      });
    }
    exportNamedDeclaration.specifiers?.forEach((specifier) => {
      const isSpecifierIdentifier = specifier.local.type === "Identifier";
      if (!isSpecifierIdentifier) return;
      exportedMethods.push((specifier.local as Identifier).name);
    });
  });
  Logger.debug({ exportedMethods });
  return exportedMethods;
};
