// deno-lint-ignore-file no-namespace
import cloudRunProcessJsonTemplate_ from "../../../templates/process/cloud-run/package.json" with {
  type: "json",
};
import processJsonSchema_ from "../../schemas/packages/processes/package.json" with {
  type: "json",
};
import makeProcessJsonTemplate_ from "../../../templates/process/make/package.json" with {
  type: "json",
};
import gasProcessJsonTemplate_ from "../../../templates/process/gas/package.json" with {
  type: "json",
};

export namespace BundleScripts {
  export const cloudRunProcessJsonTemplate = cloudRunProcessJsonTemplate_;
  export const processJsonSchema = processJsonSchema_;
  export const makeProcessJsonTemplate = makeProcessJsonTemplate_;
  export const gasProcessJsonTemplate = gasProcessJsonTemplate_;
  export type GasProcessJSONTemplate = typeof gasProcessJsonTemplate;
  export type MakeProcessJSONTemplate = typeof makeProcessJsonTemplate;
  export type CloudRunProcessJSONTemplate = typeof cloudRunProcessJsonTemplate;
  export type ProcessJSONTemplate =
    | GasProcessJSONTemplate
    | MakeProcessJSONTemplate
    | CloudRunProcessJSONTemplate;
  export const SUPPORTED_PLATFORMS = processJsonSchema.definitions
    .supportedPlatforms
    .enum as [
      "gas",
      "cloud-run",
      "make",
      "chrome-extension",
    ];
  export const PLATFORMS_AVAILABLE_TO_CHOOSE = [
    "gas",
    "chrome-extension",
    "cloud-run",
  ] as const;
  export const SUPPORTED_CLOUD_RUN_RUNTIMES = ["deno", "gas"];
  export type BundleResult = {
    processName: string;
    runtime: (typeof BundleScripts.SUPPORTED_CLOUD_RUN_RUNTIMES)[number];
    platform: (typeof BundleScripts.SUPPORTED_PLATFORMS)[number];
    stage: keyof typeof BundleScripts.cloudRunProcessJsonTemplate["stages"];
  };

  export type GasBundleResult = BundleResult & {
    platform: "gas";
    runtime: "gas";
    scriptURL: string;
  };
  export type CloudRunResult = BundleResult & {
    platform: "cloud-run";
    runtime: (typeof BundleScripts.SUPPORTED_CLOUD_RUN_RUNTIMES)[number];
  };
}
