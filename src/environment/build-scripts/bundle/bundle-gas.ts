import * as prompts from "npm:prompts";
import { exists } from "https://deno.land/std@0.224.0/fs/exists.ts";
import { pushToGas } from "./push-to-gas.ts";
import { ensureDir } from "https://deno.land/std@0.224.0/fs/ensure_dir.ts";
import { bundle } from "jsr:@deno/emit";
import { fmt } from "https://deno.land/x/deno_fmt@0.1.5/mod.ts";
import esbuild from "esbuild";
import userName from "npm:git-user-name@2.0.0";
import htmlPlugin from "npm:@chialab/esbuild-plugin-html@0.19.0-beta.3";
import graphqlLoaderPluginPkg from "npm:@luckycatfactory/esbuild-graphql-loader@3.8.1";
import { denoPlugins } from "jsr:@luca/esbuild-deno-loader@^0.11.0";
const graphqlLoaderPlugin = graphqlLoaderPluginPkg.default;
import DynamicImport from "npm:@rtvision/esbuild-dynamic-import";
import { extractExportedMethods } from "./bundle-gas/extract-exported-methods.ts";
import { Logger } from "modules/logger/index.ts";

export const bundleGAS = async (options: {
  processFolderPath: string;
  workingFiles: string[];
  watch: boolean;
  scriptURL: string;
}) => {
  const { processFolderPath, workingFiles, watch, scriptURL } = options;
  const workingFilesPaths = await validateWorkingFiles({
    processFolderPath,
    workingFiles,
  });
  await ensureDir("dist");
  if (watch) {
    Logger.info(`Watching for changes in ${processFolderPath}...`);
    const processFolderWatcher = Deno.watchFs(
      new URL(`../../../../${processFolderPath}`, import.meta.url)
        .pathname,
    );
    for await (const event of processFolderWatcher) {
      const isEventModify = event.kind === "modify";
      if (!isEventModify) continue;
      await updateServer({
        workingFilesPaths,
        scriptURL,
        isChange: false,
      });
    }
    return true;
  }
  await updateServer({ workingFilesPaths, scriptURL, isChange: false });
  return true;
};

const updateServer = async (options: {
  workingFilesPaths: string[];
  scriptURL: string;
  isChange: boolean;
}) => {
  const { workingFilesPaths, scriptURL, isChange } = options;
  Logger.info(
    isChange ? `Detected change. Rebuilding...` : `Bundling GAS project...`,
  );
  for (const workingFilePath of workingFilesPaths) {
    await toDist(workingFilePath);
  }
  Logger.info(`Bundle successful!`);
  const isCI = Deno.env.get("GITHUB_ACTIONS") === "true";
  if (isCI) {
    Logger.info(`Running on CI, skipping push to GAS.`);
    return;
  }
  await pushToGas(scriptURL);
};

const toDist = async (filePath: string) => {
  const fileName = filePath.split("/").pop()!;
  const isHTML = filePath.endsWith(".html");
  if (isHTML) {
    const bundle = await esbuild.build({
      entryPoints: [filePath],
      bundle: true,
      write: false,
      outdir: "public",
      chunkNames: "[name]",
      plugins: [htmlPlugin()],
    });
    let fileContent = bundle.outputFiles.find((output) => output.path.endsWith(".html"))?.text as string;
    for (const output of bundle.outputFiles) {
      if (output.path.endsWith(".html")) continue;
      const importName = output.path.split("/").pop();
      const isCss = output.path.endsWith(".css");
      if (isCss) {
        fileContent = fileContent.replace(
          `@import '${importName}'`,
          output.text,
        );
        continue;
      }
      fileContent = fileContent.replace(
        `import './${importName}'`,
        output.text,
      );
    }
    await esbuild.stop();
    await Deno.writeTextFile(`dist/${fileName}`, fileContent);
    return;
  }
  const isScript = filePath.endsWith(".ts") || filePath.endsWith(".js");
  if (!isScript) {
    await Deno.copyFile(filePath, `dist/${fileName}`);
    return;
  }
  const entryPointPath = new URL(`../../../../${filePath}`, import.meta.url).pathname;
  const exportedMethods = await extractExportedMethods({ entryPointPath });
  const exportedMethodsString = exportedMethods
    .map((methodName) => `function ${methodName}(){}`)
    .join("\n");
  const bundledCode = (await esbuild.build({
    entryPoints: [entryPointPath],
    target: "es2020",
    bundle: true,
    write: false,
    format: "iife",
    plugins: [
      graphqlLoaderPlugin(),
      DynamicImport({
        transformExtensions: [".html"],
      }),
      ...denoPlugins({
        configPath: new URL("../../../../deno_settings.json", import.meta.url).pathname,
      }),
    ],
    loader: {
      ".html": "text",
    },
  }))
    .outputFiles
    .map((file) => file.text)
    .join("\n");
  await esbuild.stop();
  const bundleWithExportedMethods = bundledCode.replace(
    `})()`,
    "\n// Here we are adding the exported methods to the globalThis object in order to make them callable by GAS. These functions are associated with the exported methods in the top of this file.\n" +
      exportedMethods
        .map((method) => `globalThis.${method} = ${method}`)
        .join("\n") +
      `})()`,
  );
  const timeString = new Date().toUTCString();
  const overloadedMethodsBundle = await bundle(
    new URL(`./bundle-gas/overloaded-gas-methods.ts`, import.meta.url),
  );
  const completeBundle = `// Updated at ${timeString} by ${userName()}

// The following contents are the result of our development pipeline, do not edit the contents directly.

//--------------------EXPORTED FUNCTIONS--------------------
// In the next line there should be the functions that are exported from the bundle (and thus callable by GAS).
// If you don't see any, expose them by exporting them in the "backend.ts" file.
function testAuthorization(){}
${exportedMethodsString}

//--------------------OVERLOADED METHODS--------------------
${overloadedMethodsBundle.code}

//--------------------BUNDLED CONTENTS--------------------
// The following code is the result of the bundling of the "backend.ts" file. Do not edit it directly.
${bundleWithExportedMethods}
`;
  const fileNameJS = fileName.replace(".ts", ".js");
  const formattedBundle = await fmt.format(completeBundle);
  await Deno.writeTextFile(`dist/${fileNameJS}`, formattedBundle, {
    create: true,
  });
};

const validateWorkingFiles = async (parameters: {
  processFolderPath: string;
  workingFiles: string[];
}) => {
  Logger.info("Validating working files");
  // check if the working files are present in the process folder
  const { processFolderPath, workingFiles } = parameters;
  const processAbsolutePath = new URL(
    `../../../../src/packages/processes/${processFolderPath}`,
    import.meta.url,
  ).pathname;
  const workingFilesPaths = workingFiles.map((workingFile) => `${processFolderPath}/${workingFile}`);
  const missingWorkingFiles = workingFilesPaths.filter(
    (workingFilePath) => !exists(workingFilePath),
  );
  if (missingWorkingFiles.length > 0) {
    // if they are not present, prompt the user to create them
    const { createWorkingFiles } = await prompts({
      type: "confirm",
      name: "createWorkingFiles",
      message: `The following files are missing in ${processAbsolutePath}\n${
        Logger.warning(
          missingWorkingFiles.join("\n"),
        )
      }\nCreate them now?`,
      initial: true,
    });
    // if the user doesn't want to create them, exit
    if (!createWorkingFiles) {
      throw new Error(
        `Missing working files: ${missingWorkingFiles.join(", ")}.`,
      );
    }
    // create the working files
    const workingFilesPromises = missingWorkingFiles.map((workingFile) => Deno.writeTextFile(`${processFolderPath}/${workingFile}`, ""));
    await Promise.all(workingFilesPromises);
    Logger.info([
      `Created working files in ${processAbsolutePath}`,
      ...missingWorkingFiles,
    ]);
  }
  Logger.info("Working files are present");
  return workingFilesPaths;
};
