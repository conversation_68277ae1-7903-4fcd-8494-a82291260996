import { ProcessSchema } from "types/process-schema.ts";
import * as mod from "https://deno.land/std@0.224.0/path/mod.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Iterates through the package.json files in the processes directory and checks
 * if the process names are duplicated. If a process name is duplicated, the
 * package.json file path is logged as an error message.
 */
async function checkAllPackageJSONS() {
  const processesDirectory = `${Deno.cwd()}/src/packages/processes`;

  const packagesChecked = await checkPackageJsonsForDuplicates(processesDirectory);

  Logger.info(`${packagesChecked.size} package.json files checked`);

  packagesChecked.forEach((value, key) => {
    if (value.messages.length > 0) {
      console.group(`Found duplicates for ${key} in the following package jsons:`);
      Logger.error(value.messages);
      console.groupEnd();
    }
  });

  Logger.info("Finished checking package.json files");
}

/**
 * Iterates through the package.json files in the given directory and checks if
 * the process names are duplicated. If a process name is duplicated, the
 * package.json file path is added to the messages array.
 * @param base Directory to start the search from
 * @returns A Map where the key is the process name and the value is an object
 * with a messages property containing an array of the package.json file paths
 * that have the same process name
 */
async function checkPackageJsonsForDuplicates(base: string) {
  let packageInformationChecked = new Map<string, { messages: string[] }>();
  for await (const dirEntry of Deno.readDir(base)) {
    const newBase = mod.join(base, dirEntry.name);
    if (dirEntry.isDirectory) {
      const newBasePackageJson = mod.join(newBase, "package.json");
      packageInformationChecked = await checkForDuplicatesInFiles(newBasePackageJson, packageInformationChecked);
    }

    if (dirEntry.isFile) {
      continue;
    }
  }

  return packageInformationChecked;
}

/**
 * Iterates through the given package.json file and checks if the process name,
 * url and endpointUrl are duplicated. If any of them are duplicated, the
 * package.json file path is added to the messages array.
 * @param packageJsonToCheck The path to the package.json file to check
 * @param packageInformationChecked A Map where the key is the process name/url/endpointUrl and the value is an object
 * with a messages property containing an array of the package.json file paths
 * that have the same process name/url/endpointUrl
 * @returns The same Map as the one given as a parameter, but with the new
 * information added to it
 */
async function checkForDuplicatesInFiles(packageJsonToCheck: string, packageInformationChecked: Map<string, { messages: string[] }>) {
  const packageJsonToCheckContents = await Deno.readTextFile(packageJsonToCheck);
  const packageJsonInfo = JSON.parse(packageJsonToCheckContents) as ProcessSchema.Base;
  const stages = packageJsonInfo.stages;
  const processName = packageJsonInfo.name;

  //Checking if process name is duplicated
  const isTheProcessNameDuplicated = isTheStringDuplicated(processName, packageInformationChecked);

  if (isTheProcessNameDuplicated) {
    const oldMessages = packageInformationChecked.get(processName)!.messages;
    oldMessages.push(packageJsonToCheck);
    packageInformationChecked.set(processName, {
      messages: oldMessages,
    });
  } else {
    packageInformationChecked.set(processName, { messages: [] });
  }

  for (const stage in stages) {
    const url = stages[stage as keyof typeof stages]?.url;
    if (url) {
      const urlIsLocalhost = url.includes("localhost");
      if (urlIsLocalhost) {
        continue;
      }

      const isUrlDuplicated = isTheStringDuplicated(url, packageInformationChecked);

      if (isUrlDuplicated) {
        const oldMessages = packageInformationChecked.get(url)!.messages;
        oldMessages.push(packageJsonToCheck);
        packageInformationChecked.set(url, {
          messages: oldMessages,
        });
      } else {
        packageInformationChecked.set(url, { messages: [] });
      }
    }
    const endpointUrl = stages[stage as keyof typeof stages]?.endpointUrl;
    if (endpointUrl) {
      const endPointUrlIsLocalhost = endpointUrl.includes("localhost");
      if (endPointUrlIsLocalhost) {
        continue;
      }

      const isEndPointURLDuplicated = isTheStringDuplicated(endpointUrl, packageInformationChecked);

      if (isEndPointURLDuplicated) {
        const oldMessages = packageInformationChecked.get(endpointUrl)!.messages;
        oldMessages.push(packageJsonToCheck);
        packageInformationChecked.set(endpointUrl, {
          messages: oldMessages,
        });
      } else {
        packageInformationChecked.set(endpointUrl, { messages: [] });
      }
    }
  }

  return packageInformationChecked;
}

/**
 * Checks if the given string is already present in the Map
 * @param stringToCheckDorDuplicates the string to check
 * @param packageInformationChecked the Map to check against
 * @returns true if the string is already present, false otherwise
 */
function isTheStringDuplicated(stringToCheckDorDuplicates: string, packageInformationChecked: Map<string, { messages: string[] }>) {
  const isTheStringALreadyInMap = packageInformationChecked.has(stringToCheckDorDuplicates);
  if (isTheStringALreadyInMap) {
    return true;
  }

  return false;
}

if (Deno.args.includes("--validatePackages")) {
  await checkAllPackageJSONS();
}
