{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Process Schema", "description": "JSON Schema definition for BPA process", "type": "object", "properties": {"name": {"$ref": "#/definitions/processNames", "description": "The name of the process."}, "description": {"type": "string", "description": "A description of the process."}, "contributors": {"type": "array", "minItems": 1, "description": "The contributors of the process as emails in the ebury.com domain.", "items": {"type": "string", "enum": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]}}, "status": {"$ref": "#/definitions/status"}, "platform": {"type": "string", "description": "The platform of the process.\nIt can be only one of the following: 'gas', 'cloud-run', 'make', 'zapier', 'local', 'documentation'.", "$ref": "#/definitions/supportedPlatforms"}, "relatedJiraIssues": {"type": "array", "minItems": 1, "description": "The Jira issues related to the process.", "items": {"type": "string", "pattern": "^[A-Z]{1,10}-[0-9]{1,10}$"}}, "relatedProcesses": {"type": "array", "minItems": 1, "description": "A list of related processes. The values must match the name of other processes folder.", "items": {"$ref": "#/definitions/processNames"}}, "stages": {"$ref": "#/definitions/stages"}, "stakeholders": {"description": "The stakeholders of the process.\nIt may be a personal one (<EMAIL>) or a team one (<EMAIL>).\nLeave `null` only if this process does not have specific stakeholders; it will be assumed that the stakeholders are the ones in the `relatedProcesses`, so please take care of properly specifying them.", "anyOf": [{"type": "null"}, {"type": "array", "minItems": 1, "items": {"type": "string", "pattern": ".*@ebury\\.com$", "format": "email"}}]}, "impact": {"description": "The estimated impact of the process, on a yearly basis, in GBP.\nLeave `null` only if this process does not have direct calculated impact; it will be assumed that the impact is provided by the `relatedProcesses`, so please take care of properly specifying them.", "anyOf": [{"type": "null"}, {"type": "array", "minItems": 1, "items": {"type": "object", "properties": {"type": {"description": "The type of impact. [manualWorkSaving, thirdPartyServicesSaving, risk]", "enum": ["manualWorkSaving", "thirdPartyServicesSaving", "risk"]}, "description": {"type": "string", "pattern": ".+"}}, "anyOf": [{"type": "object", "properties": {"type": {"enum": ["manualWorkSaving"]}, "description": {"description": "What kind of cost is being saved. [non empty string]"}, "hours": {"type": "number", "description": "The number of hours of manual process being saved per year.", "minimum": 1}, "costPerHour": {"type": "number", "description": "The cost per hour of the manual process.", "minimum": 1}}, "required": ["type", "description", "hours", "costPerHour"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["thirdPartyServicesSaving"]}, "description": {"description": "What third party service is being saved. [non empty string]"}, "serviceCost": {"type": "number", "description": "The cost of the service being saved.", "minimum": 1}, "paymentsPerYear": {"type": "number", "description": "The number of times the service had to be paid per year.", "minimum": 1e-21}}, "required": ["type", "description", "serviceCost", "paymentsPerYear"], "additionalProperties": false}, {"type": "object", "properties": {"type": {"enum": ["risk"]}, "kind": {"type": "string", "description": "The kind of risk.", "enum": ["breach", "fine", "disruption", "lostData"]}, "description": {"description": "What kind of risk is being prevented. [non empty string]"}, "costPerFailure": {"type": "number", "description": "The cost in case the risk happens.", "minimum": 1}, "estimatedTimesPerYear": {"type": "number", "description": "An estimation of the number of times that the risk could occur per year.", "minimum": 1e-21}}, "required": ["type", "description", "costPerFailure", "estimatedTimesPerYear"], "additionalProperties": false}]}, "additionalProperties": false}]}}, "allOf": [{"if": {"properties": {"platform": {"const": "gas"}}}, "then": {"properties": {"workingFiles": {"type": "array", "description": "The files to be bundled and uploaded to the Google Apps Script project.", "oneOf": [{"const": ["appsscript.json", "backend.ts"]}, {"const": ["appsscript.json", "backend.ts", "frontend.html"]}]}, "stages": {"type": "object", "$ref": "#/definitions/stages", "patternProperties": {"development": {"$ref": "#/definitions/gasDevelopmentStage"}, "staging|production": {"$ref": "#/definitions/gasAdvancedStage"}}}}, "required": ["workingFiles"]}}, {"if": {"properties": {"platform": {"const": "chrome-extension"}}}, "then": {"properties": {"stages": {"type": "object", "$ref": "#/definitions/stages", "patternProperties": {"development": {"$ref": "#/definitions/chromeExtensionDevelopmentStage"}, "staging|production": {"$ref": "#/definitions/chromeExtensionAdvancedStage"}}}}}}, {"if": {"properties": {"platform": {"const": "make"}}}, "then": {"properties": {"stages": {"type": "object", "$ref": "#/definitions/stages", "patternProperties": {"^.*$": {"$ref": "#/definitions/makeStage"}}}}}}, {"if": {"properties": {"platform": {"const": "cloud-run"}}}, "then": {"properties": {"runtime": {"enum": ["deno"]}, "stages": {"type": "object", "$ref": "#/definitions/stages", "patternProperties": {"local": {"$ref": "#/definitions/localStage"}, "^(?!local$).*$": {"$ref": "#/definitions/cloudRunStage"}}}, "deploymentOptions": {"type": "object", "description": "Options for the Cloud Run deployment.\nSee https://cloud.google.com/sdk/gcloud/reference/run/deploy.", "properties": {"port": {"type": "integer", "description": "Container port to receive requests at. Also sets the $PORT environment variable. Must be a number between 1 and 65535, inclusive. Defaults to 8080. See https://cloud.google.com/sdk/gcloud/reference/run/deploy#--port", "minimum": 1, "maximum": 65535}, "cpu": {"type": "string", "description": "The number of CPUs to allocate to the container.\nPossible values: [1, 2, 4]. Defaults to 0.5.\nSee https://cloud.google.com/sdk/gcloud/reference/run/deploy#--cpu", "enum": ["1", "2", "4"]}, "memory": {"type": "string", "description": "The amount of memory to allocate to the container.\nPossible values: [512M, 1G, 2G, 4G]. Defaults to 1024Mi.\nSee https://cloud.google.com/sdk/gcloud/reference/run/deploy#--memory", "enum": ["512Mi", "1Gi", "2Gi", "4Gi"]}, "deployAsJob": {"type": "boolean", "description": "Deploy as a Cloud Scheduler job instead of a Cloud Run service. Defaults to false.\nSee https://cloud.google.com/sdk/gcloud/reference/beta/run/jobs/deploy\nOnly to be set if you want to deploy the process as a Cloud Scheduler job, otherwise don't set.", "enum": [true]}, "environmentVariables": {"type": "object", "description": "Environment variables to mount to the container.", "additionalProperties": {"type": "object", "properties": {"type": {"type": "string", "description": "Type of the environment variable. Possible values: [\"secret\", \"variable\"]", "enum": ["secret", "variable"]}}, "oneOf": [{"properties": {"type": {"const": "secret"}, "secretName": {"description": "The name of the secret referenced from the Google Secrets Manager. [non empty string].\nhttps://console.cloud.google.com/security/secret-manager?authuser=<EMAIL>&project=appscript-296515", "type": "object", "properties": {"development": {"type": ["string", "null"]}, "staging": {"type": ["string", "null"]}, "production": {"type": ["string", "null"]}}, "required": ["staging", "production"]}}, "required": ["type", "secretName"]}, {"properties": {"type": {"const": "variable"}, "value": {"type": "object", "properties": {"development": {"type": ["string", "null"]}, "staging": {"type": ["string", "null"]}, "production": {"type": ["string", "null"]}}, "required": ["staging", "production"]}}, "required": ["type", "value"]}]}}}}}, "required": ["runtime"]}}, {"if": {"properties": {"platform": {"const": "local"}}}, "then": {"properties": {"stages": {"type": "object", "$ref": "#/definitions/stages", "patternProperties": {"^.*$": {"$ref": "#/definitions/localStage"}}}}}}], "definitions": {"supportedPlatforms": {"enum": ["gas", "make", "chrome-extension", "cloud-run", "zapier", "local", "documentation"]}, "status": {"enum": ["active", "inactive"]}, "stages": {"type": "object", "description": "The development stages.", "patternProperties": {"^.*$": {"$ref": "#/definitions/stage"}}, "properties": {"development": {"description": "The development stage of the process.\nIt should be developed in a safe Google Workspace environment (eburypartners.com) in order to avoid major issues in the production environment.", "$ref": "#/definitions/stage"}, "staging": {"description": "The staging stage of the process.\nIt should be developed in the production Google Workspace environment (ebury.com) in order to test that all the authorizations are in place.", "$ref": "#/definitions/stage"}, "production": {"description": "The production stage of the process.\nThe process is deployed to the production environment and runs the actual business logic. In the future, this should be deployed automatically in the CI/CD pipeline, but for now it is deployed manually.", "$ref": "#/definitions/stage"}}, "required": ["staging", "production"], "additionalProperties": false}, "stage": {"type": "object", "properties": {"url": {"type": "string", "description": "The URL of the project's stage."}, "endpointUrl": {"type": "string", "description": "The endpoint of the project's stage."}}, "additionalProperties": false, "required": ["url"]}, "gasStage": {"type": "object", "$ref": "#/definitions/stage", "properties": {"url": {"type": "string", "description": "The URL of the Google Apps Script project."}, "endpointUrl": {"pattern": "^https://script\\.google\\.com/a/macros/ebury\\.com/s/[\\w-]{71,74}/exec$", "description": "The endpoint url of the Google Apps Script script deployment."}}}, "gasDevelopmentStage": {"type": "object", "$ref": "#/definitions/gasStage", "properties": {"url": {"pattern": "^https://script\\.google\\.com/home/<USER>/[\\w-]{57}/edit\\?authuser=servicedesk@ebury-partners\\.com$"}}}, "gasAdvancedStage": {"type": "object", "$ref": "#/definitions/gasStage", "properties": {"url": {"pattern": "^https://script\\.google\\.com/home/<USER>/[\\w-]{57}/edit\\?authuser=automationadmin@ebury\\.com$"}}}, "chromeExtensionStage": {"type": "object", "$ref": "#/definitions/stage", "properties": {"url": {"type": "string", "description": "The URL of the Google Apps Script project."}, "endpointUrl": {"type": "string", "description": "The endpoint url of the Google Apps Script script deployment."}}}, "chromeExtensionDevelopmentStage": {"type": "object", "$ref": "#/definitions/chromeExtensionStage", "properties": {"url": {"pattern": "^https://chrome\\.google\\.com/webstore/devconsole/d4b52b22-21dd-49ff-b35c-6dc512507e1a/[\\w-]{32}/edit\\?authuser=servicedesk@ebury-partners\\.com$"}}}, "chromeExtensionAdvancedStage": {"type": "object", "$ref": "#/definitions/chromeExtensionStage", "properties": {"url": {"pattern": "^^https://chrome\\.google\\.com/webstore/devconsole/d4b52b22-21dd-49ff-b35c-6dc512507e1a/[\\w-]{32}/edit\\?authuser=automationadmin@ebury\\.com$"}}}, "makeStage": {"type": "object", "$ref": "#/definitions/stage", "properties": {"url": {"type": "string", "description": "The URL of the Make scenario.", "pattern": "^https://eu1\\.make\\.celonis\\.com/[0-9]{1,3}/scenarios/(?:[0-9]{4}|[0-9]{5}|[0-9]{6})/edit$"}}}, "cloudRunStage": {"type": "object", "$ref": "#/definitions/stage", "anyOf": [{"properties": {"url": {"type": "string", "description": "The URL of the Dashboard of the Cloud Run project.", "pattern": "^https://console.cloud.google.com/run/detail/europe-west1/[\\w-]*"}}}, {"properties": {"url": {"type": "string", "description": "The URL of the Dashboard of the Cloud Run project.", "pattern": "^https://console.cloud.google.com/run/jobs/details/europe-west1/[\\w-]*"}}}, {"properties": {"endpointUrl": {"type": "string", "description": "The endpoint of the cloud function.", "pattern": "^https://(?:[\\w-]*-cehm3wmena-ew.a.run.app)$"}}}, {"properties": {"endpointUrl": {"type": "string", "description": "The endpoint of the cloud function.", "pattern": "^https://(?:[\\w-]*-1060281356034.europe-west1.run.app)$"}}}], "required": ["endpointUrl"]}, "localStage": {"type": "object", "$ref": "#/definitions/stage", "properties": {"url": {"type": "string", "description": "The URL of the local project (if applies).", "pattern": "^http://localhost:[0-9]{1,5}$"}, "endpointUrl": {"type": "string", "description": "The URL of the local project (if applies).", "pattern": "^http://localhost:[0-9]{1,5}$"}}}, "processNames": {"description": "Do not manually change this list, it is automatically generated from the processes' folder names when `validate-json-schemas` script.", "enum": ["a-dummy-project", "audits", "aws-access-management", "barx-audit", "barx-audit-trigger", "bpa-api-docs", "bpa-chrome-extension", "check-for-unprocessed-hr-tickets", "check-for-unprocessed-tech-supp-tickets", "check-leaver-in-oq", "chile-reporting-notification", "citi-audit", "citi-audit-trigger", "clm-requests-reconciliation", "cookies-backup-chrome-extension", "dalo-radius-portal", "design-form", "ebo-roadmap-suggestion", "eburyops-portal-to-trade-support", "edit-jsm-ticket", "edit-jsm-ticket-gas", "egencia-user-integration", "email-forwarding-rules-cleanup", "error-notification-router-gas", "fcc-escalation", "fenx-backfill", "fenx-backfill-mass-update", "fenx-cancel-journeys-tool", "fenx-synch-users-teams-access-layers", "fenx-user-assignation", "financial-statements-analysis", "find-zendesk-ticket-id", "firestore-populate-database", "form-submit-forwarder", "gas-gcp-bridge", "gas-monitoring", "gas-runtime-errors-forwarder", "get-vonage-users", "get-wifi-creds", "github-compass-sync", "google-account-deletion", "hibob-additional-fields-for-sync", "hibob-backfill", "hibob-backup", "hibob-delete-list-items", "hibob-driven-google-account-sync", "hibob-leaver-event", "hibob-punctual-access-request", "hibob-sync", "incident-management-security", "incident-management-tech", "intelli", "intelligent-data-extraction-api", "jira-issues-creation-from-data", "jira-issues-timestamps", "jira-issues-timestamps-on-demand", "kyc-matrix-obfuscation", "leaver-platform-ticket", "leaver-remove-google-admin-role-request", "leavers-actions-part-1", "leavers-actions-part-2", "leavers-apply-routing-email-rule", "leavers-apps-access-removal", "leavers-atlassian", "leavers-dalo-radius", "leavers-fenx", "leavers-get-jira-issue-key", "leavers-github", "leavers-google-routine", "leavers-jumio", "leavers-looker", "leavers-netsuite", "leavers-netsuite-on-demand", "leavers-onetrust", "leavers-platform-access-request", "leavers-postman", "leavers-quantum-access-removal", "leavers-queue", "leavers-queue-processor", "leavers-routine", "leavers-salesforce", "leavers-slack", "leavers-urgent", "leavers-vonage", "leavers-zendesk", "make-error-mailhook", "managed-browsers-sync", "mifid-form-backend", "monitor-gas-trigger-notifications", "monitoring", "monitoring-fenx", "monitoring-github", "monitoring-google", "monitoring-jira", "monitoring-salesforce", "monitoring-zendesk", "netsuite-role-change", "new-content-request-form", "new-starter-email", "new-starter-reminder", "offboarding-domain-delegation-routines", "onboarding-aws", "onboarding-change-hibob-email", "onboarding-consistency-watchdog", "onboarding-create-new-starter-requests", "onboarding-fenx-scim", "onboarding-github", "onboarding-google", "onboarding-jira", "onboarding-looker", "onboarding-netsuite-access-request", "onboarding-netsuite-create-account", "onboarding-process", "onboarding-queue", "onboarding-queue-processor", "onboarding-salesforce", "onboarding-setup", "onboarding-tree-nation", "onboarding-vonage", "onboarding-vpn", "onboarding-welcome-email", "ondorse-application-notification", "onetrust-to-netsuite-integration", "ops-ebury-add-on", "passwordless-authentication-api", "pending-jira-issues-alert", "periodic-reviews", "periodic-reviews-api", "periodic-reviews-bounce-email", "periodic-reviews-chaser-email", "periodic-reviews-create-jira-issues", "periodic-reviews-generate-client-document", "periodic-reviews-get-info-big-query", "periodic-reviews-interface", "periodic-reviews-orchestrator", "periodic-reviews-queue-processor", "periodic-reviews-send-first-email", "periodic-reviews-webapp", "platform-access-leaver-request-component", "platform-access-title-update", "post-ident", "privacy-notice-api", "privacy-notice-interface", "program-manager-form", "program-managers-send-final-product", "reverse-proxy", "salesforce-role-allocation", "sana-course-overdue-notifier", "sana-report-extractor", "scdc", "scdc-backup", "scdc-data-fetcher", "scdc-double-check", "scdc-drive-search", "scdc-filter-funnel", "scdc-orchestrator", "seco-release-documents", "secret-manager-secrets-monitor", "slack-health-check", "store-jsons-in-bigquery", "trade-finance-checks", "trade-finance-checks-file-watcher", "trade-finance-checks-handler", "trade-finance-checks-relevant-files-filter", "trade-finance-four-eyes", "tta-monitor", "tta-monitor-pr", "undelivered-messages-notifier", "update-json-property-impact", "update-user-signature", "user-interaction-simulation-api", "user-interaction-simulation-handler", "vonage-delete-all-archived", "warroom-event", "yopass"], "uniqueItems": true}}, "required": ["name", "description", "platform", "relatedJiraIssues", "contributors", "relatedProcesses", "impact", "stakeholders"], "if": {"not": {"properties": {"platform": {"const": "documentation"}}}}, "then": {"required": ["stages", "status"]}}