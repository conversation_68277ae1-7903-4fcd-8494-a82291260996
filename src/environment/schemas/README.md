# JSON schemas

The JSON schemas are used to validate the configuration files for the processes.

The valdation is performed by the `validate-json-schemas` script, which is executed by the `develop` script, in the CI pipeline, and can be executed manually.

This folder's structure matches the structure of the `packages` folder, and it's meant to be used as a reference for the configuration files. The `validate-json-schemas` script will use this structure as a reference to know which JSON schema to use for each configuration file.

For instance, the `validate-json-schemas` script will use the JSON schema in `environment/schemas/packages/processes` to validate the configuration files in `packages/processes` folder.

## VSCode hints and validation

These schemas are referenced also in the `settings.json` file, so that VSCode can provide hints and validation for the configuration files.

These references have to be updated manually.
