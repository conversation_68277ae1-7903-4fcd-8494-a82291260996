import { Impersonator } from "modules/cloud-run-impersonator/index.ts";
import { Logger } from "modules/logger/index.ts";

let cachedSecret: {
  private_key: string;
  client_email: string;
};
export async function fetchAutomationCredentials(): Promise<{
  private_key: string;
  client_email: string;
}> {
  if (cachedSecret) return cachedSecret;
  Logger.info(`Retrieving automation admin impersonator creds`);

  const impersonator = new Impersonator({
    scopes: [
      "https://www.googleapis.com/auth/cloud-platform",
    ],
    subject: "<EMAIL>",
  });
  const secretManager = await impersonator.getSecretManagerClient();

  const secret = await secretManager.projects.locations.secrets.versions.access({
    name: `projects/appscript-296515/secrets/AUTOMATION_ADMIN_IMPERSONATOR_CREDS/versions/latest`,
  });
  Logger.info("Creds fetched successfully");

  const secretContent = secret.data.payload?.data;
  const secretString = atob(secretContent as string).replaceAll("\n", "");
  const credentials = JSON.parse(secretString) as {
    private_key: string;
    client_email: string;
  };
  cachedSecret = credentials;
  return credentials;
}
