import { getCommandOutput } from "./get-command-output.ts";

const cachedVarId = "__CURRENT_BRANCH_NAME";
/**
 * Returns the current branch name
 */
export const getCurrentBranchName = async () => {
  if (Deno.env.get(cachedVarId)) {
    return Deno.env.get(cachedVarId);
  }
  const branchNameRaw = await getCommandOutput({
    commandParts: ["git", "branch", "--show-current"],
  });
  const branchName = branchNameRaw.trim();
  Deno.env.set(cachedVarId, branchName);
  return branchName;
};
