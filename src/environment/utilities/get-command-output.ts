import { Logger } from "modules/logger/index.ts";

export const getCommandOutput = async (options: {
  commandParts: string[];
  cwd?: string;
  env?: Record<string, string>;
}) => {
  const commandString = options.commandParts.join(" ");
  Logger.info(`Executing command: ${commandString}`);
  const { commandParts, cwd, env } = options;
  const isMainCommandDeno = commandParts[0] === "deno";
  const mainCommand = isMainCommandDeno ? Deno.execPath() : commandParts[0];
  const commandArgs = commandParts.slice(1);
  const executedCommand = new Deno.Command(mainCommand, {
    args: commandArgs,
    cwd,
    env,
    stderr: "piped",
    stdout: "piped",
  });
  const { stdout, stderr } = await executedCommand.output();
  console.log(new TextDecoder().decode(stderr));
  return new TextDecoder().decode(stdout);
};
