import { getCommandOutput } from "./get-command-output.ts";

export const getCurrentCommit = async () => {
  return await getCommandOutput({
    commandParts: ["git", "rev-parse", "HEAD"],
  });
};

const getRemoteURL = async () => {
  return await getCommandOutput({
    commandParts: ["git", "remote", "get-url", "origin"],
  });
};

export const getCurrentCommitURL = async () => {
  const remoteURL = (await getRemoteURL()).split(".git")[0];
  const currentCommit = await getCurrentCommit();
  return `${remoteURL}/commit/${currentCommit}`;
};
