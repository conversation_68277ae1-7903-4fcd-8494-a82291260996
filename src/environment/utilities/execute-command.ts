import { Logger } from "modules/logger/index.ts";

const STRING_LENGTH_LIMIT = 500;

export const executeCommand = async (options: {
  commandParts: string[];
  cwd?: string;
  env?: Record<string, string>;
}) => {
  const commandString = options.commandParts.join(" ");
  const commandStringTruncated = commandString.length > STRING_LENGTH_LIMIT ? `${commandString.slice(0, STRING_LENGTH_LIMIT)}...` : commandString;
  Logger.info(`Executing command: ${commandStringTruncated}`);
  const { commandParts, cwd, env } = options;
  const isMainCommandDeno = commandParts[0] === "deno";
  const mainCommand = isMainCommandDeno ? Deno.execPath() : commandParts[0];
  const commandArgs = commandParts.slice(1);
  const executedCommand = new Deno.Command(mainCommand, {
    args: commandArgs,
    cwd,
    env,
    stderr: "inherit",
    stdout: "inherit",
  });
  const child = executedCommand.spawn();
  const { success, code, signal } = await child.status;
  Logger.debug({ success, code, signal });
  if (!success) {
    throw new Error(`Failed to execute command: ${commandStringTruncated}`);
  }
  Logger.info(`Completed command: ${commandStringTruncated}`);
};
