/**
 * Transforms the texts formats from google docs to html tags
 *
 * @param {GoogleAppsScript.Document.Text} item
 * @param {string[]} output
 */
const processText = (
  item: GoogleAppsScript.Document.Text,
  output: string[],
) => {
  const text: string = item.getText();
  const indices: number[] = item.getTextAttributeIndices();
  if (indices.length <= 1) {
    const styleString: string = getItemStyleString(item, undefined);
    let tag = "span";
    if (styleString.includes("href")) {
      tag = "a";
    }
    output.push(`<${tag} ${styleString}>${text}</${tag}>`);
  } else {
    for (let i = 0; i < indices.length; i++) {
      const startPos: number = indices[i];
      const endPos: number = i + 1 < indices.length ? indices[i + 1] : text.length;
      const partText: string = text.substring(startPos, endPos);
      const styleString: string = getItemStyleString(item, startPos);
      let tag = "span";
      if (styleString.includes("href")) {
        tag = "a";
      }
      output.push(`<${tag} ${styleString}>${partText}</${tag}>`);
    }
  }
};
/**
 * Retrieves the style of the item on google docs.
 *
 * @param {GoogleAppsScript.Document.Text} item
 * @param {number} [startPos]
 * @return {*}  {string}
 */
const getItemStyleString = (
  item: GoogleAppsScript.Document.Text,
  startPos?: number,
): string => {
  const itemAttributes: GoogleAppsScript.Document.Attribute[] = startPos !== undefined ? item.getAttributes(startPos) : item.getAttributes();
  const attributeKeys: string[] = Object.keys(itemAttributes);
  const attributesToCss = [];
  let linkUrl: string | undefined;
  for (const attributeIndex in attributeKeys) {
    const attribute = attributeKeys[attributeIndex];
    const attributeValue = itemAttributes[attribute as keyof typeof itemAttributes];
    if (!attributeValue) {
      continue;
    }
    let attributeCss: string;
    switch (attribute) {
      case "BACKGROUND_COLOR":
        attributeCss = `background-color: ${attributeValue}`;
        break;
      case "BOLD":
        attributeCss = "font-weight: bold";
        break;
      case "BORDER_COLOR":
        attributeCss = `border-color: ${attributeValue}`;
        break;
      case "BORDER_WIDTH":
        attributeCss = `border-width: ${attributeValue}`;
        break;
      case "CODE":
        attributeCss = "font-family: monospace";
        break;
      case "FONT_FAMILY":
        attributeCss = `font-family: ${attributeValue}`;
        break;
      case "FONT_SIZE":
        attributeCss = `font-size: ${attributeValue}`;
        break;
      case "FOREGROUND_COLOR":
        attributeCss = `color: ${attributeValue}`;
        break;
      case "HEADING":
        attributeCss = `font-weight: bold`;
        break;
      case "HEIGHT":
        attributeCss = `height: ${attributeValue}`;
        break;
      case "HORIZONTAL_ALIGNMENT":
        attributeCss = `text-align: ${attributeValue}`;
        break;
      case "INDENT_END":
        attributeCss = `margin-right: ${attributeValue}`;
        break;
      case "INDENT_FIRST_LINE":
        attributeCss = `text-indent: ${attributeValue}`;
        break;
      case "INDENT_START":
        attributeCss = `margin-left: ${attributeValue}`;
        break;
      case "ITALIC":
        attributeCss = "font-style: italic";
        break;
      case "GLYPH_TYPE":
        attributeCss = `font-family: ${attributeValue}`;
        break;
      case "LEFT_TO_RIGHT":
        attributeCss = "direction: ltr";
        break;
      case "LINE_SPACING":
        attributeCss = `line-height: ${attributeValue}`;
        break;
      case "LINK_URL":
        attributeCss = `text-decoration: underline`;
        linkUrl = attributeValue as unknown as string;
        break;
      case "LIST_ID":
        attributeCss = `list-style-type: ${attributeValue}`;
        break;
      case "MARGIN_BOTTOM":
        attributeCss = `margin-bottom: ${attributeValue}`;
        break;
      case "MARGIN_LEFT":
        attributeCss = `margin-left: ${attributeValue}`;
        break;
      case "MARGIN_RIGHT":
        attributeCss = `margin-right: ${attributeValue}`;
        break;
      case "MARGIN_TOP":
        attributeCss = `margin-top: ${attributeValue}`;
        break;
      case "NESTING_LEVEL":
        attributeCss = `margin-left: ${attributeValue}`;
        break;
      case "MINIMUM_HEIGHT":
        attributeCss = `min-height: ${attributeValue}`;
        break;
      case "PADDING_BOTTOM":
        attributeCss = `padding-bottom: ${attributeValue}`;
        break;
      case "PADDING_LEFT":
        attributeCss = `padding-left: ${attributeValue}`;
        break;
      case "PADDING_RIGHT":
        attributeCss = `padding-right: ${attributeValue}`;
        break;
      case "PADDING_TOP":
        attributeCss = `padding-top: ${attributeValue}`;
        break;
      case "PAGE_HEIGHT":
        attributeCss = `height: ${attributeValue}`;
        break;
      case "PAGE_WIDTH":
        attributeCss = `width: ${attributeValue}`;
        break;
      case "SPACING_AFTER":
        attributeCss = `margin-bottom: ${attributeValue}`;
        break;
      case "SPACING_BEFORE":
        attributeCss = `margin-top: ${attributeValue}`;
        break;
      case "STRIKETHROUGH":
        attributeCss = "text-decoration: line-through";
        break;
      case "UNDERLINE":
        attributeCss = "text-decoration: underline";
        break;
      case "VERTICAL_ALIGNMENT":
        attributeCss = `vertical-align: ${attributeValue}`;
        break;
      case "WIDTH":
        attributeCss = `width: ${attributeValue}`;
        break;
      default:
        attributeCss = "";
        break;
    }
    if (attributeCss) {
      attributesToCss.push(attributeCss);
    }
  }
  const styleContent: string = attributesToCss.join(";");
  const hrefString: string = linkUrl ? `href="${linkUrl}"` : "";
  const styleString: string = styleContent.length < 1 ? "" : `style="${attributesToCss.join(";")}" ${hrefString}`;
  return styleString;
};
export { processText };
