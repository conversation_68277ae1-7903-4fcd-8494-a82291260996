import { ExportedImage } from "../../types.ts";
import { processImage } from "./process-image.ts";
import { processText } from "./process-text.ts";
/**
 * Transforms a google doc item to html tags.
 *
 * @param {GoogleAppsScript.Document.Element} item
 * @param {{ [key: string]: number }} listCounters
 * @param {ExportedImage[]} images
 * @param {boolean} [dontExportImages=false]
 * @return {*}  {string}
 */
const processItem = (
  item: GoogleAppsScript.Document.Element,
  listCounters: { [key: string]: number },
  images: ExportedImage[],
  dontExportImages = false,
): string => {
  const output: string[] = [];
  let prefix = "",
    suffix = "";
  if (item.getType() === DocumentApp.ElementType.PARAGRAPH) {
    const paragraph = item as GoogleAppsScript.Document.Paragraph;
    switch (paragraph.getHeading()) {
      // Add a # for each heading level. No break, so we accumulate the right number.
      case DocumentApp.ParagraphHeading.HEADING6:
        (prefix = "<h6>"), (suffix = "</h6>");
        break;
      case DocumentApp.ParagraphHeading.HEADING5:
        (prefix = "<h5>"), (suffix = "</h5>");
        break;
      case DocumentApp.ParagraphHeading.HEADING4:
        (prefix = "<h4>"), (suffix = "</h4>");
        break;
      case DocumentApp.ParagraphHeading.HEADING3:
        (prefix = "<h3>"), (suffix = "</h3>");
        break;
      case DocumentApp.ParagraphHeading.HEADING2:
        (prefix = "<h2>"), (suffix = "</h2>");
        break;
      case DocumentApp.ParagraphHeading.HEADING1:
        (prefix = "<h1>"), (suffix = "</h1>");
        break;
      default:
        (prefix = "<p>"), (suffix = "</p>");
    }
    if (paragraph.getNumChildren() === 0) return "";
  } else if (item.getType() === DocumentApp.ElementType.INLINE_IMAGE) {
    const image = item as GoogleAppsScript.Document.InlineImage;
    processImage(image, images, output, dontExportImages);
  } else if (item.getType() === DocumentApp.ElementType.LIST_ITEM) {
    const listItem = item as GoogleAppsScript.Document.ListItem;
    const glyph: GoogleAppsScript.Document.GlyphType = listItem.getGlyphType();
    const key = `${listItem.getListId()}.${listItem.getNestingLevel()}`;
    let counter: number = listCounters[key] || 0;
    // First list item
    if (counter === 0) {
      // Bullet list
      const isGlyph = glyph === DocumentApp.GlyphType.BULLET ||
        glyph === DocumentApp.GlyphType.HOLLOW_BULLET ||
        glyph === DocumentApp.GlyphType.SQUARE_BULLET;
      if (isGlyph) {
        (prefix = "<ul><li>"), (suffix = "</li>");
        suffix += "</ul>";
      } else {
        // Ordered list
        (prefix = "<ol><li>"), (suffix = "</li>");
      }
    } else {
      prefix = "<li>";
      suffix = "</li>";
    }
    const isEnded = item.isAtDocumentEnd() ||
      (item.getNextSibling() &&
        item.getNextSibling().getType() !== DocumentApp.ElementType.LIST_ITEM);
    if (isEnded) {
      const isGlyph = glyph === DocumentApp.GlyphType.BULLET ||
        glyph === DocumentApp.GlyphType.HOLLOW_BULLET ||
        glyph === DocumentApp.GlyphType.SQUARE_BULLET;
      if (isGlyph) {
        suffix += "</ul>";
      } else {
        // Ordered list
        suffix += "</ol>";
      }
    }
    counter++;
    listCounters[key] = counter;
  }
  output.push(prefix);
  if (item.getType() === DocumentApp.ElementType.TEXT) {
    const text = item as GoogleAppsScript.Document.Text;
    processText(text, output);
  } else {
    const itemWithChildren = item as
      | GoogleAppsScript.Document.Paragraph
      | GoogleAppsScript.Document.ListItem;
    if (itemWithChildren.getNumChildren) {
      const numChildren: number = itemWithChildren.getNumChildren();
      // Walk through all the child elements of the doc.
      for (let i = 0; i < numChildren; i++) {
        const child: GoogleAppsScript.Document.Element = itemWithChildren
          .getChild(i);
        output.push(processItem(child, listCounters, images, dontExportImages));
      }
    }
  }
  output.push(suffix);
  return output.join("");
};
export { processItem };
