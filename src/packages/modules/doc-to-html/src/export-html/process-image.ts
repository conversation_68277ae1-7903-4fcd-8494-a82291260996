import { ExportedImage } from "../../types.ts";
/**
 * Transforms images on google docs to html tags.
 *
 * @param {GoogleAppsScript.Document.InlineImage} item
 * @param {ExportedImage[]} images
 * @param {string[]} output
 * @param {boolean} [dontExportImages=false]
 */
const processImage = (
  item: GoogleAppsScript.Document.InlineImage,
  images: ExportedImage[],
  output: string[],
  dontExportImages = false,
) => {
  images = images || [];
  const blob: GoogleAppsScript.Base.Blob = item.getBlob();
  const contentType: string = blob.getContentType() || "";
  let extension = "";
  if (/\/png$/.test(contentType)) {
    extension = "png";
  } else if (/\/gif$/.test(contentType)) {
    extension = "gif";
  } else if (/\/jpe?g$/.test(contentType)) {
    extension = "jpg";
  } else {
    throw "Unsupported image type: " + contentType;
  }
  const stringifiedBlob = `data:${contentType};base64,${
    Utilities.base64Encode(
      blob.getBytes(),
    )
  }`;
  const imagePrefix = "Image_";
  let imageCounter: number = images.length;
  const name = `${imagePrefix + imageCounter}.${extension}`;
  imageCounter++;
  const width: number = item.getWidth();
  const height: number = item.getHeight();
  const src: string = dontExportImages ? item.getAltTitle() : `cid:${name}`;
  const stringImage = `<img src="${src}" width="${width}" height="${height}" alt="${name}" />`;
  const link: string = item.getLinkUrl();
  const stringImageWithLink = `<a href="${link}">${stringImage}</a>`;
  const pushedImage: string = link ? stringImageWithLink : stringImage;
  output.push(pushedImage);
  if (!dontExportImages) {
    const image: ExportedImage = {
      blob: stringifiedBlob,
      type: contentType,
      name: name,
    };
    images.push(image);
  }
};
export { processImage };
