/**
 * Replaces the google doc format with html tags
 *
 * @param {string} html
 * @param {{ [key: string]: string }} parameters
 * @return {*}  {{ html: string; warnings: string[] }}
 */
export const replaceHTML = (
  html: string,
  parameters: { [key: string]: string },
): { html: string; warnings: string[] } => {
  const warnings: string[] = [];
  const replacedHTML: string = html.replace(
    /{{(.*?)}}/g,
    (match: string, parameterName: string) => {
      if (parameterName === "docUrl") {
        warnings.push(
          "docUrl is a reserved parameter name and should not be used as a parameter name.",
        );
      }
      const parameterValue: string = parameters[parameterName];
      if (parameterValue === undefined) {
        warnings.push(`parameter '${parameterName}' is not provided`);
        return `{{${parameterName}}}`;
      } else {
        return parameterValue;
      }
    },
  );
  return {
    html: replacedHTML,
    warnings,
  };
};
