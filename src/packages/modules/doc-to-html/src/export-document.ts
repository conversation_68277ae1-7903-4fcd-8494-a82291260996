import { replaceHTML } from "./replace-html.ts";
import { processItem } from "./export-html/process-item.ts";
import { ExportDocument, ExportedImage } from "../types.ts";

/**
 * Exports a google doc to html.
 *
 * @param {string} docUrl
 * @param {{ [key: string]: string }} parameters
 * @param {boolean} [dontExportImages=false]
 * @return {*}  {ExportDocument}
 */
const exportDocument = (
  docUrl: string,
  parameters: { [key: string]: string },
  dontExportImages = false,
): ExportDocument => {
  console.log("exporting document to html");
  const doc: GoogleAppsScript.Document.Document = DocumentApp.openByUrl(docUrl);
  const body: GoogleAppsScript.Document.Body = doc.getBody();
  const numChildren: number = body.getNumChildren();
  const output: string[] = [];
  const images: ExportedImage[] = [];
  const listCounters: { [key: string]: number } = {};
  // Walk through all the child elements of the body.
  for (let i = 0; i < numChildren; i++) {
    const childElement: GoogleAppsScript.Document.Element = body.getChild(i);
    output.push(
      processItem(childElement, listCounters, images, dontExportImages),
    );
  }
  const baseHTML: string = output.join("");
  const { html, warnings } = replaceHTML(baseHTML, parameters);
  const result: ExportDocument = {
    html,
    images,
  };
  if (warnings.length > 0) {
    result.warnings = warnings;
  }
  console.log(result);
  return result;
};
export { exportDocument };
