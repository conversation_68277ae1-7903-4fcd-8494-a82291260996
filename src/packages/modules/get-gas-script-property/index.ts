/**
 * Get a property from the script properties.
 * @param {string} property The name of the property to get.
 * @param {boolean} mandatory Whether the property is mandatory. Defaults to true.
 * @returns {T} The value of the property.
 */
export const getGASScriptProperty = <T extends string>(
  property: string,
  mandatory = true,
): T => {
  console.log(`Getting script property ${property}`);
  const value = PropertiesService.getScriptProperties().getProperty(property);
  const isMandatoryAndEmpty = mandatory && !value;

  if (isMandatoryAndEmpty) {
    throw new Error(`${property} not set in script properties`);
  }
  return value as T;
};
