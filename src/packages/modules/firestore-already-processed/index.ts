import { initializeFirestore } from "modules/firestore/index.ts";
import { Logger } from "modules/logger/index.ts";

export class FirestoreAlreadyProcessedManager {
  private database: FirebaseFirestore.CollectionReference<FirebaseFirestore.DocumentData, FirebaseFirestore.DocumentData> | undefined;
  private databaseName: string;

  /**
   * Constructs an instance of the FirestoreAlreadyProcessedManager.
   *
   * @param options - The configuration options for the manager.
   * @param options.databaseName - The name of the Firestore database.
   */

  constructor(options: {
    databaseName: string;
  }) {
    const { databaseName } = options;
    this.databaseName = databaseName;
  }

  private async getDatabase() {
    if (this.database) return this.database;
    const db = await initializeFirestore(this.databaseName);
    if (db) {
      this.database = db;
    } else {
      throw new Error("initializeFirestore did not return a Firestore instance");
    }
    return this.database;
  }

  public async setAlreadyProcessed(fileId: string) {
    Logger.debug(`Setting already processed ${fileId}`);
    const database = await this.getDatabase();
    await database.doc(fileId).set({
      processedAt: Date.now(),
    });
    Logger.debug(`Set already processed ${fileId}`);
  }

  public async isAlreadyProcessed(fileId: string) {
    Logger.debug(`Checking if already processed ${fileId}`);
    const database = await this.getDatabase();
    const document = database.doc(fileId);
    const data = await document.get().then((res) => res.data());
    const isAlreadyProcessed = !!data;
    Logger.debug({ isAlreadyProcessed });
    return isAlreadyProcessed;
  }

  public async removeAlreadyProcessed(fileId: string) {
    Logger.debug(`Removing already processed ${fileId}`);
    const database = await this.getDatabase();
    const document = database.doc(fileId);
    await document.delete();
  }
}
