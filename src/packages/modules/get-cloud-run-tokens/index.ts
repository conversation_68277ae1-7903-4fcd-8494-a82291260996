import { GoogleAuth, JWTInput } from "google-auth-library";
import { checkEnvVars } from "../check-env-vars/index.ts";

checkEnvVars(["AUTOMATION_ADMIN_IMPERSONATOR_CREDS"]);

/**
 * Retrieves an ID token for a Cloud Run target audience.
 *
 * @param {string} targetAudience - The target audience for the ID token.
 * @return {Promise<string>} A promise that resolves to the ID token.
 */
export const getIdTokenCloudRun = async (targetAudience: string) => {
  const client = await getIdTokenClient(targetAudience);
  return await client.idTokenProvider.fetchIdToken(targetAudience);
};
/**
 * Retrieves an ID token client for a given target audience.
 *
 * @param {string} targetAudience - The target audience for the ID token.
 * @return {Promise<IdTokenClient>} A promise that resolves to the ID token client.
 */
export const getIdTokenClient = async (targetAudience: string) => {
  const creds = getCredentials();
  const googleAuth = new GoogleAuth({
    credentials: creds,
  });
  return await googleAuth.getIdTokenClient(targetAudience);
};

/**
 * Retrieves an Access Token client
 */
export const getCloudRunAccessToken = async (): Promise<string> => {
  const creds = getCredentials();
  const googleAuth = new GoogleAuth({
    credentials: creds,
    scopes: [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/cloud-platform",
    ],
  });
  const accessToken = await googleAuth.getAccessToken();
  if (!accessToken) {
    throw new Error("Failed to get access token");
  }
  return accessToken;
};

const getCredentials = () => {
  const credentials = Deno.env.get("AUTOMATION_ADMIN_IMPERSONATOR_CREDS")!;
  return JSON.parse(credentials) as unknown as JWTInput;
};
