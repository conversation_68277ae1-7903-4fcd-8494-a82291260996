# Google Workspace GCF Authenticator

This module provides clients for Workspace services that are authenticated impersonating a given user through a Service Account to be used in Google Cloud Functions (GCF).

The purpose of this module stems from the need to access some Workspace resources, such as Google Sheets or Drive, that are only accessible through a specific Ebury account (generally, this account is `<EMAIL>`). And it is not possible to share these resources with any service account because they are not part of Ebury's domain. And the Application Default Credentials (ADC) are not valid either because they will always use a service account when executing in GCF.

Even though the module is meant to authenticate Workspace Services, it can be extended to provide clients for any Google service from the [`googleapis` Node.js library](https://googleapis.dev/nodejs/googleapis/latest/). As long as the provided service account has the necessary permissions to access the resource.

> ADC: <https://cloud.google.com/docs/authentication/application-default-credentials><br> User Impersonation: <https://github.com/googleapis/google-auth-library-nodejs/issues/916>

## Authentication

To authenticate the client, the module uses the [Google Auth Library](https://cloud.google.com/nodejs/docs/reference/google-auth-library/latest) to generate a `GoogleAuth` instance. This instance is the effective Impersonator that will create the requested clients.

The `GoogleAuth` instance needs the credentials to the service account that will impersonate the subject. These credentials must be of the `CredentialsBody` type. This means it must be an object that holds the `client_email` and `private_key` of the service account.

To obtain these you can follow the steps described [here](https://developers.google.com/workspace/guides/create-credentials#create_credentials_for_a_service_account). The service account must have the necessary permissions to access the resource you want to use. I.e., if you want to access a Google Sheet, the service account must have the `https://www.googleapis.com/auth/spreadsheets` scope or equivalent.

> **Warning**
>
> The Service Account must have domain wide delegation enabled. Otherwise it will not be able to impersonate the desired user.

In general, for the BPA team, it is recommended to use the `<EMAIL>` service account to impersonate `<EMAIL>`. This service account has the necessary permissions to access most resources relevant to the BPA team. The credentials to this serivice account can be found in the [Secret Manager](https://console.cloud.google.com/security/secret-manager/secret/AUTOMATION_ADMIN_IMPERSONATOR_CREDS/versions?project=appscript-296515).

### Supported Scopes for `<EMAIL>`

| Service                                                                                                                | Scope                                                                                                                                                                                                                                                                                                                                                                                             |
| ---------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| [Admin SDK API, v1 (Data Transfer)](https://developers.google.com/identity/protocols/oauth2/scopes#admin-datatransfer) | `https://www.googleapis.com/auth/admin.datatransfer`<br> `https://www.googleapis.com/auth/admin.datatransfer.readonly`                                                                                                                                                                                                                                                                            |
| [Admin SDK API, v1 (Directory)](https://developers.google.com/identity/protocols/oauth2/scopes#admin-directory)        | `https://www.googleapis.com/auth/admin.directory.device.mobile`<br> `https://www.googleapis.com/auth/admin.directory.domain`<br> `https://www.googleapis.com/auth/admin.directory.group`<br> `https://www.googleapis.com/auth/admin.directory.user`<br> `https://www.googleapis.com/auth/admin.directory.user.security`<br> `https://www.googleapis.com/auth/admin.directory.userschema.readonly` |
| [Admin SDK API, v1 (Reports)](https://developers.google.com/identity/protocols/oauth2/scopes#admin-reports)            | `https://www.googleapis.com/auth/admin.reports.usage.readonly`                                                                                                                                                                                                                                                                                                                                    |
| [BigQuery API, v2](https://developers.google.com/identity/protocols/oauth2/scopes#bigquery)                            | `https://www.googleapis.com/auth/bigquery`                                                                                                                                                                                                                                                                                                                                                        |
| [Calendar API, v3](https://developers.google.com/identity/protocols/oauth2/scopes#calendar)                            | `https://www.googleapis.com/auth/calendar`                                                                                                                                                                                                                                                                                                                                                        |
| [Cloud Identity API, v1](https://developers.google.com/identity/protocols/oauth2/scopes#cloudidentity)                 | `https://www.googleapis.com/auth/cloud-identity.devices`                                                                                                                                                                                                                                                                                                                                          |
| [Drive API, v3](https://developers.google.com/identity/protocols/oauth2/scopes#drive)                                  | `https://www.googleapis.com/auth/drive`<br> `https://www.googleapis.com/auth/drive.readonly`                                                                                                                                                                                                                                                                                                      |
| [Enterprise License Manager API, v1](https://developers.google.com/identity/protocols/oauth2/scopes#licensing)         | `https://www.googleapis.com/auth/apps.licensing`                                                                                                                                                                                                                                                                                                                                                  |
| [Gmail API, v1](https://developers.google.com/identity/protocols/oauth2/scopes#gmail)                                  | `https://www.googleapis.com/auth/gmail.settings.basic`<br> `https://www.googleapis.com/auth/gmail.settings.sharing`                                                                                                                                                                                                                                                                               |
| [Google OAuth2 API, v2](https://developers.google.com/identity/protocols/oauth2/scopes#oauth2)                         | `https://www.googleapis.com/auth/userinfo.email`                                                                                                                                                                                                                                                                                                                                                  |
| [Google Workspace Reseller API, v1](https://developers.google.com/identity/protocols/oauth2/scopes#reseller)           | `https://www.googleapis.com/auth/apps.order`                                                                                                                                                                                                                                                                                                                                                      |

## Supported Services

- Google Admin SDK Data Transfer API v1
- Google Admin SDK Directory API v1
- Google Admin SDK Reports API v1
- Google BigQuery API v2
- Google Calendar v3
- Google Drive v2
- Google Drive v3
- Google Enterprise License Manager API v1
- Google Gmail API v1
- Google Sheets v4
- Google Workspace Reseller API v1

## Helper Methods

### `setScopes`

With this method you can change the scopes for the Impesonator or set them if you didn't provide them in the constructor.

```ts
/**
 * Sets the scopes for the Impersonator.
  *
  * @param {string[]} scopes
  * @memberof Impersonator
  */
 setScopes(scopes: string[]): void;
```

[Code](index.ts#L50)

## Constructor & Usage

### Interface

```ts
new Impersonator({ subject, credentials, scopes }: {
  subject: string;
  credentials: Auth.CredentialBody;
  scopes: string[];
});
```

### Usage

Example:

```ts
import dotenv from "dotenv";
dotenv.config();
import { Impersonator } from "modules/cloud-run-impersonator";

// Get the credentials for the service account
const rawCredentials = process.env.CREDENTIALS;
const credentials = JSON.parse(rawCredentials);

const impersonator = new Impersonator({
  subject: "<EMAIL>",
  credentials,
  scopes: ["https://www.googleapis.com/auth/drive"],
});
```

[Code](index.ts#L24)
