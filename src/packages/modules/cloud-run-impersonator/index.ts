import {
  admin_directory_v1,
  admin_reports_v1,
  Auth,
  bigquery_v2,
  calendar_v3,
  docs_v1,
  drive_v3,
  google,
  licensing_v1,
  pubsub_v1,
  script_v1,
  secretmanager_v1,
  sheets_v4,
  admin_datatransfer_v1,
  cloudresourcemanager_v3,
  cloudasset_v1,
  storage_v1,
} from "googleapis";
import { checkEnvVars } from "modules/check-env-vars/index.ts";

const createClient = (email: string) => {
  checkEnvVars(["AUTOMATION_ADMIN_IMPERSONATOR_CREDS"]);
  const credentials = JSON.parse(
    Deno.env.get("AUTOMATION_ADMIN_IMPERSONATOR_CREDS")!,
  ) as Auth.CredentialBody;
  const impersonator = new Impersonator({
    subject: email,
    credentials,
    scopes: [
      "https://www.googleapis.com/auth/drive",
      "https://www.googleapis.com/auth/admin.directory.user",
      "https://www.googleapis.com/auth/cloud-platform",
      "https://www.googleapis.com/auth/calendar",
      "https://www.googleapis.com/auth/admin.directory.group",
      "https://www.googleapis.com/auth/admin.reports.usage.readonly",
      "https://www.googleapis.com/auth/admin.directory.user.security",
      "https://www.googleapis.com/auth/admin.directory.device.mobile",
      "https://www.googleapis.com/auth/apps.licensing",
      "https://www.googleapis.com/auth/admin.datatransfer",
    ],
  });
  return impersonator;
};

let impersonatorClient: Impersonator;
const getImpersonatorClient = () => {
  if (impersonatorClient) return impersonatorClient;
  const impersonator = createClient(
    "<EMAIL>",
  );
  impersonatorClient = impersonator;
  return impersonator;
};

let stagingImpersonatorClient: Impersonator | null = null;
const getStagingImpersonatorClient = () => {
  if (stagingImpersonatorClient) return stagingImpersonatorClient;
  const impersonator = createClient("<EMAIL>");
  stagingImpersonatorClient = impersonator;
  return stagingImpersonatorClient;
};

const clients = new Map<string, unknown>();
const getClient = <T>(
  api: string,
  createClientFnc: (impersonator: Impersonator) => T,
) => {
  const key = api;

  if (clients.has(key)) {
    return clients.get(key) as T;
  }

  const impersonator = getImpersonatorClient();

  const client = createClientFnc(impersonator);
  clients.set(key, client);
  return client;
};

export const getImpersonatorSheetsClient = () => {
  return getClient("sheets", (impersonator) => impersonator.getSheetsClient());
};

export const getImpersonatorStorageClient = () => {
  return getClient(
    "storage",
    (impersonator) => impersonator.getStorageClient(),
  );
};

export const getImpersonatorDocsClient = () => {
  return getClient("docs", (impersonator) => impersonator.getDocsClient());
};

export const getImpersonatorDriveClient = () => {
  return getClient("drive", (impersonator) => impersonator.getDriveV3Client());
};

export const getImpersonatorReportClient = () => {
  return getClient(
    "reports",
    (impersonator) => impersonator.getReportsClient(),
  );
};

let googleClient: admin_directory_v1.Admin | null = null;
export const getImpersonatorGoogleClient = (forStagingEnv = false) => {
  if (googleClient) return googleClient;
  const impersonator = forStagingEnv
    ? getStagingImpersonatorClient()
    : getImpersonatorClient();
  googleClient = impersonator.getAdminClient();
  return googleClient;
};

export const getImpersonatorBigQueryClient = () => {
  return getClient(
    "bigquery",
    (impersonator) => impersonator.getBigQueryClient(),
  );
};

export const getImpersonatorSecretManagerClient = () => {
  return getClient(
    "secretmanager",
    (impersonator) => impersonator.getSecretManagerClient(),
  );
};

export const getImpersonatorPubSubClient = () => {
  return getClient("pubsub", (impersonator) => impersonator.getPubSubClient());
};

export const getImpersonatorCalendarClient = () => {
  return getClient(
    "calendar",
    (impersonator) => impersonator.getCalendarClient(),
  );
};

export const getImpersonatorLicensingClient = () => {
  return getClient(
    "licensing",
    (impersonator) => impersonator.getLicensingClient(),
  );
};

export const getImpersonatorDatatransferClient = () => {
  return getClient(
    "datatransfer",
    (impersonator) => impersonator.getDatatransferClient(),
  );
};

export const getImpersonatorResourceManagerClient = () => {
  return getClient(
    "cloudresourcemanager",
    (impersonator) => impersonator.getResourceManagerClient(),
  );
};

export const getImpersonatorCloudAssetsClient = () => {
  return getClient(
    "cloudasset",
    (impersonator) => impersonator.getCloudAssetsClient(),
  );
};

export class Impersonator {
  private auth: Auth.GoogleAuth;
  private credentials: Auth.CredentialBody | undefined;
  private readonly subject: string;
  /**
   * Creates an instance of Impersonator
   * using the service account provided in the credentials
   * to impersonate the provided subject
   * with the provided scopes.
   * @param {{
   *     subject: string;
   *     credentials: object;
   *     scopes: string[];
   *   }} {
   *     subject,
   *     credentials,
   *     scopes,
   *   }
   * @memberof Impersonator
   */
  constructor({
    subject,
    credentials,
    scopes,
  }: {
    subject: string;
    credentials?: Auth.CredentialBody;
    scopes: string[];
  }) {
    this.subject = subject;
    this.credentials = credentials;

    if (credentials) {
      this.auth = new Auth.GoogleAuth({
        credentials,
        scopes,
        clientOptions: {
          subject,
        },
      });
      return this;
    }

    this.auth = new Auth.GoogleAuth({
      scopes,
      clientOptions: {
        subject,
      },
    });
  }
  /**
   * Sets the scopes for the Impersonator.
   *
   * @param {string[]} scopes
   * @memberof Impersonator
   */
  setScopes(scopes: string[]) {
    if (this.credentials) {
      this.auth = new Auth.GoogleAuth({
        scopes,
        clientOptions: {
          subject: this.subject,
        },
        credentials: this.credentials,
      });
      return;
    }

    this.auth = new Auth.GoogleAuth({
      scopes,
      clientOptions: {
        subject: this.subject,
      },
    });
  }
  /**
   * Returns the Auth.GoogleAuth instance.
   *
   * @readonly
   * @type {Auth.GoogleAuth}
   * @memberof Impersonator
   */
  getAuthClient() {
    return this.auth;
  }
  /**
   * Returns an authenticated client for Google Sheets v4.
   *
   * @return {*}  {sheets_v4.Sheets}
   * @memberof Impersonator
   */
  getSheetsClient(): sheets_v4.Sheets {
    return google.sheets({
      auth: this.auth as unknown as sheets_v4.Options["auth"],
      version: "v4",
    });
  }
  /**
   * Returns an authenticated client for Google Storage v1.
   * @return {*}  {storage_v1.Storage}
   * @memberof Impersonator
   */
  getStorageClient(): storage_v1.Storage {
    return google.storage({
      version: "v1",
      auth: this.auth as unknown as storage_v1.Options["auth"],
    });
  }
  /**
   * Returns an authenticated client for Google Docs v1.
   *
   * @return {*}  {docs_v1.Docs}
   * @memberof Impersonator
   */
  getDocsClient(): docs_v1.Docs {
    return google.docs({
      version: "v1",
      auth: this.auth as unknown as docs_v1.Options["auth"],
    });
  }
  /**
   * Returns an authenticated client for Google Admin SDK Directory API.
   *
   * @return {*}  {admin_directory_v1.Admin}
   * @memberof Impersonator
   */
  getAdminClient(): admin_directory_v1.Admin {
    return google.admin({
      version: "directory_v1",
      auth: this.auth as unknown as admin_directory_v1.Options["auth"],
    });
  }

  /**
   * Returns an authenticated client for Google Reports API.
   *
   * @return {*}  {admin_reports_v1.Admin}
   * @memberof Impersonator
   */
  getReportsClient(): admin_reports_v1.Admin {
    return google.admin({
      version: "reports_v1",
      auth: this.auth as unknown as admin_reports_v1.Options["auth"],
    });
  }

  /**
   * Returns an authenticated client for Google Drive v3.
   *
   * @return {*}  {drive_v3.Drive}
   * @memberof Impersonator
   */
  getDriveV3Client(): drive_v3.Drive {
    return google.drive({
      version: "v3",
      auth: this.auth as unknown as drive_v3.Options["auth"],
    });
  }
  /**
   * Returns an authenticated client for BigQuery.
   * @return {*}  {bigquery_v2.Bigquery}
   */
  getBigQueryClient(): bigquery_v2.Bigquery {
    return google.bigquery({
      version: "v2",
      auth: this.auth as unknown as bigquery_v2.Options["auth"],
    });
  }
  /**
   * Returns an authenticated client for Google Calendar.
   * @return {*}  {calendar_v3.Calendar}
   */
  getCalendarClient(): calendar_v3.Calendar {
    return google.calendar({
      version: "v3",
      auth: this.auth as unknown as calendar_v3.Options["auth"],
    });
  }
  getAppsScriptClient(): script_v1.Script {
    return google.script({
      version: "v1",
      auth: this.auth as unknown as script_v1.Options["auth"],
    });
  }
  getSecretManagerClient(): secretmanager_v1.Secretmanager {
    return google.secretmanager({
      version: "v1",
      auth: this.auth as unknown as secretmanager_v1.Options["auth"],
    });
  }
  getPubSubClient(): pubsub_v1.Pubsub {
    return google.pubsub({
      version: "v1",
      auth: this.auth as unknown as pubsub_v1.Options["auth"],
    });
  }
  getLicensingClient(): licensing_v1.Licensing {
    return google.licensing({
      version: "v1",
      auth: this.auth as unknown as licensing_v1.Options["auth"],
    });
  }
  getDatatransferClient(): admin_datatransfer_v1.Admin {
    return google.admin({
      version: "datatransfer_v1",
      auth: this.auth as unknown as admin_datatransfer_v1.Options["auth"],
    });
  }
  getResourceManagerClient(): cloudresourcemanager_v3.Cloudresourcemanager {
    return google.cloudresourcemanager({
      version: "v3",
      auth: this.auth as unknown as cloudresourcemanager_v3.Options["auth"],
    });
  }
  getCloudAssetsClient(): cloudasset_v1.Cloudasset {
    return google.cloudasset({
      version: "v1",
      auth: this.auth as unknown as cloudasset_v1.Options["auth"],
    });
  }
}
