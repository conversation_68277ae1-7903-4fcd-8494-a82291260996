import { stringSimilarity } from "string-similarity-js";

/**
 * Calculates the consensus (average similarity) of each value in an array compared to all other values in the array.
 * @param values - An array of values to calculate consensus for. Each value is converted to a string using its `toString` method, or an empty string if `toString` is not available.
 * @returns An array of numbers representing the average similarity of each value to all other values in the array.
 * @remarks This function uses the `stringSimilarity` function from the `string-similarity-js` package to compute the similarity between strings.
 * @example
 * ```typescript
 * import { calculateArrayConsensus } from './calculateArrayConsensus';
 * const values = ["apple", "apples", "banana"];
 * const consensus = calculateArrayConsensus(values);
 * Logger.info(consensus); // Example output: [0.8, 0.75, 0.0]
 * ```
 */
export const calculateArrayConsensus = (values: unknown[]) => {
  if (values.length === 0) {
    return [];
  }
  if (values.length === 1) {
    return [1]; // No other values to compare
  }
  const stringifiedValues = values.map((value) => value?.toString?.() ?? "");
  const consensus = stringifiedValues.map((value, index) => {
    const otherValues = stringifiedValues.filter((_, i) => i !== index);
    const similarities = otherValues.map((otherValue) => {
      const identical = value === otherValue;
      if (identical) return 1; // Returning early so that we don't have to deal with potential issues with edge cases of stringSimlarity, f.i. two empty string might return 0 (as minimum length of comparison is 1), but we want to return 1 in this case as they match. Also, it's just convenient...and a very slight performance optimization.
      const similarity = stringSimilarity(value, otherValue, 1); // setting threshold of length to 1 to get exact similarity even for strings of 1 character}
      return similarity;
    });
    const averageSimilarity = similarities.reduce((acc, similarity) => acc + similarity, 0) / similarities.length;
    return averageSimilarity;
  });
  return consensus;
};
