import { assertEquals, assertNotEquals } from "@std/assert";
import { consolidateExtractionResults } from "./consolidateExtractionResults.ts";
import { TypeOf, z } from "zod";

const testConsolidateExtractionResultsSchema = (options: {
  resultSchema: z.ZodSchema;
  confidenceSchema: z.ZodSchema;
  extractionResults: Record<string, unknown>[];
}) => {
  // arrange
  const { resultSchema, confidenceSchema, extractionResults } = options;

  // act
  const { result, confidence } = consolidateExtractionResults(extractionResults);

  // assert
  resultSchema.parse(result);
  confidenceSchema.parse(confidence);
};

Deno.test("consolidateExtractionResults should return an object in the correct shape", async (t) => {
  await t.step(`when provided 0 elements`, () => {
    // arrange
    const resultSchema = z.object({});
    const confidenceSchema = z.object({});
    const extractionResults = Array.from<z.infer<typeof resultSchema>>({ length: 0 });

    // act & assert
    testConsolidateExtractionResultsSchema({ resultSchema, confidenceSchema, extractionResults });
  });

  const arrayLength = 5;
  for (let i = 1; i < arrayLength; i++) {
    await t.step(`when provided ${i} elements`, () => {
      // arrange
      const resultSchema = z.object({
        name: z.string(),
        age: z.number(),
        domicile: z.object({
          city: z.string(),
          country: z.string(),
        }),
      });
      const confidenceSchema = z.object({
        name: z.number(),
        age: z.number(),
      });
      const extractionResults = Array.from<z.infer<typeof resultSchema>>({ length: i }).fill({
        name: "John",
        age: 30,
        domicile: {
          city: "New York",
          country: "USA",
        },
      });

      // act & assert
      testConsolidateExtractionResultsSchema({ resultSchema, confidenceSchema, extractionResults });
    });
  }
});

Deno.test("consolidateExtractionResults should return the first object when provided with only 2 elements, confidence should be 0", async (t) => {
  // arrange
  const resultSchema = z.object({
    name: z.string(),
    age: z.number(),
    domicile: z.object({
      city: z.string(),
      country: z.string(),
    }),
  });
  const extractionWithConfidence = {
    name: "John",
    age: 12,
    domicile: {
      city: "abc",
      country: "abc",
    },
  };
  const extractionWithoutConfidence = {
    name: "Gary",
    age: 34,
    domicile: {
      city: "def",
      country: "def",
    },
  };
  const expectedConfidence = {
    name: 0,
    age: 0,
    domicile: {
      city: 0,
      country: 0,
    },
  };

  // act
  const extractionResults = [extractionWithConfidence, extractionWithoutConfidence];
  const { result, confidence } = consolidateExtractionResults<z.infer<typeof resultSchema>>(extractionResults);

  // assert
  await t.step("result should be the first object", () => {
    assertEquals(result, extractionWithConfidence);
    assertNotEquals(result, extractionWithoutConfidence);
  });
  await t.step("confidence should be an object with the same properties as the first object", () => {
    assertEquals(confidence, expectedConfidence);
  });
});

Deno.test("consolidateExtractionResults should return an object with the correct values", async (t) => {
  // arrange
  const extractionWithConfidence = {
    name: "John",
    age: 12,
    domicile: {
      city: "ab",
      country: "ab",
    },
  } as const;
  const extractionWithoutConfidence = {
    name: "Gary",
    age: 34,
    domicile: {
      city: "cd",
      country: "cd",
    },
  };
  const arrayLength = 10;
  const minNumberOfConfidenceExtraction = 2;
  for (let i = minNumberOfConfidenceExtraction; i < arrayLength; i++) {
    await t.step(`when provided ${i} matching elements`, () => {
      // arrange
      const extractionResults = Array.from<typeof extractionWithConfidence | typeof extractionWithoutConfidence>({ length: i }).fill(extractionWithConfidence);
      extractionResults.push(extractionWithoutConfidence);

      // the expected value is proportional to the number of matching elements
      const expectedConfidence = Number(((i - 1) / i).toFixed(2));
      const confidenceValueSchema = z.literal(expectedConfidence);
      const confidenceSchema = z.object({
        name: confidenceValueSchema,
        age: confidenceValueSchema,
        domicile: z.object({
          city: confidenceValueSchema,
          country: confidenceValueSchema,
        }),
      });

      // act
      const { result, confidence } = consolidateExtractionResults(extractionResults);

      // assert
      assertEquals(result, extractionWithConfidence);
      assertNotEquals(result, extractionWithoutConfidence);
      confidenceSchema.parse(confidence);
    });
  }
});
