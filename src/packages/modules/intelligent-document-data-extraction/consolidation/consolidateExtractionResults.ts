import { flatten, unflatten } from "flat";
import { getHighestConsensusElement } from "./getHighestConsensusElement.ts";
import { IntelligentDocumentDataExtraction } from "modules/intelligent-document-data-extraction/index.ts";
import { type _ConfidenceResult } from "modules/intelligent-document-data-extraction/types.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Consolidates multiple extraction results into a single result and calculates a confidence score for each property.
 * @param extractionResults - An array of extraction results to be consolidated. Each result is expected to be an object.
 *
 * @returns An object containing:
 * - `result`: The consolidated extraction result, where each property is determined based on the highest confidence.
 * - `confidence`: An object of the same structure as the result, where each property contains the confidence score (a number between 0 and 1, rounded to 2 decimals) for the corresponding property in the result.
 *
 * @remarks
 * - The function flattens the input objects to process their properties, calculates confidence scores based on string similarity,
 *   and then reconstructs the objects to their original structure.
 * - The confidence score for a property is calculated as the average similarity of its value with other values for the same property across all input objects.
 * - The property value with the highest confidence score is selected for the consolidated result.
 *
 * @example
 * ```typescript
 * const extractionResults = [
 *   { name: "<PERSON>", age: 30 },
 *   { name: "<PERSON>", age: 30 },
 *   { name: "John", age: 31 }
 * ];
 *
 * const { result, confidence } = consolidateExtractionResults(extractionResults);
 *
 * Logger.info(result); // { name: "John", age: 30 }
 * Logger.info(confidence); // { name: 0.95, age: 0.67 }
 * ```
 */
export const consolidateExtractionResults = <T extends object>(extractionResults: T[]): {
  result: T;
  confidence: IntelligentDocumentDataExtraction.ConfidenceResult<T>;
} => {
  Logger.info(`Consolidating extraction results...`);
  const flattenedResults = extractionResults.map((result) => flatten(result) as Record<string, unknown>);

  const allProperties = flattenedResults.map((result) => Object.keys(result)).flat();
  const uniqueProperties = Array.from(new Set(allProperties));

  const table = uniqueProperties.map((property) => {
    const row = flattenedResults.map((result) => result[property]);
    return [property, ...row];
  });

  const confidenceCalculations: [string, unknown, number][] = table.map((row) => {
    const [property, ...values] = row;
    const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);
    return [property as string, valueWithHighestConsensus, Number(highestConsensus.toFixed(2))];
  });

  const resultTable = confidenceCalculations.map(([property, value]) => {
    return [property, value];
  });
  const result = unflatten(Object.fromEntries(resultTable)) as T;
  const confidenceTable = confidenceCalculations.map(([property, , confidence]) => {
    return [property, confidence];
  });
  const confidence = unflatten(Object.fromEntries(confidenceTable)) as IntelligentDocumentDataExtraction.ConfidenceResult<T>;
  const consolidatedData = {
    result,
    confidence,
  };
  Logger.debug({ consolidatedData });
  return consolidatedData;
};
