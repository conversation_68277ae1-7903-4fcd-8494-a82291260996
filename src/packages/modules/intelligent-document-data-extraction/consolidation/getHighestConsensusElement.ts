import { calculateArrayConsensus } from "./calculateArrayConsensus.ts";

/**
 * Determines the element with the highest consensus from an array of values.
 *
 * @param values - An array of values to evaluate for consensus.
 * @returns An object containing:
 * - `valueWithHighestConsensus`: The value from the input array that has the highest consensus, or the first element if there is a tie.
 * - `highestConsensus`: The numerical value representing the highest consensus score.
 */
export const getHighestConsensusElement = (values: unknown[]) => {
  const consensus = calculateArrayConsensus(values);
  const highestConsensusIndex = consensus.reduce((acc, value, index, arr) => {
    const isCurrentHighest = value > arr[acc];
    return isCurrentHighest ? index : acc;
  }, 0);
  const highestConsensus = consensus[highestConsensusIndex];
  const valueWithHighestConsensus = values[highestConsensusIndex];
  return { valueWithHighestConsensus, highestConsensus };
};
