import { assertEquals } from "@std/assert";
import { calculateArrayConsensus } from "./calculateArrayConsensus.ts";

const testCalculateArrayConsensus = (values: unknown[], expectedConsensus: number[]) => {
  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus, expectedConsensus);
};

Deno.test("calculateArrayConsensus should calculate max consensus for identical elements", async (test) => {
  // arrange
  const arrayLength = 10;
  const mockValues = ["apple", 1, 0, new Date()];
  for (const mockValue of mockValues) {
    for (let i = 0; i < arrayLength; i++) {
      await test.step(`Test with ${i + 1} elements`, () => {
        // arrange
        const values = Array.from({ length: arrayLength }).fill(mockValue);

        // act & assert
        testCalculateArrayConsensus(values, Array.from({ length: arrayLength }).fill(1) as number[]);
      });
    }
  }
});

Deno.test("calculateArrayConsensus should calculate min consensus for 0-9 numbers", async (test) => {
  // arrange
  const arrayLength = 10;
  for (let i = 0; i < arrayLength; i++) {
    await test.step(`Test with ${i + 1} elements`, () => {
      // arrange
      const values = Array.from({ length: arrayLength }).map((_, index) => index);

      // act & assert
      testCalculateArrayConsensus(values, Array.from({ length: arrayLength }).fill(0) as number[]);
    });
  }
});

Deno.test("calculateArrayConsensus should calculate consensus for similar strings", () => {
  // arrange
  const values = ["apple", "apples", "appl"];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus.length, 3);
  assertEquals(consensus.every((value) => value > 0.8), true); // High similarity
});

Deno.test("calculateArrayConsensus should calculate consensus for different strings", () => {
  // arrange
  const values = ["apple", "banana", "cherry"];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus.length, 3);
  assertEquals(consensus.every((value) => value < 0.5), true); // Low similarity
});

Deno.test("calculateArrayConsensus should handle mixed types", () => {
  // arrange
  const values = ["banana", 123, true];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus.length, 3);
  assertEquals(consensus.every((value) => value === 0), true);
});

Deno.test("calculateArrayConsensus should handle empty array", () => {
  // arrange
  const values: unknown[] = [];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus, []);
});

Deno.test("calculateArrayConsensus should handle array with one element", () => {
  // arrange
  const values = ["apple"];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus, [1]); // No other values to compare
});

Deno.test("calculateArrayConsensus should handle empty strings", () => {
  // arrange
  const values = ["", "", ""];

  // act
  const consensus = calculateArrayConsensus(values);

  // assert
  assertEquals(consensus.length, 3);
  assertEquals(consensus.every((value) => value === 1), true);
});
