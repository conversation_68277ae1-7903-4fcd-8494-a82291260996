import { assertEquals } from "@std/assert";
import { getHighestConsensusElement } from "./getHighestConsensusElement.ts";

const testHighestConsensus = (values: unknown[], expectedValue: unknown, expectedConsensus: number) => {
  // act
  const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);

  // assert
  assertEquals(valueWithHighestConsensus, expectedValue);
  assertEquals(highestConsensus, expectedConsensus);
};

Deno.test("getHighestConsensusElement should return max for N identical elements", async (t) => {
  // arrange
  const mockValue = "apple";
  const arrayLength = 10;
  for (let i = 0; i < arrayLength; i++) {
    await t.step(`Test with ${i + 1} elements`, () => {
      // arrange
      const values = Array.from({ length: arrayLength }).fill(mockValue);

      // act & assert
      testHighestConsensus(values, mockValue, 1);
    });
  }
});

Deno.test("getHighestConsensusElement should return 0 for N completely different elements, returning the first element", async (t) => {
  const arrayLength = 10;
  for (let i = 0; i < arrayLength; i++) {
    await t.step(`Test with ${i + 1} elements`, () => {
      // arrange
      const values = Array.from({ length: arrayLength }).map((_, index) => index);

      // act & assert
      testHighestConsensus(values, values[0], 0);
    });
  }
});

Deno.test("getHighestConsensusElement should handle an empty array", () => {
  // arrange
  const values: unknown[] = [];

  // act
  const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);

  // assert
  assertEquals(valueWithHighestConsensus, undefined);
  assertEquals(highestConsensus, undefined);
});

Deno.test("getHighestConsensusElement should handle an array with one element", () => {
  // arrange
  const values = ["single"];

  // act
  const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);

  // assert
  assertEquals(valueWithHighestConsensus, "single");
  assertEquals(highestConsensus, 1);
});

Deno.test("getHighestConsensusElement should handle an array with multiple elements and a clear consensus", () => {
  // arrange
  const values = ["banana", "apple", "apple", "apple", "banana"];

  // act
  const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);

  // assert
  assertEquals(valueWithHighestConsensus, "apple");
  assertEquals(highestConsensus > 0.5, true);
});

Deno.test("getHighestConsensusElement should handle an array with a tie in consensus", () => {
  // arrange
  const values = ["apple", "banana", "apple", "banana"];

  // act
  const { valueWithHighestConsensus, highestConsensus } = getHighestConsensusElement(values);

  // assert
  assertEquals(valueWithHighestConsensus, "apple"); // Assuming it picks the first tied element
  assertEquals(highestConsensus > 0.45, true);
});
