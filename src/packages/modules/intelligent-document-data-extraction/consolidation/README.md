# Consolidation utility functions

The functions contained in this folder are instrumental towards a reasoned and structured consolidation of various extraction results.

## Confidence (consensus based)

We calculate confidence based on the consensus between the results of different extractions.

To calculate such consensus, we want to choose the value that has the most agreement between extractions:

1. for each property we compare the values from each extraction by string similarity (a value between 0 and 1);
2. we then calculate the `consensus`, the average of such similarities (0~1 value);
3. we then choose for each property the element with the highest consensus; note: the highest consensus is going to represent the consensus for the property;
4. we then aggregate results and consensus into an object.

For more information regarding implementation, please refer to the jsdocs of each function.

### Calculating consensus

We start with an array of json objects that have the same structure:

```ts
[
  {
    invoice: {
      number: "A123",
      due_date: "2022-01-01",
    },
  },
  {
    invoice: {
      number: "A-123",
      due_date: "2022-01-01",
    },
  },
  {
    invoice: {
      number: "A-123",
      due_date: "2022-01-01",
    },
  },
];
```

We then turn this object into a table by simply flattening the objects, where each row corresponds to a property, and in each column is the value of the corresponding object.

> Note: the list of properties is a union of the properties of all the objects (unique values).

With this table we can compare values in the same rows by similarity in order to get the level of consensus between the values.

Table example:

| Property       | Column 1   | Column 2   | Column 3   |
| -------------- | ---------- | ---------- | ---------- |
| invoice.number | A123       | A-123      | A-123      |
| due_date       | 2022-01-01 | 2022-01-01 | 2022-01-01 |

The final output we're expecting from the function is something like this (NOTE: this is just an example, refer to docs for proper signatures):

```ts
{
  consolidated:{
    invoice:{
        number: "A-123",
    }
  },
  confidence:{
    invoice:{
        number:1.0,
    }
  },
}
```

## Test

An extensive set of tests are available, they can be all run using this command:

```bash
deno test src/packages/modules/intelligent-document-data-extraction/consolidation/allUnit.test.ts
```

> Tests are definitely improvable, in particular when "high" and "low" consensus are being evaluated, although they're not more strictly defined. This is due to the fact that research and understanding of the "consolidation" logics are ongoing, and this first implementation serves as a foundation for the subsequent work.
