import { _MODEL_NAMES_MAP, _ModelNameSchema, _PROVIDERS } from "./config/_MODELS.ts";
import { z, ZodSchema } from "zod";
import { IntelligentDocumentDataExtraction } from "modules/intelligent-document-data-extraction/index.ts";

export type _ProviderName = typeof _PROVIDERS[number];
export type _ModelName = keyof typeof _MODEL_NAMES_MAP;
export type NestedCopy<T, R extends unknown> = {
  [K in keyof T]: T[K] extends (string | null) ? R : NestedCopy<T[K], R>;
};
export type _ConfidenceResult<T> = NestedCopy<T, number>;
export type _ConsolidatedResult<T extends ZodSchema> = {
  consolidated: z.infer<T>;
  confidence: _ConfidenceResult<z.infer<T>>;
  extractionResults: Record<_ModelName, IntelligentDocumentDataExtraction.ExtractionDataResponse<T>>;
};

export type _ExtractionDataRequest<T extends ZodSchema> = {
  prompt: string;
  blob: Blob;
  modelName: _ModelName;
  responseSchema: T;
};
export type _ExtractionDataResponse<T extends ZodSchema> = {
  data: z.infer<T>;
  modelName: _ModelName;
  extractionId: string;
  extractionTimeInSeconds: string;
};

export type _ExtractionConsolidatedDataRequest<T extends ZodSchema> = {
  prompt: string;
  blob: Blob;
  modelNames: _ModelName[];
  responseSchema: T;
};
