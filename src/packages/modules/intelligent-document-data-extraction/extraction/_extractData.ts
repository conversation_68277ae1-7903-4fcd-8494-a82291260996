import { ZodSchema } from "zod";
import { IntelligentDocumentDataExtraction } from "modules/intelligent-document-data-extraction/index.ts";
import { _extractGemini } from "./providers/_extractGemini.ts";
import { _extractOpenAI } from "./providers/_extractOpenAI.ts";
import { _ProviderName } from "modules/intelligent-document-data-extraction/types.ts";
import { Logger } from "modules/logger/index.ts";
import { _getProviderFunction } from "modules/intelligent-document-data-extraction/extraction/providers/_getProviderFunction.ts";

export const _extractData = async <T extends ZodSchema>(options: IntelligentDocumentDataExtraction.ExtractionDataRequest<T>): Promise<IntelligentDocumentDataExtraction.ExtractionDataResponse<T>> => {
  const { prompt, blob, modelName, responseSchema } = options;

  const extractionId = crypto.randomUUID();

  Logger.info(`Extracting with model: ${modelName} ${extractionId}`);
  const startTime = performance.now();

  const providerFunction = _getProviderFunction(modelName);

  const result = await providerFunction({
    prompt,
    blob,
    modelName,
    responseSchema,
  });

  Logger.info({ result });

  const endTime = performance.now();
  const extractionTimeInSeconds = ((endTime - startTime) / 1000).toFixed(2);
  Logger.info(`Extraction time: ${extractionTimeInSeconds} seconds`);

  const content = {
    data: result,
    modelName,
    extractionId,
    extractionTimeInSeconds,
  };

  Logger.debug({ content });
  Logger.info(`Extracted data with model: ${modelName}`);

  return content;
};
