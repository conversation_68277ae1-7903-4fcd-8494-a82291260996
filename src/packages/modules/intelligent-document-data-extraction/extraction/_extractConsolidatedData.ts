import { z, ZodSchema } from "zod";
import { consolidateExtractionResults } from "modules/intelligent-document-data-extraction/consolidation/consolidateExtractionResults.ts";
import { _extractData } from "modules/intelligent-document-data-extraction/extraction/_extractData.ts";
import { _ConfidenceResult, _ConsolidatedResult } from "../types.ts";
import { IntelligentDocumentDataExtraction } from "modules/intelligent-document-data-extraction/index.ts";
import { Logger } from "modules/logger/index.ts";

export const _extractConsolidatedData = async <T extends ZodSchema>(options: IntelligentDocumentDataExtraction.ExtractionConsolidatedDataRequest<T>): Promise<IntelligentDocumentDataExtraction.ConsolidatedResult<T>> => {
  Logger.info(`Extracting and consolidating data from ${options.modelNames.length} models`);
  const extractionResults = await Promise.all(
    options.modelNames.map(async (modelName) => {
      return _extractData({
        prompt: options.prompt,
        blob: options.blob,
        modelName: modelName,
        responseSchema: options.responseSchema,
      });
    }),
  );
  const consolidatedData = consolidateExtractionResults<z.infer<T>>(extractionResults.map((result) => result.data));
  const extractionResultsStructured = extractionResults.reduce((acc, result) => {
    acc[result.modelName] = result;
    return acc;
  }, {} as Record<IntelligentDocumentDataExtraction.ModelName, IntelligentDocumentDataExtraction.ExtractionDataResponse<T>>);
  const result = {
    consolidated: consolidatedData.result,
    confidence: consolidatedData.confidence,
    extractionResults: extractionResultsStructured,
  };
  Logger.info(`Successfully extracted and consolidated data from ${options.modelNames.length} models`);
  return result;
};
