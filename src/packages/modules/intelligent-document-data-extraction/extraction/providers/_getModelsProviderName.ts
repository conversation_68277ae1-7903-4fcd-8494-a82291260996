import { _MODEL_NAMES_MAP } from "modules/intelligent-document-data-extraction/config/_MODELS.ts";
import { _ModelName, _ProviderName } from "modules/intelligent-document-data-extraction/types.ts";
import { Logger } from "modules/logger/index.ts";

export const _getModelsProviderName = (modelName: _ModelName) => {
  const providerName = modelName.split(".")[0] as _ProviderName;
  Logger.debug({ modelName, providerName });
  return providerName;
};
