import { z, ZodSchema } from "zod";
import { _ModelName } from "modules/intelligent-document-data-extraction/types.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { Logger } from "modules/logger/index.ts";
import { OpenAI } from "jsr:@openai/openai";
import { zodTextFormat } from "jsr:@openai/openai/helpers/zod";
import { encodeBase64 } from "jsr:@std/encoding/base64";
import { _OPENAI_MODEL_NAMES } from "modules/intelligent-document-data-extraction/config/_MODELS.ts";

checkEnvVars(["OPENAI_API_KEY"]);

type OpenAIModelKey = keyof typeof _OPENAI_MODEL_NAMES;
type OpenAIModelName = typeof _OPENAI_MODEL_NAMES[OpenAIModelKey];

export const _extractOpenAI = async (options: {
  prompt: string;
  blob: Blob;
  modelName: _ModelName;
  responseSchema: ZodSchema;
}) => {
  const { prompt, blob, modelName, responseSchema } = options;
  const client = new OpenAI();

  const base64String = encodeBase64(await blob.arrayBuffer());

  const modelId = _OPENAI_MODEL_NAMES[modelName as OpenAIModelKey] as OpenAIModelName;

  const response = await client.responses.create({
    model: modelId,
    input: [
      {
        role: "user",
        content: prompt,
      },
      {
        role: "user",
        content: [
          {
            type: "input_file",
            filename: "invoice.pdf",
            file_data: `data:application/pdf;base64,${base64String}`,
          },
        ],
      },
    ],
    text: {
      format: zodTextFormat(responseSchema as unknown as Parameters<typeof zodTextFormat>[0], "responseSchema"),
    },
  });

  const text = response.output_text as string;

  Logger.info(text);

  const json = JSON.parse(text);
  return json as z.infer<typeof responseSchema>;
};
