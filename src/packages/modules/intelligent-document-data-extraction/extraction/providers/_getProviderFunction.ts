import { _MODEL_NAMES_MAP } from "modules/intelligent-document-data-extraction/config/_MODELS.ts";
import { _ModelName, _ProviderName } from "modules/intelligent-document-data-extraction/types.ts";
import { _extractGemini } from "./_extractGemini.ts";
import { _extractOpenAI } from "modules/intelligent-document-data-extraction/extraction/providers/_extractOpenAI.ts";
import { _getModelsProviderName } from "modules/intelligent-document-data-extraction/extraction/providers/_getModelsProviderName.ts";

const PROVIDER_FUNCTIONS_MAP = {
  "OPENAI_GPT": _extractOpenAI,
  "GOOGLE_GEMINI": _extractGemini,
} as const;

export const _getProviderFunction = (modelName: _ModelName) => {
  const providerName = _getModelsProviderName(modelName);
  const providerFunction = PROVIDER_FUNCTIONS_MAP[providerName];
  if (!providerFunction) {
    throw new Error(`No matching provider found for model: ${modelName}`);
  }
  return providerFunction;
};
