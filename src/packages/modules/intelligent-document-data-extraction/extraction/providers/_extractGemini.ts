import { z, ZodSchema } from "zod";
import { _ModelName, _ProviderName } from "modules/intelligent-document-data-extraction/types.ts";
import { createGoogleGenerativeAI } from "@ai-sdk/google";
import { generateObject } from "ai";
import { _GEMINI_MODEL_NAMES } from "modules/intelligent-document-data-extraction/config/_MODELS.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { _getModelsProviderName } from "modules/intelligent-document-data-extraction/extraction/providers/_getModelsProviderName.ts";

checkEnvVars(["GOOGLE_GENERATIVE_AI_API_KEY"]);

type GeminiModelKey = keyof typeof _GEMINI_MODEL_NAMES;
type GeminiModelName = typeof _GEMINI_MODEL_NAMES[GeminiModelKey];

export const _extractGemini = async (options: {
  prompt: string;
  blob: Blob;
  modelName: _ModelName;
  responseSchema: ZodSchema;
}) => {
  const { prompt, blob, modelName, responseSchema } = options;

  const modelId = _GEMINI_MODEL_NAMES[modelName as GeminiModelKey] as GeminiModelName;

  const provider = createGoogleGenerativeAI();
  const model = provider(modelId);

  const result = await generateObject<z.infer<typeof responseSchema>>({
    model,
    schema: responseSchema,
    messages: [
      {
        role: "user",
        content: [
          {
            type: "text",
            text: prompt,
          },
          {
            type: "file",
            data: await blob.arrayBuffer(),
            mimeType: blob.type,
          },
        ],
      },
    ],
  });
  return result.object as z.infer<typeof responseSchema>;
};
