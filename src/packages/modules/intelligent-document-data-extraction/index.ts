import { _MODEL_NAMES_MAP, _ModelNameSchema } from "./config/_MODELS.ts";
import { _Template, _TemplateName, _TemplateNameSchema, _TEMPLATES } from "./config/_TEMPLATES.ts";
import { _ConfidenceResult, _ConsolidatedResult, _ExtractionConsolidatedDataRequest, _ExtractionDataRequest, _ExtractionDataResponse, _ModelName } from "modules/intelligent-document-data-extraction/types.ts";
import { _extractData } from "./extraction/_extractData.ts";
import { _extractConsolidatedData } from "modules/intelligent-document-data-extraction/extraction/_extractConsolidatedData.ts";
import { ZodSchema } from "zod";

/**
 * A module for extracting data from documents using Generative AI.
 * It offers a function to extract data from a blob using a specific model.
 *
 * It also provides an enum `MODELS` with all the models that can be used for extracting data from documents.
 */
export namespace IntelligentDocumentDataExtraction {
  /**
   * An enum of models that can be used for extracting data from documents.
   * @example
   * const model = IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.FLASH.LATEST;
   */
  export const MODELS = _MODEL_NAMES_MAP;
  export const ModelNameSchema = _ModelNameSchema;
  export type ModelName = _ModelName;
  export const TEMPLATES = _TEMPLATES;
  export const TemplateNameSchema = _TemplateNameSchema;
  export type TemplateName = _TemplateName;
  export type Template = _Template;

  /**
   * Extracts data from a blob using a specific model.
   * @param options The options object
   * @param options.prompt The prompt to be used for the extraction
   * @param options.blob The blob to be extracted
   * @param options.modelName The name of the model to be used for the extraction
   * @param options.responseSchema The schema to validate the response against
   * @returns An object with the extracted data, the model name and an extraction id
   * @example
   * const data = await IntelligentDocumentDataExtraction.extractData({
   *   prompt: "From this invoice extract the data matching the provided schema",
   *   blob: new Blob([invoice]),
   *   modelName: IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.FLASH.LATEST,
   *   responseSchema: invoiceOutputSchema
   })
   */
  export const extractData = _extractData;
  export type ExtractionDataRequest<T extends ZodSchema> = _ExtractionDataRequest<T>;
  export type ExtractionDataResponse<T extends ZodSchema> = _ExtractionDataResponse<T>;

  /**
   * Extracts data from a blob using a specific model.
   * @param options The options object
   * @param options.prompt The prompt to be used for the extraction
   * @param options.blob The blob to be extracted
   * @param options.modelNames The names of the models to be used for the extraction
   * @param options.responseSchema The schema to validate the response against
   * @returns An object with the extracted data, the model name and an extraction id
   * @example
   * const data = await IntelligentDocumentDataExtraction.extractData({
   *   prompt: "From this invoice extract the data matching the provided schema",
   *   blob: new Blob([invoice]),
   *   modelName: IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.FLASH.LATEST,
   *   responseSchema: invoiceOutputSchema
   })
   */
  export const extractConsolidatedData = _extractConsolidatedData;
  export type ExtractionConsolidatedDataRequest<T extends ZodSchema> = _ExtractionConsolidatedDataRequest<T>;
  export type ConfidenceResult<T> = _ConfidenceResult<T>;
  export type ConsolidatedResult<T extends ZodSchema> = _ConsolidatedResult<T>;
}
