# Intelligent Document Data Extraction

This module provides functionality for extracting structured data from documents using AI and consolidates results using similarity algorithms.

## Templates in the Intelligent Document Data Extraction Module

The templates in this module are structured as namespaces, each representing a specific type of document or data extraction process. Each namespace contains:

- A Zod schema that defines the structure and validation rules for the extracted data.
- A template object that includes:
  1. A prompt to guide the AI model on how to extract the data.
  2. The schema to validate the extracted data.

This design ensures modularity, reusability, and clarity for different document types. At the moment we use templates for two different processes.

### Current templates

- `_InvoiceV1`: Used for extracting data from invoices in the Trade Finance process. Please, refer to [here](./config/templates/_InvoiceV1.ts)

- `_ProfitAndLossV1`: Used for extracting data from financial statements, specifically from the Profit and Loss section. Please refer to [here](./config/templates/_ProfitAndLossV1.ts)

### Future plans

- To handle financial statements, we will have templates for different countries and types of documents. This is due to the fact that the document types of each country differ greatly and different templates will be used. Therefore, the following apply: Each country has its own subfolder, containing templates for analyzing two types of financial documents:
  - Profit and Loss Statements (`_ProfitAndLossV1`).
  - Balance Sheets (`_BalanceSheetV1`).

Next steps:

- Adding `_BalanceSheetV1` for Poland: The structure is already in place to include this template for analyzing balance sheets in Polish financial statements.
- Expanding to Other Countries: The modular design allows us to easily add subfolders for new countries. Each country will have its own `_ProfitAndLossV1` and `_BalanceSheetV1` templates, tailored to the specific structure of its financial documents.

## Extraction and consolidation

In short, extraction is delegated to AI models; we're using an useful wrapper library provided by Vercel that standardises inputs and outputs from different ai providers (currently only Google, other providers - work in progress).

Regarding consolidation, please refer to this [readme](./consolidation/README.md);

## Usage

To use the `IntelligentDocumentDataExtraction` module, ensure you have the required environment variables set up and follow the examples below.

### Example: Extracting Data from a Document

```typescript
import { z } from "zod";
import { IntelligentDocumentDataExtraction } from "modules/intelligent-document-data-extraction";

const invoiceOutputSchema = z.object({
  invoiceNumber: z.string(),
  date: z.string(),
  totalAmount: z.number(),
});

const extractInvoiceData = async () => {
  const blob = new Blob([/* your document data */], { type: "application/pdf" });

  const data = await IntelligentDocumentDataExtraction.extractData({
    prompt: "Extract invoice details matching the schema.",
    blob,
    modelName: IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.FLASH.LATEST,
    responseSchema: invoiceOutputSchema,
  });

  Logger.info("Extracted Data:", data);
};

extractInvoiceData();
```

### Example: Consolidating Results from Multiple Models

```typescript
const consolidateInvoiceData = async () => {
  const blob = new Blob([/* your document data */], { type: "application/pdf" });

  const consolidatedData = await IntelligentDocumentDataExtraction.extractDataConsolidated({
    prompt: "Extract invoice details matching the schema.",
    blob,
    modelNames: [
      IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.FLASH.LATEST,
      IntelligentDocumentDataExtraction.MODELS.GOOGLE_GEMINI.PRO.STABLE,
    ],
    responseSchema: invoiceOutputSchema,
  });

  Logger.info("Consolidated Data:", consolidatedData);
};

consolidateInvoiceData();
```
