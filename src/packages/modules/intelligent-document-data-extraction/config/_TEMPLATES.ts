import z from "zod";
import { typedObjectKeys } from "modules/typed-object/index.ts";
import { _InvoiceV1 } from "./templates/_InvoiceV1.ts";
import { _ProfitAndLossV1 } from "./templates/_ProfitAndLossV1.ts";

export const _TEMPLATES = {
  INVOICE_V1: _InvoiceV1.template,
  PROFITANDLOSS_V1: _ProfitAndLossV1.template,
} as const;

export const [first, ...promptTemplateNames] = typedObjectKeys(_TEMPLATES);

export const _TemplateNameSchema = z.enum([first, ...promptTemplateNames]);
export type _TemplateName = z.infer<typeof _TemplateNameSchema>;
export type _Template = typeof _TEMPLATES[_TemplateName];
