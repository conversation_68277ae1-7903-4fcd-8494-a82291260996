import { z } from "zod";

export namespace _ProfitAndLossV1 {
  export const profitAndLossSchema = z.object({
    turnover: z.number().nullable().optional().describe("Total amount of sales / revenue."),
    costOfGoodsSold: z
      .number()
      .nullable()
      .optional()
      .describe(
        "Direct costs associated with the production of goods sold. Calculated as the sum of 'Consumption of materials' and 'Value of sold goods and materials', or any relevant fields.",
      ),
    personnelExpenses: z.number().nullable().optional().describe("Costs related to employees (salaries, social charges, etc.)."),

    administrativeExpenses: z
      .number()
      .nullable()
      .optional()
      .describe(
        "Extracted from the field 'General administration costs' or a similar label. If not present, it is calculated as the sum of 'Outsourced services', 'Taxes and fees', and 'Other costs by nature', if available.",
      ),
    sellingCosts: z.number().nullable().optional().describe("Expenses related to the marketing and sale of products/services."),
    depreciation: z.number().nullable().optional().describe("Typically extracted from fields labeled as 'Depreciation' or similar in the document."),
    amortisation: z.number().nullable().optional().describe("Typically extracted from fields labeled as 'Amortisation' or similar in the document."),
    interestReceivablesIncome: z.number().nullable().optional().describe("Income earned from financial investments or loans granted."),
    interestPayablesExpenses: z.number().nullable().optional().describe("Expenses incurred due to loans received or financial debts."),
    totalOtherFinancialIncome: z
      .number()
      .nullable()
      .optional()
      .describe(
        "Typically calculated as the sum of the field labeled as 'Other operating income' and 'Financial income'. And then substract the interest from financial income section.",
      ),

    totalOtherFinancialIncomeExpenses: z
      .number()
      .nullable()
      .optional()
      .describe(
        "Total financial expenses and other operating expenses. Calculated as the sum of fields labeled as 'Other operating expenses' and 'Financial costs' or similar in the document. And then substract the interest from financial costs section.",
      ),
    extraordinaryIncome: z.number().nullable().optional().describe("Unusual and infrequent income."),
    extraordinaryLoss: z.number().nullable().optional().describe("Unusual and infrequent losses."),
    taxation: z.number().nullable().optional().describe("Amount of income taxes payable."),
  });

  export const template = {
    prompt: `From this financial statement, extract the data from the Profit and Loss section matching the provided schema.
    If you are not absolutely sure about a value, leave it blank.
    Remove all decimals from the numbers.
`,
    schema: profitAndLossSchema,
  };
}
