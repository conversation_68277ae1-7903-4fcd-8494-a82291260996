import { z } from "zod";

export namespace _InvoiceV1 {
  export const entitySchema = z.object({
    name: z.string(),
    address: z.string(),
    city: z.string(),
    country: z.string(),
    countryCodeISO2: z.string(),
    zip: z.string(),
  });
  export const invoiceItemSchema = z.object({
    description: z.string({
      message: "Description should be consolidated in one line, without new lines",
    }).nullable(),
    quantity: z.number().nullable(),
    unit_price: z.number().nullable(),
    total: z.number().nullable(),
  });

  export const countrySchema = z.object({
    country: z.string(),
    countryCodeISO2: z.string(),
  });

  export const invoiceSchema = z.object({
    title: z.string().nullable(),
    invoice_number: z.string().nullable(),
    due_date: z.string({
      message: "Date must be in format YYYY-MM-DD",
    }).nullable(),
    invoice_date: z.string({
      message: "Date must be in format YYYY-MM-DD",
    }).nullable(),
    paymentTerms: z.string({
      message: "Payment terms should be consolidated in one line, without new lines",
    }).nullable(),
    paymentTermsInDays: z.string({
      message: "If not available, leave it blank, DO NOT provide N/A",
    }).nullable(),
    invoice_amounts: z.object({
      total: z.number().nullable(),
      tax: z.number().nullable(),
      subtotal: z.number().nullable(),
      currency: z.string().nullable(),
    }).nullable().optional(),
    beneficiary: entitySchema.nullable().optional(),
    client: entitySchema.nullable().optional(),
    countries: z.array(countrySchema).nullable().optional().describe(
      "Extract countries that are directly relevant to the transaction. These include client and supplier countries, origin, destination and intermediate countries. Do NOT include country names that appear in the goods or other irrelevant contexts.",
    ),
    invoiceItems: z.array(invoiceItemSchema).nullable().optional(),
  });

  export const template = {
    prompt: `From this invoice extract the data matching the provided schema. 
          All the dates must be output in the format YYYY-MM-DD.
          Important! If not absolutely sure about something, better leave blank. 
          You should use for countryCodeISO2 the 2-letter ISO code of the respective country.
          Do NOT deduce any other data, f.i. if the country is missing but the city is available, still leave country blank.`,
    schema: invoiceSchema,
  };
}
