import { z } from "zod";
import { typedObjectKeys } from "modules/typed-object/index.ts";
import { _ProviderName } from "modules/intelligent-document-data-extraction/types.ts";

export const _PROVIDERS = ["GOOGLE_GEMINI", "OPENAI_GPT"] as const;

export const _GEMINI_MODEL_NAMES = {
  "GOOGLE_GEMINI.FLASH.LATEST": "gemini-2.5-flash-preview-05-20",
  "GOOGLE_GEMINI.FLASH.STABLE": "gemini-2.0-flash",
  "GOOGLE_GEMINI.FLASH.LITE": "gemini-2.0-flash-lite",
  "GOOGLE_GEMINI.PRO.LATEST": "gemini-2.5-pro-preview-05-06",
} as const;

export const _OPENAI_MODEL_NAMES = {
  "OPENAI_GPT.4.LATEST": "gpt-4.1",
  "OPENAI_GPT.4.STABLE": "gpt-4o",
  "OPENAI_GPT.4mini.LATEST": "gpt-4.1-mini",
  "OPENAI_GPT.4mini.STABLE": "gpt-4o-mini",
  "OPENAI_GPT.4nano.LATEST": "gpt-4.1-nano",
} as const;

export const _MODEL_NAMES_MAP = {
  ..._GEMINI_MODEL_NAMES,
  ..._OPENAI_MODEL_NAMES,
} as const;

export const [first, ...modelNames] = typedObjectKeys(_MODEL_NAMES_MAP);

export const _ModelNameSchema = z.enum([first, ...modelNames]);
