import { ProcessSchema } from "types/process-schema.ts";
import { getCurrentGasStageName } from "../get-current-gas-stage-name/index.ts";

/**
 * Uses the process's package.json file to determine if the current environment is production or staging/developement.
 * @returns boolean
 */
export const checkIfProdStage = (packageJson: ProcessSchema.Base) => {
  const currentStageName = getCurrentGasStageName(packageJson);
  const isProdStage = currentStageName === "production";
  return isProdStage;
};
