import { GoogleAppsScriptOAuth2 } from "modules/gas-oauth2/library.ts";
import { OAuth2 } from "modules/gas-oauth2/index.ts";

/**
 * A function to get a service with domain wide delegation.
 * Usually the parameters `issuerClientEmail` and `privateKey` would be stored in a secret and fetched using the `fetchSecret` module.
 *
 * @param {string} parameters.serviceName The name of the service
 * @param {string} parameters.scope A string with the scopes separated by spaces
 * @param {string} parameters.impersonatedEmail The email of the user to impersonate
 * @param {string} parameters.issuerClientEmail The email of the service account
 * @param {string} parameters.privateKey The private key of the service account
 * @returns A service with domain wide delegation
 */

export function getDomainWideDelegationService(parameters: {
  serviceName: string;
  scope: string;
  impersonatedEmail: string;
  issuerClientEmail: string;
  privateKey: string;
}): GoogleAppsScriptOAuth2.OAuth2Service {
  const {
    serviceName,
    scope,
    impersonatedEmail,
    issuerClientEmail,
    privateKey,
  } = parameters;
  const service = OAuth2.createService(serviceName + impersonatedEmail)
    // Set the endpoint URL.
    .setTokenUrl("https://accounts.google.com/o/oauth2/token")
    // Set the private key and issuer.
    .setPrivateKey(privateKey)
    .setIssuer(issuerClientEmail)
    // Set the name of the user to impersonate. This will only work for Google Apps for Work/EDU accounts whose admin has setup domain-wide  delegation: https://developers.google.com/identity/protocols/OAuth2ServiceAccount#delegatingauthority
    .setSubject(impersonatedEmail)
    // Set the scope. This must match one of the scopes configured during the setup of domain-wide delegation.
    .setScope(scope);
  return service;
}
