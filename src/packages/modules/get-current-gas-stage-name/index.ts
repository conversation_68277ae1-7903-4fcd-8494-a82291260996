import { ProcessSchema } from "types/process-schema.ts";
/**
 * A function that given a package.json file content, detects the current GAS stage's name.
 * @param {ProcessSchema.Base} packageJsonContent The package.json file content.
 */
export const getCurrentGasStageName = (
  packageJsonContent: ProcessSchema.Base,
): ProcessSchema.StageName => {
  const { stages } = packageJsonContent;
  const stageNames = Object.keys(stages) as ProcessSchema.StageName[];
  const scriptId = ScriptApp.getScriptId();
  const currentStage = stageNames.find((stageName) => {
    const stage = stages[stageName];
    const stageUrl = stage?.url;
    return stageUrl?.includes(`/${scriptId}/`);
  });
  if (!currentStage) {
    throw new Error(`Could not find current stage for script id ${scriptId}`);
  }
  return currentStage;
};
