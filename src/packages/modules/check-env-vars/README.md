# Check Environment Variables

This module is used to check the environment variables of the application.

## Usage

Use the `checkEnvVars` function to check if the provided environment variables are present.

See the following example:

```ts
import checkEnvVars from "modules/check-env-vars/index.ts";

const envVars = ["ENV_VAR_1", "ENV_VAR_2"];
try {
  checkEnvVars(envVars);
} catch (error) {
  // Handle error
}
```
