import { Logger } from "modules/logger/index.ts";
/**
 * Checks if the required environment variables are set.
 *
 * @return {void} Throws an error if any required environment variables are missing.
 */
export const checkEnvVars = (envVars: string[]) => {
  Logger.info(`checking env vars for ${envVars}`);
  const missingEnvVars = envVars.filter((envVar) => !Deno.env.get(envVar));
  if (missingEnvVars.length > 0) {
    throw new Error(
      `Missing environment variables: ${missingEnvVars.join(", ")}`,
    );
  }
};
