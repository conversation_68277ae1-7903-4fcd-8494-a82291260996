{"compilerOptions": {"target": "ES2019", "rootDir": ".", "lib": ["DOM", "DOM.Iterable", "esnext"], "allowImportingTsExtensions": true, "noEmit": true, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "downlevelIteration": true, "skipLibCheck": true, "noImplicitAny": true, "noImplicitReturns": true, "esModuleInterop": true, "resolveJsonModule": true, "module": "esnext", "moduleResolution": "node", "isolatedModules": true, "allowJS": true}}