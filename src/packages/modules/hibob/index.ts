import "@std/dotenv/load";
import { Hibob } from "./types.ts";
import { checkEnvVars } from "../check-env-vars/index.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["HIBOB_SERVICE_ACCOUNT"]);

/**
 * Returns a hibob client to interactive with their API.
 * @param packageJson
 * @returns
 */
export const getHibobClient = () => {
  Logger.info("Creating a new instance of the hibob client");
  const { encodedToken, baseUrl } = JSON.parse(
    Deno.env.get("HIBOB_SERVICE_ACCOUNT")!,
  ) as Hibob.Types.Credentials;
  const missingSecretElements = [encodedToken, baseUrl].filter(
    (element) => !element,
  );
  const isElementMissing = missingSecretElements.length > 0;
  if (isElementMissing) {
    throw new Error(
      `Missing secret elements: ${missingSecretElements.join(", ")}`,
    );
  }
  const hibobClient = new HibobModule(baseUrl, encodedToken);
  Logger.info("Hibob client instance created");
  return hibobClient;
};

class HibobModule {
  private token: string;
  private baseUrl: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  /**
   * Generalization of the api call to the hibob platform.Takes a url path relative to https://api.hibob.com/v1 and options
   *
   * @template T - The type of the response
   * @param {string} relativeEndpoint - The url path relative to https://api.hibob.com/v1
   * @param {string} method - The method to use for the call
   * @param {unknown} [payload] - The payload to send to the endpoint
   */
  hibobApiCall = async <T>(
    relativeEndpoint: string,
    method: string,
    payload?: unknown,
  ): Promise<T | void> => {
    const options: RequestInit = {
      method,
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        Authorization: `Basic ${this.token}`,
      },
    };
    if (payload) {
      options.body = JSON.stringify(payload);
    }
    const url = this.baseUrl + relativeEndpoint;
    const response = await fetch(url, options);
    const responseText = await response.text();
    const statusCode = response.status;
    console.log({ statusCode });
    const isSuccessful = statusCode.toString().startsWith("2") ||
      statusCode.toString() === "304";
    if (!isSuccessful) {
      throw new Error(
        `call to url failed, status code: ${statusCode}, message: ${responseText}`,
      );
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
  };

  /**
   * Call to retrieve a list of all employees. Based on the parameters it will return the information in different formats.
   *
   * @param {object} parameters - Parameters to filter the employees. NOTE: to fetch fields you have to provide the path, f.i. `work.title` or `root.email`, and operator must be `"equals"`
   * @return {Hibob.Employee[]}
   */
  searchForEmployees = async (parameters: {
    humanReadable?: "APPEND" | "REPLACE";
    showInactive?: boolean;
    fields?: string[];
    filters?: { fieldPath: string; operator: string; values: string[] }[];
  }): Promise<Hibob.Types.Employee[]> => {
    const relativeEndpoint = `/people/search`;
    const method = "post";
    const response = await this.hibobApiCall<{
      employees: Hibob.Types.Employee[];
    }>(relativeEndpoint, method, parameters);
    if (!response) return [];
    Logger.info(`Found ${response.employees.length} employees`);
    return response.employees;
  };

  /**
   *  Call to get the information of one employee. The id can be an email or the hibob id of the user.
   *  For the fields option, it is an array of paths (not field names). For example, if you want just the email, then it is root.email.
   *  Another example, if you want site, you include work.site into the array.
   * @param {string} id
   * @return {Hibob.Employee | void}
   */
  getEmployeeById = async (
    id: string | number,
    fields?: string[],
    humanReadable: "APPEND" | "REPLACE" = "APPEND",
  ): Promise<Hibob.Types.Employee> => {
    Logger.info(`getting hibob data for id: ${id}`);
    const relativeEndpoint = `/people/${id}`;
    const parameters = {
      humanReadable,
      fields,
    };
    const method = "post";
    const response = (await this.hibobApiCall(
      relativeEndpoint,
      method,
      parameters,
    )) as Hibob.Types.Employee;
    return response;
  };

  /**
   *  Call to update the information of one employee. The id can be an email or the hibob id of the user.
   *
   * @param {string} id
   * @param {object} payload
   * @return {void}
   */
  updateEmployee = async (
    id: string,
    payload: Hibob.Types.Employee,
  ): Promise<void> => {
    Logger.info(
      `updating employee ${id} with the following payload: ${
        JSON.stringify(payload)
      }`,
    );
    const relativeEndpoint = `/people/${id}`;
    const method = "put";
    await this.hibobApiCall<void>(
      relativeEndpoint,
      method,
      payload,
    );
    Logger.info(`Employee updated`);
  };

  updateEmployeeEmail = async (
    hibobId: string,
    email: string,
  ): Promise<void> => {
    Logger.info(
      `updating employee ${hibobId}'s email with ${email}`,
    );
    const relativeEndpoint = `/people/${hibobId}/email`;
    const payload = { email };
    const method = "put";
    await this.hibobApiCall<void>(
      relativeEndpoint,
      method,
      payload,
    );
    Logger.info(`Employee's email updated`);
  };
  /**
   * Checks to see if an employee is on time off on the day of execution. Takes the email of the employee and returns a boolean.
   * @param email
   * @returns
   */
  checkIfEmployeeIsOnTimeoffToday = async (email: string) => {
    Logger.info(`starting request to see if ${email} is on timeoff today`);
    const relativeEndpoint = `/timeoff/outtoday`;
    const method = "get";
    const response = (await this.hibobApiCall(
      relativeEndpoint,
      method,
    )) as Hibob.Types.Timeoff;
    const listOfEmployeesOnTimeoff = response.outs;
    const filteredByEmail = listOfEmployeesOnTimeoff.filter(
      (employee) => employee.employeeEmail === email,
    );
    const isEmployeeOnTimeoff = filteredByEmail.length === 1 ? true : false;
    Logger.info(`Is employee on timeoff today? ${isEmployeeOnTimeoff}`);
    return isEmployeeOnTimeoff;
  };

  /**
   * A function that retrieves a list from hibob using the listName
   * Reference https://apidocs.hibob.com/reference/get_company-named-lists-listname
   * @param listName
   * @returns
   */
  getListByName = async (listName: string) => {
    Logger.info(`Getting list called ${listName}`);
    const relativeEndpoint = `/company/named-lists/${listName}`;
    const method = "get";
    const response = (await this.hibobApiCall<Hibob.Types.List>(
      relativeEndpoint,
      method,
    )) as Hibob.Types.List;
    const list = response.items;
    Logger.info({ list });
    return list;
  };

  /**
   * A function that deletes an item from a list. Uses the listName and the id of the item to be deleted.
   * Reference https://apidocs.hibob.com/reference/delete_company-named-lists-listname-itemid
   * @param listName
   * @param itemId
   */
  deleteListItems = async (listName: string, itemId: string) => {
    Logger.info(`Deleting item with id ${itemId} from the ${listName} list`);
    const encodedItemId = encodeURIComponent(itemId);
    const relativeEndpoint =
      `/company/named-lists/${listName}/${encodedItemId}`;
    const method = "delete";
    (await this.hibobApiCall(relativeEndpoint, method)) as Hibob.Types.List;
  };

  /**
   * A function to get the full work history of an employee
   * @param {string} employeeId - The id of the employee
   * @returns {Hibob.WorkHistory} - The work history of the employee
   */
  getFullWorkHistory = async (
    employeeId: string,
  ): Promise<Hibob.Types.WorkHistory> => {
    Logger.info(`Getting full work history for employee with id ${employeeId}`);
    const encodedEmployeeId = encodeURIComponent(employeeId);
    const relativeEndpoint = `/people/${encodedEmployeeId}/work`;
    const method = "get";
    const response = (await this.hibobApiCall<Hibob.Types.WorkHistory>(
      relativeEndpoint,
      method,
    )) as Hibob.Types.WorkHistory;
    Logger.info({ response });
    return response;
  };

  /**
   * A function to update the custom columns of the work history of an employee
   * @param {string} employeeId - The id of the employee
   * @param {Partial<Hibob.CustomColumnsWorkHistory>} newCustomColumns - The new custom columns to be added
   * @returns {void}
   */
  updateCustomColumsWorkHistory = async (
    employeeId: string,
    newCustomColumns: Partial<Hibob.Types.CustomColumnsWorkHistory>,
  ): Promise<void> => {
    Logger.info(
      `Updating work history with new fields for the employee with id ${employeeId}`,
    );
    const workHistory = await this.getFullWorkHistory(employeeId);
    const lastWorkHistoryEntry = workHistory.values.find(
      (entry) => !entry.endEffectiveDate,
    );
    if (!lastWorkHistoryEntry) {
      throw new Error(
        `No current work history entry found for employee with id ${employeeId}`,
      );
    }
    Logger.info("Last work history entry found:");
    Logger.info(lastWorkHistoryEntry);

    const relativeEndpoint =
      `/people/${employeeId}/work/${lastWorkHistoryEntry.id}`;
    const method = "put";
    const newWorkHistoryEntry = {
      ...lastWorkHistoryEntry,
      customColumns: {
        ...lastWorkHistoryEntry.customColumns,
        ...newCustomColumns,
      },
    };
    // This hibob endpoint allways returns an empty body and a 200 OK. So we are not parsing the response.
    (await this.hibobApiCall(
      relativeEndpoint,
      method,
      newWorkHistoryEntry,
    )) as Hibob.Types.WorkHistoryEntry;
  };
}
