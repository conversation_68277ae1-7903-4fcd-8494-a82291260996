import { assertEquals } from "@std/assert";
import { Logger } from "modules/logger/index.ts";

const { formatName } = await import("./index.ts");

const testCases = [
  {
    name: "<PERSON><PERSON><PERSON>",
    expectedResult: "jorgmuller",
  },
  {
    name: "<PERSON>",
    expectedResult: "anamarialopez",
  },
  {
    name: "<PERSON>",
    expectedResult: "francois.dupont",
  },
  {
    name: "<PERSON>",
    expectedResult: "jose<PERSON><PERSON><PERSON>",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    expectedResult: "oconnor",
  },
  {
    name: "<PERSON><PERSON>",
    expectedResult: "john.smith",
  },
  {
    name: "<PERSON><PERSON><PERSON> 123",
    expectedResult: "jorgmuller123",
  },
  {
    name: "",
    expectedResult: "",
  },
];

for (const testCase of testCases) {
  Deno.test(`Name: ${testCase.name} should be formatted as: ${testCase.expectedResult}`, () => {
    const result = formatName(testCase.name);
    Logger.debug({ result });
    assertEquals(result, testCase.expectedResult);
  });
}
