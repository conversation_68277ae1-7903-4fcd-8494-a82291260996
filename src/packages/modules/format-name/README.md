# Format Name Module

This module provides a utility function to format names by normalizing, removing diacritics, converting to lowercase, and removing unwanted characters.

## Functionality

The `formatName` function performs the following steps:

1. **Normalize**: Normalizes the string to decompose combined characters into their base characters and diacritical marks.
2. **Remove Diacritics**: Removes diacritical marks (accents) from the characters.
3. **Remove Unwanted Characters**: Removes all characters that are not letters, numbers, or periods.
4. **Convert to Lowercase**: Converts the string to lowercase.

## Example

```typescript
const username = "<PERSON><PERSON><PERSON>";
const formattedUsername = formatName(username);

Logger.info(formattedUsername); // Output: "jorg muller"
```

## Test

To run the tests, following command:

```
deno test src/packages/modules/format-name/formatName.test.ts
```
