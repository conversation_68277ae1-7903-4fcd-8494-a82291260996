/**
 * @description
 * This function will create a new temporal spreadsheet from a xlsx file.
 * @param {object} options
 * @param {string} options.xlsxId The id of the xlsx file
 * @param {string} options.folderId The id of the folder where the new spreadsheet will be created
 * @returns The id of the new spreadsheet
 */
export function convertXlsxToSheet({
  xlsxId,
  folderId,
}: {
  xlsxId: string;
  folderId: string;
}) {
  console.log(`Converting xlsx to sheet with xlsxId: ${xlsxId}`);
  const xlsxFile = DriveApp.getFileById(xlsxId);
  const xlsxFileName = xlsxFile.getName();

  console.log("Xlsx file name: ", xlsxFileName);

  const xlsxFileBlob = xlsxFile.getBlob();
  const spreadsheetName = xlsxFileName.replace(".xlsx", "");

  const config = {
    title: spreadsheetName,
    parent: DriveApp.getFolderById(folderId),
    mimeType: "application/vnd.google-apps.spreadsheet",
  };
  // @ts-ignore The types are wrong, as here the reference is to the v2 version
  // see here for the explanation https://stackoverflow.com/a/77676379
  const spreadsheetId = Drive?.Files?.insert(config, xlsxFileBlob).id;
  if (!spreadsheetId) {
    throw new Error("Error creating the spreadsheet");
  }
  return spreadsheetId;
}
