/**
 * Module for common date formatting utilities.
 */

/**
 * Formatting the date to match the YYYY-MM-DD format.
 * @param  {Date} date      The date to be formatted.
 * @return {string}        The formatted date.
 */
export function formatDateToYYYYMMDD(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-based
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}