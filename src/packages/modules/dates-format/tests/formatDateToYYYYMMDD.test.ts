import { describe, it } from "jsr:@std/testing/bdd";
import { assertEquals } from "@std/assert";
import { formatDateToYYYYMMDD } from "modules/dates-format/index.ts";

describe("formatDateToYYYYMMDD", () => {
  it("should format a standard date correctly", () => {
    const date = new Date(2024, 5, 7); // June 7, 2024 (months are 0-based)
    assertEquals(formatDateToYYYYMMDD(date), "2024-06-07");
  });

  it("should pad single-digit months and days with zeros", () => {
    const date = new Date(2024, 0, 5); // January 5, 2024
    assertEquals(formatDateToYYYYMMDD(date), "2024-01-05");
  });

  it("should handle end of year dates", () => {
    const date = new Date(2023, 11, 31); // December 31, 2023
    assertEquals(formatDateToYYYYMMDD(date), "2023-12-31");
  });

  it("should handle leap year dates", () => {
    const date = new Date(2020, 1, 29); // February 29, 2020
    assertEquals(formatDateToYYYYMMDD(date), "2020-02-29");
  });

  it("should handle single-digit day and month", () => {
    const date = new Date(2022, 2, 3); // March 3, 2022
    assertEquals(formatDateToYYYYMMDD(date), "2022-03-03");
  });

  it("should handle minimum possible date", () => {
    const date = new Date(0); // 1970-01-01
    assertEquals(formatDateToYYYYMMDD(date), "1970-01-01");
  });
});
