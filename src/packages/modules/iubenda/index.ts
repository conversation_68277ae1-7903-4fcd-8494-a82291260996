import { CreateConsentInput, CreateConsentResponse } from "modules/iubenda/types.ts";
import { getCurrentCloudRunStageName } from "modules/get-current-cloud-run-stage-name/index.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["IUBENDA_API_KEY"]);

export const getIubendaClient = () => {
  const apiKey = Deno.env.get("IUBENDA_API_KEY")!;
  return new Iubenda(apiKey);
};

/**
 * Class for interacting with the Iubenda consent service
 *
 * documentation: https://www.iubenda.com/en/help/6484-consent-solution-http-api-documentation
 */
export class Iubenda {
  private apiKey: string;
  private url: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.url = "https://consent.iubenda.com/consent/";
  }

  private makeCall = async <T>(method: "GET" | "POST" | "PATCH" | "PUT" | "DELETE", payload?: unknown) => {
    try {
      const options: RequestInit = {
        method,
        headers: {
          "ApiKey": `${this.apiKey}`,
          "Content-Type": "application/json",
        },
      };

      if (payload) {
        options.body = JSON.stringify(payload);
      }

      const response = await fetch(this.url, options);
      const responseText = await response.text();
      if (!response.ok) {
        Logger.info(responseText);
        throw new Error(responseText);
      }
      return JSON.parse(responseText) as T;
    } catch (error) {
      throw new Error(`Error while making request: ${error.message}`);
    }
  };

  /**
   * Create a consent in the Iubenda consent service.
   * @param {CreateConsentInput} consentData - The data to create the consent with
   * @returns {Promise<CreateConsentResponse>} - The response from the Iubenda API
   */
  public async createConsent({ email, firstName, lastName, proofContent, proofForm }: CreateConsentInput): Promise<CreateConsentResponse> {
    const method = "POST";
    const fullName = `${firstName} ${lastName}`;
    const preferencesProd = {
      "privacy_policy": true,
    };
    const preferencesStaging = {
      "testing": true,
    };
    const currentStageName = getCurrentCloudRunStageName();
    const preferences = currentStageName === "production" ? preferencesProd : preferencesStaging;
    const payload = {
      subject: {
        email,
        first_name: firstName,
        last_name: lastName,
        full_name: fullName,
        verified: true,
      },
      legal_notices: [
        {
          identifier: "privacy_notice",
          version: "1",
        },
      ],
      proofs: [
        {
          content: proofContent,
          form: proofForm,
        },
      ],
      preferences,
    };

    const response = await this.makeCall<CreateConsentResponse>(method, payload);
    return response;
  }
}
