import { z } from "zod";
import { hash } from "processes/privacy-notice-api/src/helpers/hash.ts";

export const createConsentInputSchema = z.object({
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  proofContent: z.string(),
  proofForm: z.string(),
});

export type CreateConsentInput = z.infer<typeof createConsentInputSchema>;

export const createConsentResponseSchema = z.object({
  id: z.string(),
  timestamp: z.string(),
  subject_id: z.string(),
});

export type CreateConsentResponse = z.infer<typeof createConsentResponseSchema>;
