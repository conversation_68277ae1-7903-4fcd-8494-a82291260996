# Github Enterprise Client

This module provides a client for interacting with the Github API using owner rights. It is specifically designed to handle operations that require higher permissions, such as getting consumed licenses.

See the [GitHub GraphQL API documentation](https://docs.github.com/graphql) for more information. 
See the [GitHub REST API documentation](https://docs.github.com/en/rest) for more information.

## Authentication

For authentication, we use a [Personal Access Token](https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token). This is <PERSON>'s access token as he has owner rights; the token is stored in the [secret manager](https://console.cloud.google.com/security/secret-manager/secret/GITHUB_TOKEN_FELOPRI/versions?authuser=0&project=appscript-296515).

## Usage

```ts
const githubEnterprise = new GithubClient();
await githubEnterprise.getConsumedLicenses();
```

## Nuances

Ensure that when importing this module in a process, you use the right token in the `package.json`, as it is necessary to have owner rights.

## Methods

- [getConsumedLicenses()](./index.ts#L16)

## Change Log

- **2025-03-04**: Module created to handle operations requiring owner rights, such as `getConsumedLicenses()`. 