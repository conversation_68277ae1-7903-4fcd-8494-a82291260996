export type LicensesInfo = {
  total_seats_consumed: number;
  total_seats_purchased: number;
  users: Array<{
    github_com_login: string;
    github_com_name: string;
    enterprise_server_user_ids: string[];
    github_com_user: boolean;
    enterprise_server_user: boolean;
    visual_studio_subscription_user: boolean;
    license_type: string;
    github_com_profile: string;
    github_com_member_roles: string[];
    github_com_enterprise_roles: string[];
    github_com_verified_domain_emails: string[];
    github_com_saml_name_id: string;
    github_com_orgs_with_pending_invites: string[];
    github_com_two_factor_auth: boolean;
    enterprise_server_emails: string[];
    visual_studio_license_status: string;
    visual_studio_subscription_email: string;
    total_user_accounts: number;
  }>;
};
