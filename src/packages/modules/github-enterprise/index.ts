import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { LicensesInfo } from "./types.ts";
import { octokit } from "./constants.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["GITHUB_TOKEN_ENTERPRISE"]);

/**
 * GithubClient class to used every method of the Github enterprise API
 */
export class GithubClient {
  /**
   * Function to get licenses of an enterprise
   * @returns {Promise<LicensesInfo>} The consumed licenses data
   */
  public async getConsumedLicenses(): Promise<LicensesInfo> {
    Logger.info(`Fetching consumed licenses for Ebury`);
    const response = await octokit.request(
      "GET /enterprises/Ebury/consumed-licenses",
    );
    return response.data as LicensesInfo;
  }
}
