import { Octokit } from "@octokit/rest";
import { GetResponseTypeFromEndpointMethod } from "@octokit/types";

const octokit = new Octokit();

export type restResponse =
  | RemoveUserFromOrganizationResponse
  | AddUserToOrganizationResponse
  | GetAllReposResponse
  | GetRepoEvents;

export type RemoveUserFromOrganizationResponse =
  GetResponseTypeFromEndpointMethod<typeof octokit.rest.orgs.removeMember>;
export type AddUserToOrganizationResponse = GetResponseTypeFromEndpointMethod<
  typeof octokit.rest.orgs.setMembershipForUser
>;
export type GetAllReposResponse = GetResponseTypeFromEndpointMethod<
  typeof octokit.rest.repos.listForOrg
>;
export type GetRepoEvents = GetResponseTypeFromEndpointMethod<
  typeof octokit.rest.activity.listRepoEvents
>;

export type GithubRepo = {
  id: number;
  node_id: string;
  name: string;
  full_name: string;
  private: boolean;
  owner: {
    login: string;
    id: number;
    node_id: string;
    avatar_url: string;
    gravatar_id: string;
    url: string;
    received_events_url: string;
    type: string;
    site_admin?: boolean;
  };
  html_url: string;
  description: string | null;
  fork: boolean;
  url: string;
  releases_url?: string;
  deployments_url?: string;
  created_at: string;
  updated_at: string;
  pushed_at: string | null;
  git_url: string;
  ssh_url: string;
  clone_url: string;
  svn_url: string;
  homepage: string | null;
  size: number;
  stargazers_count: number;
  watchers_count: number;
  language: string | null;
  has_issues: boolean;
  has_projects: boolean;
  has_downloads: boolean;
  has_wiki: boolean;
  has_pages: boolean;
  has_discussions?: boolean;
  forks_count: number;
  mirror_url?: string | null;
  archived: boolean;
  disabled: boolean;
  open_issues_count: number;
  license: {
    key: string;
    name: string;
    spdx_id: string;
    url: string | null;
    node_id: string;
  } | null;
  allow_forking?: boolean;
  is_template?: boolean;
  web_commit_signoff_required?: boolean;
  topics?: string[];
  visibility?: string;
  forks: number;
  open_issues: number;
  watchers: number;
  default_branch: string;
};

export type GithubRepoEvent = {
  id: string;
  type: string;
  actor: {
    id: number;
    login: string;
    display_login?: string;
    gravatar_id: string;
    url: string;
    avatar_url: string;
  };
  repo: {
    id: number;
    name: string;
    url: string;
  };
  org?: {
    id: number;
    login: string;
    display_login?: string;
    gravatar_id: string;
    url: string;
    avatar_url: string;
  };
  public: boolean;
  created_at: string;
};
