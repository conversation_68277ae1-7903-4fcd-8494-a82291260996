import { checkEnvVars } from "modules/check-env-vars/index.ts";
import {
  AddUserToOrganizationResponse,
  GetAllReposResponse,
  GetRepoEvents,
  GithubRepo,
  GithubRepoEvent,
  RemoveUserFromOrganizationResponse,
  restResponse,
} from "./types/rest.ts";
import { graphqlResponse, Node } from "./types/graphql.ts";
import { baseUrl, query, username } from "./constants.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["GITHUB_TOKEN"]);

/**
 * GithubClient class to be used without depending on the API called internally
 */
export class GithubClient {
  private password: string;
  private token: string;

  constructor() {
    this.password = Deno.env.get("GITHUB_TOKEN")!;
    this.token = btoa(`${username}:${this.password}`);
  }

  /**
   * Fetches a user's information from GitHub based on their Ebury email address.
   *
   * @param {string} email - The email address of the user to fetch.
   * @returns {Promise<Node | null>} - Returns the user information object if found, or `null` if not in the organization or not found.
   * Returned object is of user Node type:
   *   {
   *    guid: "0decc45c-4344-11ed-8e38-50156f806021",
   *    samlIdentity: { nameId: "<EMAIL>" },
   *    user: { login: "githubUsername" }
   *   }
   * If the user is inactive or has no login, the user object will be null:
   *   {
   *    guid: "0decc45c-4344-11ed-8e38-50156f806021",
   *    samlIdentity: { nameId: "<EMAIL>" },
   *    user: null
   *   }
   * Then it will not be taken into account and return null.
   * @throws {Error} - Throws an error if the GitHub API call fails.
   */
  public async getUserByEmail(
    email: string,
  ): Promise<Node | null> {
    Logger.info(`Fetching user with email ${email} from github`);
    const users = await this.listOrganizationUsers();
    const userInfo = users.find((user) => user.samlIdentity?.nameId === email);
    Logger.info("Showing response from Github:", { userInfo });
    const isUserInOrganisation = !!userInfo && !!userInfo.user;
    if (!isUserInOrganisation) {
      Logger.warning(
        `User with email ${email} has no login or was not found in Github`,
      );
      return null;
    }
    return userInfo;
  }

  /**
   * Function to get all the events of a GitHub repository
   * @param repoId {string} The repository id
   * @returns {GithubRepoEvent[]} List of events
   */
  public async getRepoEvents(repoId: string) {
    Logger.info(`Fetching Github events for repository ${repoId}`);
    const response = await this.restApiCall<GetRepoEvents>({
      method: "get",
      relativeEndpoint: `/repos/Ebury/${repoId}/events`,
    }) as unknown as GithubRepoEvent[];
    return response;
  }

  /**
   * Function to get all the repositories of the organization
   * @returns {GithubRepo[]} List of repositories
   */
  public async getAllRepos() {
    Logger.info("Fetching all Github repositories");
    const allRepos: GithubRepo[] = [];
    let page = 1;
    let hasNextPage = true;

    while (hasNextPage) {
      const response = await this.restApiCall<GetAllReposResponse>({
        method: "get",
        relativeEndpoint: `/orgs/Ebury/repos?per_page=100&page=${page}`,
      }) as unknown as GithubRepo[];
      hasNextPage = response.length > 0;
      if (!hasNextPage) break;
      allRepos.push(...response);
      page++;
    }
    Logger.info(`Found ${allRepos.length} repos`);
    return allRepos;
  }

  /**
   * Function to invite a user to a department
   * @param userEmail {string} The user email
   * @param departmentId {number[]} The department id
   * @returns {AddUserToOrganizationResponse}
   */
  public async inviteUserToDepartment(
    userEmail: string,
    departmentId: number[],
  ): Promise<AddUserToOrganizationResponse> {
    Logger.info(`Inviting user ${userEmail} to department ${departmentId}`);
    const response = await this.restApiCall<AddUserToOrganizationResponse>({
      method: "post",
      relativeEndpoint: `/orgs/Ebury/invitations`,
      payload: { email: userEmail, team_ids: departmentId },
    });
    return response as AddUserToOrganizationResponse;
  }

  /**
   * Function to add a user to the Ebury organization
   * @param username {string} the user email
   * @returns {AddUserToOrganizationResponse}
   */
  public async addUserToOrganization(
    username: string,
  ): Promise<AddUserToOrganizationResponse> {
    Logger.info(`Adding user ${username} to organization`);
    const response = await this.restApiCall<AddUserToOrganizationResponse>({
      method: "put",
      relativeEndpoint: `/orgs/Ebury/memberships/${username}`,
      payload: { role: "member" },
    });
    return response as AddUserToOrganizationResponse;
  }

  /**
   * Function to remove a user from the Ebury organization
   * @param username {string} the user email
   * @returns {RemoveUserFromOrganizationResponse}
   */
  public async removeUserFromOrganization(
    username: string,
  ): Promise<RemoveUserFromOrganizationResponse> {
    Logger.info(`Removing user ${username} from organization`);
    const response = await this.restApiCall<RemoveUserFromOrganizationResponse>(
      {
        method: "delete",
        relativeEndpoint: `/orgs/Ebury/memberships/${username}`,
      },
    );
    return response as RemoveUserFromOrganizationResponse;
  }

  /**
   * Internal function to fetch a chunk of users from Ebury github organization
   * @param after {string} The cursor to fetch the next chunk
   * @returns
   */
  private async listOrganizationUsersChunk(after?: string) {
    Logger.info("Fetching Github users chunk");
    const queryWithEndCursor = query.replace(
      /after: null/g,
      after ? `after: "${after}"` : "after: null",
    );
    const response = await this.graphQLApiCall<graphqlResponse>({
      method: "post",
      query: queryWithEndCursor,
    });
    return response;
  }

  /**
   * Function to get all the users from Ebury github organization
   * @returns {Node[]} List of users
   */
  public async listOrganizationUsers() {
    Logger.info("Fetching all Github users");
    const users: Node[] = [];
    let endCursor: string | undefined;
    do {
      const response: graphqlResponse = await this.listOrganizationUsersChunk(
        endCursor,
      );
      const {
        data: {
          organization: {
            samlIdentityProvider: {
              externalIdentities: { edges, pageInfo },
            },
          },
        },
      } = response;
      users.push(...edges.map((edge) => edge.node));
      endCursor = pageInfo.hasNextPage ? pageInfo.endCursor : undefined;
    } while (endCursor);
    Logger.info(`Found ${users.length} users`);
    return users;
  }

  /**
   * Generic function to call the Github GraphQL API
   * @param options {method: string, query?: string}
   * @returns graphqlResponse
   */
  private async graphQLApiCall<T extends graphqlResponse>(options: {
    method: string;
    query?: string;
  }) {
    try {
      const { method, query } = options;
      const url = `${baseUrl}/graphql`;
      const requestOptions: RequestInit = {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${this.token}`,
        },
      };
      if (query) {
        requestOptions.body = JSON.stringify({ query });
      }
      Logger.info(`Fetching data from ${url}`);
      const response = await fetch(url, requestOptions);
      const responseText = await response.text();
      Logger.info({ responseText });
      if (!response.ok) {
        Logger.info(responseText);
        throw new Error(responseText);
      }
      const jsonResponse = JSON.parse(responseText) as T;
      return jsonResponse;
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : String(error);
      Logger.info(errorMessage);
      throw new Error(
        `Error fetching data from Github GraphQL API, ${errorMessage}`,
      );
    }
  }

  /**
   * Generic function to call the Github Rest API
   * @param options {method: string, relativeEndpoint: string, payload?: unknown}
   * @returns restResponse
   */
  private async restApiCall<T extends restResponse>(options: {
    method: string;
    relativeEndpoint: string;
    payload?: unknown;
  }) {
    try {
      const { relativeEndpoint, method, payload } = options;
      const url = `${baseUrl}${relativeEndpoint}`;
      const requestOptions: RequestInit = {
        method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Basic ${this.token}`,
        },
      };
      if (payload) {
        requestOptions.body = JSON.stringify(payload);
      }
      Logger.info(`Fetching data from ${url}`);
      const response = await fetch(url, requestOptions);
      const responseText = await response.text();
      if (!response.ok) {
        Logger.info(responseText);
        throw new Error(responseText);
      }
      return responseText as unknown as T;
    } catch (error) {
      const errorMessage = error instanceof Error
        ? error.message
        : String(error);
      Logger.info(errorMessage);
      throw new Error(
        `Error fetching data from Github Rest API, ${errorMessage}`,
      );
    }
  }
}
