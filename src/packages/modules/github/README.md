# Github Client

This module provides a client for interacting with the Github API. There are
methods using GitHub GraphQL API and others using the GitHub REST API.

See the [GitHub GraphQL API documentation](https://docs.github.com/graphql) for
more information. See the
[GitHub REST API documentation](https://docs.github.com/en/rest) for more
information.

## Authentication

For authentication we use a
[Personal Access Token](https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token).
This is Automation Admin access token; the token is
stored in the
[secret manager](https://console.cloud.google.com/security/secret-manager/secret/GITHUB_TOKEN/versions?inv=1&invt=AbrIJA&project=appscript-296515)

## Usage

```ts
const github = new GithubClient();
await github.inviteUserToDepartment(email, [departmentID]);
```

## Methods

- [getRepoEvents()](./index.ts#L33)
- [getAllRepos()](./index.ts#L46)
- [inviteUserToDepartment()](./index.ts#L73)
- [addUserToOrganization()](./index.ts#L90)
- [removeUserFromOrganization()](./index.ts#L107)
- [listOrganizationUsers()](./index.ts#L142)


## Nuances 

The method `getConsumedLicenses()` needs different permissions, then, when changing the used Token, this calls still needs the old secret. That is why `monitoring-github` is fetching a different token form secret manager but using the same name.

## Change Log

- **2025-01-13**: Module migrated from GAS as the onboarding was in its way from
  Make to Deno. At this moment only `inviteUserToDepartment()` in in use.

- **2025-03-04**: Change used Token from Fer's to automationadmin's one.