# Error Notifier

This module is used to notify the user of errors that occur in the application.

It sends a notification to our slack monitoring channels depending on the development stage: #bpa-monitoring for production and #bpa-testing for the rest.

## Usage

Use the `notifyError` function to notify the team of an error.

Pass the `packageJSON` object to the function and the desired error message.

See the following example for a `try/catch` block:

```ts
import packageJSO<PERSON> from "../package.json" with { type: "json" };
try{
  ...
} catch(error){
  notifyError(packageJSON, error.message);
}
```

## Monitoring dashboard

To monitor errors we leverage a [log-based metric](https://console.cloud.google.com/logs/metrics/edit/projects%2Fappscript-296515%2Fmetrics%2FErrorNotified?authuser=1&project=appscript-296515) and charts are available in our team's [monitoring dashboard](https://console.cloud.google.com/monitoring/dashboards/builder/b4d5fee2-5930-409d-aea6-11974002a3fb).

## Contributing

Please see the [contributing guidelines](./CONTRIBUTING.md) for more information.
