import { getCurrentGasStageName } from "../get-current-gas-stage-name/index.ts";
import packageJ<PERSON><PERSON> from "processes/error-notification-router-gas/package.json" with {
  type: "json",
};
import { ProcessSchema } from "types/process-schema.ts";
// when developing this module, change the property `production` to `staging` in the following line
const WEBHOOK_URL = packageJSON.stages.production.endpointUrl;

type ErrorDetails = {
  /**
   * The name of the script that threw the error.
   * @example "My Script"
   */
  scriptName: string;
  /**
   * The URL of the script that threw the error.
   * @example "https://script.google.com/home/<USER>/1X2Y3Z4/edit"
   */
  scriptUrl: string;
  /**
   * The details of the error.
   * @example "Something went wrong"
   */
  details: string;
  /**
   * The platform that the script is running on. At the moment, only `gas` is supported.
   * @default "gas"
   */
  platform: "gas";
  /**
   * (optional) Whether to run in testing mode. If `true`, the request will be sent to the testing Slack channel.
   * @default false
   * @example true
   */
  testing?: boolean;
};

/**
 * An interface for notifying errors.
 * The handler is found in the `error-notification-router` process.
 */
export class ErrorNotifier {
  /**
   * Notifies an error.
   * @param packageJSON The `package.json` of the process that threw the error.
   * @param message The error message.
   *
   * @example
   * import packageJSON from "./package.json" with { type: "json" };
   * const message = "Something went wrong";
   * ErrorNotifier.notifyError(packageJSON, message);
   */
  static notifyError(
    packageJSON: ProcessSchema.Base | ProcessSchema.EndpointProcess,
    message: string,
  ) {
    const currentStage = getCurrentGasStageName(
      packageJSON as ProcessSchema.EndpointProcess,
    ) as keyof typeof packageJSON.stages;
    const isProduction = currentStage === "production";
    const scriptUrl = packageJSON.stages[currentStage]?.url;
    if (!scriptUrl) {
      throw new Error(
        `The URL for the current stage ${currentStage} is not defined`,
      );
    }
    const errorDetails: ErrorDetails = {
      scriptName: `${packageJSON.name} - ${currentStage}`,
      scriptUrl,
      platform: "gas",
      details: message,
      testing: !isProduction,
    };
    const payload = JSON.stringify(errorDetails);
    // DriveApp.getRootFolder(); adding the Drive scope to be able to access the target GAS project
    const response = UrlFetchApp.fetch(WEBHOOK_URL, {
      method: "post",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${ScriptApp.getOAuthToken()}`,
      },
      contentType: "application/json",
      payload,
    });
    const responseCode = response.getResponseCode();
    const responseContent = response.getContentText();
    const isRequestSuccessful = responseCode === 200;
    if (!isRequestSuccessful) {
      throw new Error(
        `Error notifier request failed with code ${responseCode} and content ${responseContent}`,
      );
    }
    const parsedResponseContent = JSON.parse(responseContent) as {
      success: boolean;
    };
    const isResponseSuccessful = parsedResponseContent.success;
    if (!isResponseSuccessful) {
      throw new Error(
        `Error notifier response failed with content ${responseContent}`,
      );
    }
  }
}
