import "jsr:@std/dotenv/load";
import { ProcessSchema } from "../../../types/process-schema.ts";
import { Logger } from "modules/logger/index.ts";

let currentCloudRunStageName: ProcessSchema.StageName;

/**
 * A function that given a package.json file content, detects the current Cloud Run stage's name.
 */
export const getCurrentCloudRunStageName = (): ProcessSchema.StageName => {
  if (currentCloudRunStageName) return currentCloudRunStageName;
  const serviceName = Deno.env.get("K_SERVICE");
  const stageName = serviceName?.split("-").pop() as ProcessSchema.StageName;
  if (!stageName) {
    Logger.error(
      `Unable to detect the current Cloud Run stage name. Detected service name: ${serviceName}. Using "staging".`,
    );
    return "staging";
  }
  currentCloudRunStageName = stageName;
  return stageName;
};
