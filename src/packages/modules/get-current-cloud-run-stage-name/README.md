# Get Current Cloud Run stage

A function that returns the current Cloud Run stage by checking the `K_SERVICE` environment variable - that is, the name of the current Cloud Run service automatically set by Cloud Run.

If it's set, the stage is inferred by the last segment of the name (last portion after the last hyphen f.i. my-process-production returns production); otherwise it's set to `staging` by default (local development).
