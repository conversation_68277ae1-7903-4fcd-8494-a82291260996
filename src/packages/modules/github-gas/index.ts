// deno-lint-ignore-file
import { SecretManager } from "modules/secret-manager/index.ts";
import { GithubGraphQLGAS } from "./types/graphql.ts";
import { GithubRepo, GithubRepoEvent, GithubRest } from "./types/rest.ts";
import { getGASScriptProperty } from "../get-gas-script-property/index.ts";
// deno-lint-ignore ban-ts-comment
// @ts-ignore
import { query } from "./constants.ts";

export namespace GithubClientGAS {
  const username = getGASScriptProperty("GITHUB_USER", false) ||
    "<EMAIL>";
  const personalToken = SecretManager.fetchSecret("GITHUB_TOKEN");
  const token = Utilities.base64Encode(`${username}:${personalToken}`);

  const graphQLRequest = <T extends GithubGraphQLGAS.Response>(options: {
    method: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions["method"];
    query?: string;
  }) => {
    try {
      const baseUrl = "https://api.github.com/graphql";
      const { method, query } = options;
      const requestOptions: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions = {
        method,
        contentType: "application/json",
        headers: {
          Authorization: `Basic ${token}`,
        },
        muteHttpExceptions: true,
      };
      if (query) {
        requestOptions.payload = JSON.stringify({ query });
      }
      const response = UrlFetchApp.fetch(baseUrl, requestOptions);
      const responseContent = response.getContentText();
      try {
        return JSON.parse(responseContent) as T;
      } catch (error) {
        return responseContent as unknown as T;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(errorMessage);
      throw new Error(`Error fetching data from Github, ${errorMessage}`);
    }
  };

  const restRequest = <T extends GithubRest.Response>(options: {
    method: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions["method"];
    path: string;
    payload?: string;
  }) => {
    try {
      const baseUrl = "https://api.github.com";
      const { method, path, payload } = options;
      const requestOptions: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions = {
        method,
        contentType: "application/json",
        headers: {
          Authorization: `Basic ${token}`,
        },
        muteHttpExceptions: true,
      };
      if (payload) {
        requestOptions.payload = payload;
      }
      const response = UrlFetchApp.fetch(`${baseUrl}${path}`, requestOptions);
      const responseContent = response.getContentText();
      try {
        return JSON.parse(responseContent) as T;
      } catch (error) {
        return responseContent as unknown as T;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.log(errorMessage);
      throw new Error(`Error fetching data from Github, ${errorMessage}`);
    }
  };

  const listOrganisationUsersChunk = (after?: string) => {
    console.log("Fetching github users chunk");
    const queryWithEndCursor = query.replace(
      /after: null/g,
      after ? `after: "${after}"` : "after: null",
    );
    const response = graphQLRequest<GithubGraphQLGAS.OrganisationMembersResponse>({
      method: "post",
      query: queryWithEndCursor,
    });
    return response;
  };

  export const listOrganisationUsers = () => {
    console.log("Fetching all github users");
    const users: GithubGraphQLGAS.Node[] = [];
    let endCursor: string | undefined;
    do {
      const response = listOrganisationUsersChunk(endCursor);
      const {
        data: {
          organization: {
            samlIdentityProvider: {
              externalIdentities: { edges, pageInfo },
            },
          },
        },
      } = response;
      users.push(...edges.map((edge) => edge.node));
      endCursor = pageInfo.hasNextPage ? pageInfo.endCursor : undefined;
    } while (endCursor);
    console.log(`Found ${users.length} users`);
    return users;
  };

  export const removeUserFromOrganisation = (username: string) => {
    const response = restRequest<GithubRest.RemoveUserFromOrganisationResponse>(
      {
        method: "delete",
        path: `/orgs/Ebury/memberships/${username}`,
      },
    );
    return response;
  };

  export const addUserToOrganisation = (username: string) => {
    const response = restRequest<GithubRest.AddUserToOrganisationResponse>({
      method: "put",
      path: `/orgs/Ebury/memberships/${username}`,
      payload: JSON.stringify({ role: "member" }),
    });
    return response;
  };

  export const inviteUserToDepartment = (userEmail: string, departmentId: number[]) => {
    const response = restRequest<GithubRest.AddUserToOrganisationResponse>({
      method: "post",
      path: `/orgs/Ebury/invitations`,
      payload: JSON.stringify({ email: userEmail, team_ids: departmentId }),
    });
    return response;
  };

  export const getAllRepos = () => {
    console.log("Fetching all github repos");
    const allRepos: GithubRepo[] = [];
    let page = 1;
    let hasNextPage = true;

    while (hasNextPage) {
      const response = restRequest<GithubRest.GetAllReposResponse>({
        method: "get",
        path: `/orgs/Ebury/repos?per_page=100&page=${page}`,
      }) as unknown as GithubRepo[];
      hasNextPage = response.length > 0;
      if (!hasNextPage) break;
      allRepos.push(...response);
      page++;
    }
    console.log(`Found ${allRepos.length} repos`);
    return allRepos;
  };

  export const getRepoEvents = (repoId: string) => {
    const response = restRequest<GithubRest.GetRepoEvents>({
      method: "get",
      path: `/repos/Ebury/${repoId}/events`,
    }) as unknown as GithubRepoEvent[];
    return response;
  };
}
