// This is a template query to get all the users from a GitHub organization.
// You can replace the `null` value in the `after` field with the `endCursor` value
export const query = `query {
    organization(login: "Ebury") {
      samlIdentityProvider {
        ssoUrl
        externalIdentities(first: 100, after: null) {
          pageInfo {
            endCursor
            hasNextPage
          }
          edges {
            node {
              guid
              samlIdentity {
                nameId
              }
              user {
                login
              }
            }
          }
        }
      }
    }
  }`;
