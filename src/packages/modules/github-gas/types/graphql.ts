import z from "zod";
export namespace GithubGraphQLGAS {
  export type Response = OrganisationMembersResponse;
  export const PageInfoSchema = z.object({
    endCursor: z.string(),
    hasNextPage: z.boolean(),
  });
  export type PageInfo = z.infer<typeof PageInfoSchema>;

  export const UserSchema = z.object({
    login: z.string(),
  });
  export type User = z.infer<typeof UserSchema>;

  export const SamlIdentitySchema = z.object({
    nameId: z.string(),
  });
  export type SamlIdentity = z.infer<typeof SamlIdentitySchema>;

  export const NodeSchema = z.object({
    guid: z.string(),
    samlIdentity: SamlIdentitySchema,
    user: UserSchema,
  });
  export type Node = z.infer<typeof NodeSchema>;

  export const EdgeSchema = z.object({
    node: NodeSchema,
  });
  export type Edge = z.infer<typeof EdgeSchema>;

  export const ExternalIdentitiesSchema = z.object({
    pageInfo: PageInfoSchema,
    edges: z.array(EdgeSchema),
  });
  export type ExternalIdentities = z.infer<typeof ExternalIdentitiesSchema>;

  export const SamlIdentityProviderSchema = z.object({
    ssoUrl: z.string(),
    externalIdentities: ExternalIdentitiesSchema,
  });
  export type SamlIdentityProvider = z.infer<typeof SamlIdentityProviderSchema>;

  export const OrganizationSchema = z.object({
    samlIdentityProvider: SamlIdentityProviderSchema,
  });
  export type Organization = z.infer<typeof OrganizationSchema>;

  export const DataSchema = z.object({
    organization: OrganizationSchema,
  });
  export type Data = z.infer<typeof DataSchema>;

  export const OrganisationMembersResponseSchema = z.object({
    data: DataSchema,
  });
  export type OrganisationMembersResponse = z.infer<
    typeof OrganisationMembersResponseSchema
  >;
}
