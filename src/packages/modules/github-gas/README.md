# Github Client

This module provides a client for interacting with the Github API. See the [GitHub GraphQL API documentation](https://docs.github.com/graphql) for more information.

## Authentication

For authentication we use a [Personal Access Token](https://docs.github.com/en/github/authenticating-to-github/creating-a-personal-access-token). 
This is Automation Admin access token; it's stored in the [secret manager](https://console.cloud.google.com/security/secret-manager/secret/GITHUB_TOKEN/versions?inv=1&invt=AbrIJA&project=appscript-296515)
