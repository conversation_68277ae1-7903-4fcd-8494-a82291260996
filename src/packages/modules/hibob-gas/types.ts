export namespace Hibob {
  export namespace Types {
    export type Credentials = {
      id: string;
      token: string;
      encodedToken: string;
      baseUrl: string;
    };
    export type BaseEmployee = {
      fullName?: string;
      displayName?: string;
      custom?: {
        category_1622105420047?: {
          field_1622128638827?: string;
          field_1685362186626?: string;
        };
      };
      personal?: {
        shortBirthDate?: string;
        honorific?: string;
        nationality?: string;
      };
      creationDateTime?: string;
      work?: {
        shortStartDate?: string;
        startDate?: string;
        manager?: string;
        tenureDuration?: string;
        custom?: {
          field_1668668686805?: string;
          field_1622201070501?: string;
          field_1668668634953?: string;
          field_1674817516056?: string;
        };
        durationOfEmployment?: string;
        reportsToIdInCompany?: string;
        employeeIdInCompany?: string;
        reportsTo?: {
          displayName?: string;
          email: string;
          surname?: string;
          firstName?: string;
          id?: string;
        };
        siteId?: string;
        tenureDurationYears?: string;
        department?: string;
        tenureYears?: string;
        customColumns?: CustomColumnsWorkHistory;
        isManager?: string;
        title?: string;
        site?: string;
        activeEffectiveDate?: string;
        secondLevelManager?: string;
        daysOfPreviousService?: string;
        yearsOfService?: string;
      };
      avatarUrl?: string;
      about?: {
        foodPreferences?: string;
        superpowers?: string;
        hobbies?: string;
        avatar?: string;
      };
      companyId?: string;
      email?: string;
      surname?: string;
      id?: string;
      firstName?: string;
      payroll?: {
        employment: {
          type: string;
        };
      };
      employment?: {
        custom?: CustomFieldsEmployment;
      };
    };
    export type Employee = BaseEmployee & {
      humanReadable?: BaseEmployee;
    };
    export type Outs = {
      policyTypeDisplayName: string;
      requestRangeType: string;
      endDate: string;
      employeeEmail: string;
      endDatePortion: string;
      employeeId: string;
      employeeDisplayName: string;
      startDate: string;
      startDatePortion: string;
    };
    export type Timeoff = {
      outs: [Outs];
    };

    export type ListItem = {
      id: string;
      value: string;
      name: string;
      archived: boolean;
    };
    export type List = {
      name: string;
      values: ListItem[];
      items: ListItem[];
    };

    export type WorkHistory = {
      values: WorkHistoryEntry[];
    };

    export type WorkHistoryEntry = {
      canBeDeleted: boolean;
      workChangeType: string;
      change: {
        reason: string | null;
        changedBy: null;
        changedById: string;
      };
      reportsTo: {
        id: string;
        firstName: string;
        surname: string;
        email: string;
        displayName: string;
      };
      creationDate: null;
      title: string;
      customColumns: CustomColumnsWorkHistory;
      isCurrent: boolean;
      modificationDate: string;
      site: string;
      siteId: number;
      id: number;
      endEffectiveDate: string | null;
      activeEffectiveDate: string;
      department: string;
      effectiveDate: string;
    };

    export type CustomColumnsWorkHistory = {
      column_1619179960366: string | null;
      column_1619430539580: string | null;
      column_1678790781277: string | null;
      column_1678804442207: string | null;
      column_1680614993346: string | null;
      column_1680615014916: string | null;
      column_1688046647133: string | null;
      column_1688046890114: string | null;
      column_1691160590650: string | null;
      column_1691160608086: string | null;
      column_1700058775202: string | null;
      column_1703709386385: string | null;
      column_1709894724543: string | null;
      column_1718378012762: string | null;
    };

    export type CustomFieldsEmployment = {
      field_1742462591997?: string | null; //Single sign-on (ID)
      field_1742462615722?: string | null; //Authentication Mode
      field_1743516139713?: string | null; //Approver (Yes/No)
      field_1743516184437?: string | null; //Flexible Arranger (Yes/No)
      field_1743516213301?: string | null; //Global Guest Manager (Yes/No)
      field_1743516166065?: string | null; //Guest Traveler Arranger (Yes/No)
      field_1743516254544?: string | null; //Travel approver arrangers
      field_1743516277819?: string | null; //Billing Entity (Egencia)
      field_1743516291805?: string | null; //Department (Egencia)
    };
  }
}
