import { getCurrentGasStageName } from "../get-current-gas-stage-name/index.ts";
import { SecretManager } from "../secret-manager/index.ts";
import { Hibob } from "./types.ts";
import { ProcessSchema } from "types/process-schema.ts";

/**
 * Returns a hibob client to interactive with their API. Please remember to include the following scopes in your appsscript.json as the function uses the SecretManager
 * "oauthScopes": [
    "https://www.googleapis.com/auth/script.external_request",
    "https://www.googleapis.com/auth/cloud-platform"
   ]
 * @param packageJson
 * @returns
 */
export const getHibobClient = (
  packageJson: ProcessSchema.Base,
  forceProduction = false,
) => {
  console.log("Creating a new instance of the hibob client");
  const currentStage = getCurrentGasStageName(packageJson);
  console.log({ currentStage });
  const secretNames = {
    production: "HiBob-service-account",
    staging: "hibob-sandbox-service-account",
  };
  const secretName = forceProduction
    ? secretNames.production
    : secretNames[currentStage as keyof typeof secretNames];
  const { encodedToken, baseUrl } = SecretManager.fetchSecret(
    secretName,
  ) as Hibob.Types.Credentials;
  const hibobClient = new HibobModule(baseUrl, encodedToken);
  console.log("Hibob client instance created");
  return hibobClient;
};

export class HibobModule {
  private token: string;
  private baseUrl: string;

  constructor(baseUrl: string, token: string) {
    this.baseUrl = baseUrl;
    this.token = token;
  }

  /**
   * Generalization of the api call to the hibob platform.Takes a url path relative to https://api.hibob.com/v1 and options
   *
   * @template T - The type of the response
   * @param {string} relativeEndpoint - The url path relative to https://api.hibob.com/v1
   * @param {GoogleAppsScript.URL_Fetch.HttpMethod} method - The method to use for the call
   * @param {unknown} [payload] - The payload to send to the endpoint
   */
  hibobApiCall = <T>(
    relativeEndpoint: string,
    method: GoogleAppsScript.URL_Fetch.HttpMethod,
    payload?: unknown,
  ): T | void => {
    const options: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions = {
      method,
      muteHttpExceptions: true,
      headers: {
        accept: "application/json",
        "content-type": "application/json",
        Authorization: `Basic ${this.token}`,
      },
    };
    if (payload) {
      options.payload = JSON.stringify(payload);
    }
    const url = this.baseUrl + relativeEndpoint;
    const response = UrlFetchApp.fetch(url, options);
    const responseText = response.getContentText();
    const statusCode = response.getResponseCode();
    const isSuccessful = statusCode.toString().startsWith("2") ||
      statusCode.toString() === "304";
    if (!isSuccessful) {
      throw new Error(
        `call to url failed, status code: ${statusCode}, message: ${responseText}`,
      );
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
  };

  /**
   * Call to retrieve a list of all employees. Based on the parameters it will return the information in different formats.
   *
   * @param {object} parameters - Parameters to filter the employees. NOTE: to fetch fields you have to provide the path, f.i. `work.title` or `root.email`, and operator must be `"equals"`
   * @return {Hibob.Employee[]}
   */
  searchForEmployees = (parameters: {
    humanReadable?: "APPEND" | "REPLACE";
    showInactive?: boolean;
    fields?: string[];
    filters?: { fieldPath: string; operator: string; values: string[] }[];
  }): Hibob.Types.Employee[] => {
    const relativeEndpoint = `/people/search`;
    const method = "post";
    const response = this.hibobApiCall<{ employees: Hibob.Types.Employee[] }>(
      relativeEndpoint,
      method,
      parameters,
    );
    if (!response) return [];
    return response.employees;
  };

  /**
   *  Call to get the information of one employee. The id can be an email or the hibob id of the user.
   *  For the fields option, it is an array of paths (not field names). For example, if you want just the email, then it is root.email.
   *  Another example, if you want site, you include work.site into the array.
   * @param {string} id
   * @return {Hibob.Employee | void}
   */
  getEmployeeById = (
    id: string | number,
    fields?: string[],
    humanReadable: "APPEND" | "REPLACE" = "APPEND",
  ): Hibob.Types.Employee => {
    const relativeEndpoint = `/people/${id}`;
    const parameters = {
      humanReadable,
      fields,
    };
    const method = "post";
    const response: Hibob.Types.Employee = this.hibobApiCall<
      Hibob.Types.Employee
    >(
      relativeEndpoint,
      method,
      parameters,
    ) as Hibob.Types.Employee;
    return response;
  };

  /**
   *  Call to update the information of one employee. The id can be an email or the hibob id of the user.
   *
   * @param {string} id
   * @param {object} payload
   * @return {void}
   */
  updateEmployee = (id: string, payload: Hibob.Types.Employee): void => {
    const relativeEndpoint = `/people/${id}`;
    const method = "put";
    this.hibobApiCall<void>(relativeEndpoint, method, payload);
  };

  /**
   * Checks to see if an employee is on time off on the day of execution. Takes the email of the employee and returns a boolean.
   * @param email
   * @returns
   */
  checkIfEmployeeIsOnTimeoffToday = (email: string) => {
    console.log(`starting request to see if ${email} is on timeoff today`);
    const relativeEndpoint = `/timeoff/outtoday`;
    const method = "get";
    const response = this.hibobApiCall(
      relativeEndpoint,
      method,
    ) as Hibob.Types.Timeoff;
    const listOfEmployeesOnTimeoff = response.outs;
    const filteredByEmail = listOfEmployeesOnTimeoff.filter(
      (employee) => employee.employeeEmail === email,
    );
    const isEmployeeOnTimeoff = filteredByEmail.length === 1 ? true : false;
    console.log(`Is employee on timeoff today? ${isEmployeeOnTimeoff}`);
    return isEmployeeOnTimeoff;
  };

  /**
   * A function that retrieves a list from hibob using the listName
   * Reference https://apidocs.hibob.com/reference/get_company-named-lists-listname
   * @param listName
   * @returns
   */
  getListByName = (listName: string) => {
    console.log(`Getting list called ${listName}`);
    const relativeEndpoint = `/company/named-lists/${listName}`;
    const method = "get";
    const response = this.hibobApiCall<Hibob.Types.List>(
      relativeEndpoint,
      method,
    ) as Hibob.Types.List;
    const list = response.items;
    console.log({ list });
    return list;
  };

  /**
   * A function that deletes an item from a list. Uses the listName and the id of the item to be deleted.
   * Reference https://apidocs.hibob.com/reference/delete_company-named-lists-listname-itemid
   * @param listName
   * @param itemId
   */
  deleteListItems = (listName: string, itemId: string) => {
    console.log(`Deleting item with id ${itemId} from the ${listName} list`);
    const encodedItemId = encodeURIComponent(itemId);
    const relativeEndpoint =
      `/company/named-lists/${listName}/${encodedItemId}`;
    const method = "delete";
    this.hibobApiCall(relativeEndpoint, method) as Hibob.Types.List;
  };

  /**
   * A function to get the full work history of an employee
   * @param {string} employeeId - The id of the employee
   * @returns {Hibob.WorkHistory} - The work history of the employee
   */
  getFullWorkHistory = (employeeId: string): Hibob.Types.WorkHistory => {
    console.log(`Getting full work history for employee with id ${employeeId}`);
    const encodedEmployeeId = encodeURIComponent(employeeId);
    const relativeEndpoint = `/people/${encodedEmployeeId}/work`;
    const method = "get";
    const response = this.hibobApiCall<Hibob.Types.WorkHistory>(
      relativeEndpoint,
      method,
    ) as Hibob.Types.WorkHistory;
    console.log({ response });
    return response;
  };

  /**
   * A function to update the custom columns of the work history of an employee
   * @param {string} employeeId - The id of the employee
   * @param {Partial<Hibob.CustomColumnsWorkHistory>} newCustomColumns - The new custom columns to be added
   * @returns {void}
   */
  updateCustomColumsWorkHistory = (
    employeeId: string,
    newCustomColumns: Partial<Hibob.Types.CustomColumnsWorkHistory>,
  ): void => {
    console.log(
      `Updating work history with new fields for the employee with id ${employeeId}`,
    );
    const workHistory = this.getFullWorkHistory(employeeId);
    const lastWorkHistoryEntry = workHistory.values.find(
      (entry) => !entry.endEffectiveDate,
    );
    if (!lastWorkHistoryEntry) {
      throw new Error(
        `No current work history entry found for employee with id ${employeeId}`,
      );
    }
    console.log("Last work history entry found:");
    console.log(lastWorkHistoryEntry);

    const relativeEndpoint =
      `/people/${employeeId}/work/${lastWorkHistoryEntry.id}`;
    const method = "put";
    const newWorkHistoryEntry = {
      ...lastWorkHistoryEntry,
      customColumns: {
        ...lastWorkHistoryEntry.customColumns,
        ...newCustomColumns,
      },
    };
    // This hibob endpoint allways returns an empty body and a 200 OK. So we are not parsing the response.
    this.hibobApiCall(
      relativeEndpoint,
      method,
      newWorkHistoryEntry,
    ) as Hibob.Types.WorkHistoryEntry;
  };
}
