# Hibob Module.

This module is an api wrapper for the hibob platform.

> Reference: <https://apidocs.hibob.com/reference/getting-started-1>

## Authorizing

The authorization is pulled automatically from google secrets, is transparent for the user of the module.

## Permissions

Please add the following to `appsscript.json` :

```json
"oauthScopes": [
  "https://www.googleapis.com/auth/script.external_request",
  "https://www.googleapis.com/auth/cloud-platform"
]
```

## Usage

example:

```js
const hibobClient = getHibobClient(packageJson);
const listOfEmployeesOnTimeoff = hibobClient.listOfEmployeesOnTimeoffToday();
```

## Methods

- [searchForEmployees()](./index.ts#L87)
  > NOTE: to fetch fields you have to provide the path, f.i. `work.title` or `root.email`.
- [getEmployeeById()](./index.ts#L111)
- [updateEmployee()](./index.ts#L138)
- [checkIfEmployeeIsOnTimeoffToday()](./index.ts#L149)
- [getListByName()](./index.ts#172)
- [deleteListItems()](./index.ts#191)
