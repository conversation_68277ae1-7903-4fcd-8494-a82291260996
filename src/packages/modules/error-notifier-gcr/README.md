# Error Notifier

This module is used to notify the user of errors that occur in the application.

## Usage

Use the `notifyError` function to notify the user of an error.

JSDoc comments are provided for the function, so you can see the parameters in
your IDE, also type definitions are provided to help you use the function.

See the following example:

```ts
import { ErrorNotifier } from "modules/error-notifier-gcr";
const errorDetails: ErrorNotifier.ErrorDetails = {
  functionName: "Form Submit Forwarder",
  functionUrl: functionURL,
  details,
  platform: "gcf",
};
ErrorNotifier.notifyError(errorDetails);
```

## Monitoring dashboard

To monitor errors we use charts that are available in our team's
[monitoring dashboard](https://console.cloud.google.com/monitoring/dashboards/builder/b4d5fee2-5930-409d-aea6-11974002a3fb).
