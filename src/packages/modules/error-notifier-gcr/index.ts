import { ProcessSchema } from "types/process-schema.ts";
import { getCurrentCloudRunStageName } from "modules/get-current-cloud-run-stage-name/index.ts";
import { STATUS_CODE } from "@std/http";
import {
  ERROR_MESSAGE_PREFIX,
  HOURS_AGO,
  monitoringChannel,
  nowInSeconds,
  separator,
  slackClient,
  testingChannel,
} from "./constants.ts";
import { SlackPayload } from "modules/error-notifier-gcr/types.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * An interface for notifying errors.
 */
export class ErrorNotifier {
  /**
   * Notifies an error.
   * @param errorMessage The error message.
   * @param packageJSON The `package.json` of the process that threw the error.
   * @param statusCode The status code of the error. Defaults to error 500.
   *
   * @returns `true` if the error was notified successfully, `false` otherwise.
   *
   * @example
   * const isNotified = ErrorNotifier.notifyError(errorMessage: string, packageJson: ProcessSchema.Base, statusCode = STATUS_CODE.InternalServerError);
   * if (isNotified) {
   *    Logger.info("Error notified successfully");
   * }
   * else {
   *   Logger.info("Error not notified");
   * }
   */
  static async notifyError(
    errorMessage: string,
    processPackageJSON: ProcessSchema.Base,
    statusCode: number = STATUS_CODE.InternalServerError,
  ): Promise<boolean> {
    try {
      const currentStageName = getCurrentCloudRunStageName();
      const isCurrentStageProduction = currentStageName === "production";
      const currentStage = processPackageJSON.stages[currentStageName];
      if (!currentStage) {
        throw new Error(
          `No currentStage found for process "${processPackageJSON.name}"`,
        );
      }
      const scriptName = processPackageJSON.name;
      const functionName = "main";
      const scriptUrl = currentStage.url;
      const functionUrl = currentStage.url;
      const details = `Error ${statusCode}: ${errorMessage}`;
      const testing = !isCurrentStageProduction;

      await this.sendSlackNotification({
        scriptName,
        functionName,
        scriptUrl,
        functionUrl,
        details,
        testing,
      } as SlackPayload);

      return true;
    } catch (error) {
      Logger.error(`Error notifying error: ${error}`);
      return false;
    }
  }

  /**
   * Sends a Slack notification.
   * @param options The options for the function.
   * @returns A promise that resolves when the Slack notification is sent.
   * @throws An error if there was an issue sending the Slack notification.
   */
  private static async sendSlackNotification({
    scriptName,
    functionName,
    scriptUrl,
    functionUrl,
    details,
    testing,
  }: SlackPayload): Promise<void> {
    try {
      const slackChannel = testing ? testingChannel : monitoringChannel;

      const loggedErrorMessage =
        `ErrorNotified${separator}projectURL${scriptUrl}${separator}testing${testing}\n${details}`;
      Logger.info(loggedErrorMessage);

      const name = scriptName || functionName;
      const url = scriptUrl || functionUrl;
      const message = `${ERROR_MESSAGE_PREFIX}${name}
  *Project URL*: ${url}
  *Reason*:
   ${details.trim()}`;

      const existingThreadTs = await slackClient.getExistingSlackThreadTs({
        scriptName: name,
        channel: slackChannel,
        nowInSeconds,
        pastTime: HOURS_AGO,
        errorMessagePrefix: ERROR_MESSAGE_PREFIX,
      });

      await slackClient.sendMessageToChannel({
        message,
        channel: slackChannel,
        threadTs: existingThreadTs,
      });
    } catch (error) {
      const errorMessage = `Error sending Slack notification: ${error}`;
      Logger.error(errorMessage);
      throw new Error(errorMessage);
    }
  }
}

export default ErrorNotifier;
