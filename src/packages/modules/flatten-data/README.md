# Flatten Data

Flattens a nested object into a single level object with concatenated keys.

This function takes a nested object and returns a new object where all nested properties are flattened into a single level with keys concatenated by underscores. Arrays are also handled by concatenating the index to the key. /**

This recursive approach to flattening an object is inspired by the article: https://mansimanhas.medium.com/flatten-a-nested-object-in-javascript-bd643707c5af

## Testing

Run `deno test src/packages/modules/flatten-data/tests/flattenData.test.ts`.
