/**
 * Flattens a nested object into a single level object with concatenated keys.
 *
 * This function takes a nested object and returns a new object where all nested
 * properties are flattened into a single level with keys concatenated by underscores.
 * Arrays are also handled by concatenating the index to the key.
 *
 * This recursive approach to flattening an object is inspired by the article:
 * https://mansimanhas.medium.com/flatten-a-nested-object-in-javascript-bd643707c5af
 *
 * @param data - The nested object to be flattened.
 * @param prefix - The prefix to be used for concatenated keys (used in recursion).
 * @returns A new object with flattened keys.
 */
export const flattenData = (data: Record<string, unknown>, prefix = ""): Record<string, unknown> => {
  let result: Record<string, unknown> = {};

  // Iterate over each key in the data object
  for (const key in data) {
    const newKey = prefix ? `${prefix}_${key}` : key;
    const value = data[key];

    // If the value is an object (but not an array), recursively flatten it
    if (typeof value === "object" && value !== null && !Array.isArray(value)) {
      result = { ...result, ...flattenData(value as Record<string, unknown>, newKey) };
    } // If the value is an array, iterate over each item and flatten it
    else if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (typeof item === "object" && item !== null) {
          Object.assign(result, flattenData(item as Record<string, unknown>, `${newKey}_${index}`));
        } else {
          result[`${newKey}_${index}`] = item;
        }
      });
    } // If the value is a primitive, add it to the result with the new key
    else {
      result[newKey] = value;
    }
  }

  return result;
};
