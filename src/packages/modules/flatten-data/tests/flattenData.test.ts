import { describe, it } from "jsr:@std/testing/bdd";
import { expect } from "jsr:@std/expect";
import { injectEnvVars } from "modules/deno-process/injectEnvVars.ts";
import packageJson from "../../../processes/store-jsons-in-bigquery/package.json" with { type: "json" };

await injectEnvVars(packageJson);
const { flattenData } = await import("modules/flatten-data/index.ts");

describe("flattenData", () => {
  it("should flatten a nested object", () => {
    const input = {
      requestId: "054c724e-e3a9-45a8-b92f-67421b1544f1",
      fileId: "1sF55T-GYylBl3ySsmuLoYMLH2nQhJFVj",
      tradeNumber: "37545",
      invoice: {
        title: "COMMERCIAL INVOICE",
        invoice_number: "PUR230840-ΟΥ",
        due_date: "",
        invoice_date: "2023-12-2",
        invoice_amounts: { total: 78039.45, tax: 0, subtotal: 78039.45, currency: "USD" },
        beneficiary: {
          name: "XIAMEN OUYUE INDUSTRY & TRADING CO., LTD.",
          address: "ROOM 501, NO.311 ZUCUOBIAN ROAD, TONG'AN INDUSTRIAL CONCENTRATION ZONE, XIAMEN",
          city: "XIAMEN",
          country: "CHINA",
          countryCodeISO2: "CN",
          zip: "361009",
        },
        client: {
          name: "GIFTS & CONCEPETS B.V",
          address: "DE RUIF 1 4751 XH OUD GASTEL THE NETHERLANDS",
          city: "OUD GASTEL",
          country: "NETHERLANDS",
          countryCodeISO2: "NL",
          zip: "4751 XH",
        },
        invoiceItems: [
          {
            description: "GC0492Fire Spray Candy 25ml Tedi -25ml 25ml* 15pcs/display",
            quantity: 66945,
            unit_price: 0.21,
            currency: "USD",
            total: 14058.45,
          },
          {
            description: "GC0736 Fublab Snot Squeeze Candy -30g 30g/pc*18pcs*12displays",
            quantity: 140400,
            unit_price: 0.15,
            currency: "USD",
            total: 21060,
          },
          {
            description: "GC0690Funlab Twister Animal Plush-50g 50g*12pcs/PDQ",
            quantity: 12108,
            unit_price: 0.75,
            currency: "USD",
            total: 9081,
          },
          {
            description: "GC0811Funlab Foam Candy Party T-40ml 40ml/pc*12pcs/Display",
            quantity: 112800,
            unit_price: 0.3,
            currency: "USD",
            total: 33840,
          },
        ],
      },
    };

    const expectedOutput = {
      requestId: "054c724e-e3a9-45a8-b92f-67421b1544f1",
      fileId: "1sF55T-GYylBl3ySsmuLoYMLH2nQhJFVj",
      tradeNumber: "37545",
      invoice_title: "COMMERCIAL INVOICE",
      invoice_invoice_number: "PUR230840-ΟΥ",
      invoice_due_date: "",
      invoice_invoice_date: "2023-12-2",
      invoice_invoice_amounts_total: 78039.45,
      invoice_invoice_amounts_tax: 0,
      invoice_invoice_amounts_subtotal: 78039.45,
      invoice_invoice_amounts_currency: "USD",
      invoice_beneficiary_name: "XIAMEN OUYUE INDUSTRY & TRADING CO., LTD.",
      invoice_beneficiary_address: "ROOM 501, NO.311 ZUCUOBIAN ROAD, TONG'AN INDUSTRIAL CONCENTRATION ZONE, XIAMEN",
      invoice_beneficiary_city: "XIAMEN",
      invoice_beneficiary_country: "CHINA",
      invoice_beneficiary_countryCodeISO2: "CN",
      invoice_beneficiary_zip: "361009",
      invoice_client_name: "GIFTS & CONCEPETS B.V",
      invoice_client_address: "DE RUIF 1 4751 XH OUD GASTEL THE NETHERLANDS",
      invoice_client_city: "OUD GASTEL",
      invoice_client_country: "NETHERLANDS",
      invoice_client_countryCodeISO2: "NL",
      invoice_client_zip: "4751 XH",
      invoice_invoiceItems_0_description: "GC0492Fire Spray Candy 25ml Tedi -25ml 25ml* 15pcs/display",
      invoice_invoiceItems_0_quantity: 66945,
      invoice_invoiceItems_0_unit_price: 0.21,
      invoice_invoiceItems_0_currency: "USD",
      invoice_invoiceItems_0_total: 14058.45,
      invoice_invoiceItems_1_description: "GC0736 Fublab Snot Squeeze Candy -30g 30g/pc*18pcs*12displays",
      invoice_invoiceItems_1_quantity: 140400,
      invoice_invoiceItems_1_unit_price: 0.15,
      invoice_invoiceItems_1_currency: "USD",
      invoice_invoiceItems_1_total: 21060,
      invoice_invoiceItems_2_description: "GC0690Funlab Twister Animal Plush-50g 50g*12pcs/PDQ",
      invoice_invoiceItems_2_quantity: 12108,
      invoice_invoiceItems_2_unit_price: 0.75,
      invoice_invoiceItems_2_currency: "USD",
      invoice_invoiceItems_2_total: 9081,
      invoice_invoiceItems_3_description: "GC0811Funlab Foam Candy Party T-40ml 40ml/pc*12pcs/Display",
      invoice_invoiceItems_3_quantity: 112800,
      invoice_invoiceItems_3_unit_price: 0.3,
      invoice_invoiceItems_3_currency: "USD",
      invoice_invoiceItems_3_total: 33840,
    };

    const result = flattenData(input);
    expect(result).toEqual(expectedOutput);
  });
});
