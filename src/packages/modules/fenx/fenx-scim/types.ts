import { z } from "zod";

export const fenXSCIMCredsSchema = z.object({
  clientId: z.string(),
  clientSecret: z.string(),
  xTenantId: z.string(),
  schema: z.string(),
  scope: z.string(),
});

export type FenXSCIMCreds = z.infer<typeof fenXSCIMCredsSchema>;

export namespace SCIM {
  export type MakeCallParams = {
    relativeEndpoint: string;
    method: string;
    payload?: unknown;
  };

  export const baseURL = "https://identity.fenergox.com";

  export type Email = {
    type: "work" | "home";
    value: string;
    primary?: boolean;
  };

  export type Name = {
    formatted?: string;
    familyName: string;
    givenName: string;
    middleName?: string;
    honorificPrefix?: string;
    honorificSuffix?: string;
  };

  export type Schema =
    | "urn:ietf:params:scim:schemas:core:2.0:User"
    | "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User";

  export type ScimAllUsers = {
    schemas: string[];
    totalResults: number;
    itemsPerPage: number;
    startIndex: number;
    Resources: Partial<User>[];
  };

  // https://docs.fenergox.com/api-docs/scim-api#tag/Users/<USER>/CreateUser
  export type User = {
    id?: string;
    schemas: Schema[];
    userName: string;
    name: Name;
    emails: Email[];
    active?: boolean;
    x509Certificates?: string[];
    ims?: string[];
    phoneNumbers?: string[];
    photos?: string[];
    entitlements?: string[];
    roles?: string[];
    addresses?: string[];
    groups?: string[];
    meta?: {
      resourceType: "User";
      created?: string;
      lastModified?: string;
      version?: number;
      location?: string;
    };
  };
}
