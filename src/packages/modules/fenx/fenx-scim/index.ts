import "https://deno.land/x/dotenv@v3.0.0/load.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { zodValidation } from "../../zod-validation/index.ts";
import { FenXSCIMCreds, fenXSCIMCredsSchema, SCIM } from "./types.ts";
import { getToken } from "modules/fenx/helpers/getToken.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["FENX_SCIM_CREDENTIALS"]);
/**
 * Returns a FenX SCIM client.
 * @returns {FenXScimClient} - The FenX SCIM client instance.
 */
export const getFenXScimClient = async (): Promise<FenXScimClient> => {
  Logger.info("Fetching FENX_SCIM_CREDENTIALS from environment variables");
  const secretEnv = Deno.env.get("FENX_SCIM_CREDENTIALS") as string;

  const secret = JSON.parse(secretEnv) as FenXSCIMCreds;
  const accessToken = await getToken(secret);

  zodValidation({
    objectToTest: secret,
    schema: fenXSCIMCredsSchema,
    description: "Validating FenX SCIM secret",
  });

  Logger.info("Creating FenXScimClient instance");
  const fenXScimClient = new FenXScimClient(secret, accessToken);

  return fenXScimClient;
};

/**
 * Wrapper class for FenX SCIM API.
 *
 * @export
 * @class FenXScimClient
 */
export class FenXScimClient {
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly xTenantId: string;
  private readonly scope: string;
  private accessToken: string;
  private readonly baseUrl: string;
  private readonly creds: FenXSCIMCreds;

  /**
   * Constructor of FenXScimClient.
   * @param {FenXSCIMCreds} fenXCreds - FenX SCIM API credentials.
   * @param {string} accessToken - The access token.
   */
  constructor(fenXCreds: FenXSCIMCreds, accessToken: string) {
    this.clientId = fenXCreds.clientId;
    this.clientSecret = fenXCreds.clientSecret;
    this.xTenantId = fenXCreds.xTenantId;
    this.scope = fenXCreds.scope;
    this.accessToken = accessToken;
    this.baseUrl = SCIM.baseURL;
    this.creds = fenXCreds;
  }

  /**
   * Gets the access token.
   * @returns {string} - The access token.
   */
  public getAccessToken(): string {
    return this.accessToken;
  }

  /**
   * Refetches the access token.
   * @returns {Promise<void>} - A promise that resolves when the token is refreshed.
   */
  public async refetchAccessToken(): Promise<void> {
    this.accessToken = await getToken(this.creds);
  }

  private async makeCall<T>({
    relativeEndpoint,
    method,
    payload,
  }: SCIM.MakeCallParams): Promise<T | void> {
    const options: RequestInit = {
      method,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
        "x-tenant-id": this.xTenantId,
      },
    };
    if (payload) {
      options.body = JSON.stringify(payload);
    }
    const url = `${this.baseUrl}/${relativeEndpoint}`;
    let response = await fetch(url, options);
    let responseText = await response.text();
    let statusCode = response.status;
    let isSuccessful = statusCode.toString().startsWith("2") || statusCode.toString() === "304";

    if (!isSuccessful && statusCode === 401) {
      Logger.info("Unauthorized. Attempting to refresh token and retry.");
      await this.refetchAccessToken();
      if (options.headers instanceof Headers) {
        options.headers.set("Authorization", `Bearer ${this.getAccessToken()}`);
      }
      response = await fetch(url, options);
      responseText = await response.text();
      statusCode = response.status;
      isSuccessful = statusCode.toString().startsWith("2") || statusCode.toString() === "304";
    }

    if (!isSuccessful) {
      throw new Error(`Call to URL failed, status code: ${statusCode}, message: ${responseText}`);
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
  }

  /**
   * Retrieves a user by email from SCIM. If no email is passed, it will retrieve all users.
   *
   * @param {string} [email] - The email of the user to search for.
   * @returns {Promise<FenX.SCIM.ScimAllUsers>} - The list of users.
   * @throws {Error} - Throws an error if the API call fails.
   */
  public async searchSCIMUser(email?: string): Promise<SCIM.ScimAllUsers> {
    Logger.info(`Searching SCIM user${email ? ` with email: ${email}` : ""}`);
    let filter = "";
    if (email) {
      filter = `?filter=userName Eq "${email}"`;
    }

    const relativeEndpoint = `scim/Users${filter}`;
    const method = "GET";
    const listOfUsers = await this.makeCall<SCIM.ScimAllUsers>({ relativeEndpoint, method }) as SCIM.ScimAllUsers;
    Logger.info(`Retrieved ${listOfUsers.totalResults} users`);
    return listOfUsers;
  }

  /**
   * Create a new user in the FenX SCIM API
   *
   * @param {FenX.SCIM.User} user - The user to create.
   * @returns {Promise<FenX.SCIM.User>} - The created user.
   * @throws {Error} - Throws an error if the API call fails or if no user is returned.
   */
  public async createSCIMUser(user: SCIM.User): Promise<SCIM.User> {
    Logger.info(`Creating SCIM user`);
    const relativeEndpoint = `scim/Users`;
    const method = "POST";
    const payload = user;

    const newUser = await this.makeCall<SCIM.User>({ relativeEndpoint, method, payload }) as SCIM.User;
    if (!newUser) {
      throw new Error(
        `Error creating user: ${JSON.stringify(user)}\n\nNO USER RETURNED`,
      );
    }
    Logger.info(`Created user with ID: ${newUser.id}`);
    return newUser;
  }

  /**
   * Updates properties of the user in SCIM. (Setting the active to false will deactivate the user).
   *
   * @param {string} userId - The ID of the user to update.
   * @param {Partial<FenX.SCIM.User>} propertiesToUpdate - The properties to update.
   * @returns {Promise<FenX.SCIM.User>} - The updated user.
   * @throws {Error} - Throws an error if the API call fails or if no user is returned.
   */
  public async updateSCIMUser(
    userId: string,
    propertiesToUpdate: Partial<SCIM.User>,
  ): Promise<SCIM.User> {
    Logger.info(`Updating SCIM user with ID: ${userId}`);
    const relativeEndpoint = `scim/Users/<USER>
    const method = "PUT";
    const payload = propertiesToUpdate;

    const updatedUser = await this.makeCall<SCIM.User>({ relativeEndpoint, method, payload }) as SCIM.User;
    if (!updatedUser) {
      throw new Error(
        `Error updating user: ${userId}`,
      );
    }
    Logger.info(`Updated user with ID: ${updatedUser.id}`);
    return updatedUser;
  }
}
