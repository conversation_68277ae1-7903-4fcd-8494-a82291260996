# FenX SCIM Client Module

This is a module to be used as a client for FenX SCIM API calls.

## Nuances

Ensure that the environment variable `FENX_SCIM_CREDENTIALS` is set with the appropriate credentials.

## Authorizing

To authorize API calls to the FenX platform, you need to provide the appropriate credentials. This module uses the SCIM API credentials and URL for processes such as user onboarding and user management.

### Types of Secrets and URL

- Secret: `FENX_SCIM_CREDENTIALS`
- URL: https://api.fenergox.com

### How to Use

To use the `FenXScimClient`, you need to call the `getFenXScimClient` function, which will load the appropriate credentials and URL.

#### Example

```typescript
import { getFenXScimClient } from "modules/fenx/fenx-scim-client.ts";

(async () => {
  const fenXScimClient = await getFenXScimClient();
  const users = await fenXScimClient.searchSCIMUser();
  Logger.info(users);
});
```

## Methods

- **searchSCIMUser** : Retrieves a user by email from SCIM. If no email is passed, it will retrieve all users.
  - Returns: eturns: A promise that resolves to a list of users.
- **createSCIMUser** : Creates a new user in the FenX SCIM API.
  - Returns: A promise that resolves to the created user.
- **updateSCIMUser** : Updates properties of the user in SCIM. Setting the active to false will deactivate the user.
  - Returns: A promise that resolves to the updated user.
