import { z } from "zod";

export const fenXCredsSchema = z.object({
  tenantId: z.string(),
  clientId: z.string(),
  clientSecret: z.string(),
  spreadsheetId: z.string(),
  sheetName: z.string(),
  emailHeader: z.string(),
  teamsHeader: z.string(),
  accessLayersHeader: z.string(),
  timestampSheetName: z.string(),
  timestampRange: z.string(),
});

export type FenXCreds = z.infer<typeof fenXCredsSchema>;

export namespace FenX {
  export const baseURL = "https://api.fenergox.com";

  export type APIRequestOptions = {
    baseURL?: string;
    path: string;
    method: GoogleAppsScript.URL_Fetch.HttpMethod;
    payload?: string;
  };
  /**
   * This is the type of the object returned by the API
   */
  export type ApiRequestContent<T> = {
    data: T;
  };
  export interface User {
    id: string;
    label?: string;
    email?: string;
  }
  export interface Team {
    id: string;
    name: string;
    scopes: string[];
  }
  export interface TeamDetails extends Team {
    description: string;
    users: string[];
    version: 1;
  }
  export interface AccessLayer {
    id: string;
    dataKey: string;
    label: string;
    type: string;
    dataType: string;
  }
  export interface AccessLayerDetails extends AccessLayer {
    name: string;
    description: string;
    users: string[];
    version: 1;
  }
  export type UserConfiguration =
    & {
      userId: string;
    }
    & {
      [key in "teams" | "accessLayers"]?: string[];
    };
  export interface UserAuthorizationProfile {
    id: string;
    teams: string[];
    accessLayers: string[];
  }
  export type UpdateKeys = "teams" | "accessLayers";

  export type ReferenceData = {
    accessLayers: AccessLayer[];
    users: User[];
    teams: Team[];
    accessLayersConfiguration: {
      populateDefaultAccessLayers: boolean;
    };
  };
}
