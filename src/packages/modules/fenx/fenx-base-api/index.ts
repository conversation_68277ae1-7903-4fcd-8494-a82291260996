import "https://deno.land/x/dotenv@v3.0.0/load.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { FenX, FenXCreds, fenXCredsSchema } from "./types.ts";
import { zodValidation } from "../../zod-validation/index.ts";
import { getToken } from "modules/fenx/helpers/getToken.ts";
import { SCIM } from "modules/fenx/fenx-scim/types.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["FENX_CREDENTIALS"]);
/**
 * Returns a FenX client.
 * @returns {FenXClient} - The FenX client instance.
 */
export const getFenXClient = async (): Promise<FenXClient> => {
  Logger.info("Fetching FENX_CREDENTIALS from environment variables");
  const secretEnv = Deno.env.get("FENX_CREDENTIALS") as string;

  const secret = JSON.parse(secretEnv) as FenXCreds;
  const accessToken = await getToken(secret);

  zodValidation({
    objectToTest: secret,
    schema: fenXCredsSchema,
    description: "Validating FenX secret",
  });

  Logger.info("Creating FenXClient instance");
  const fenXClient = new FenXClient(secret, accessToken);

  return fenXClient;
};

/**
 * Wrapper class for FenX API.
 *
 * @export
 * @class FenXClient
 */
export class FenXClient {
  private readonly fenXCreds: FenXCreds;
  private readonly tenantId: string;
  private readonly baseUrl: string;

  /**
   * Constructor of FenXClient.
   * @param {FenXCreds} fenXCreds - FenX API credentials.
   * @param {string} accessToken - The access token.
   */
  constructor(fenXCreds: FenXCreds, private accessToken: string) {
    this.fenXCreds = fenXCreds;
    this.tenantId = fenXCreds.tenantId;
    this.baseUrl = FenX.baseURL;
  }

  /**
   * Gets the access token.
   * @returns {string} - The access token.
   */
  public getAccessToken(): string {
    return this.accessToken;
  }

  /**
   * Refetches the access token.
   * @returns {Promise<void>} - A promise that resolves when the token is refreshed.
   */
  public async refetchAccessToken(): Promise<void> {
    this.accessToken = await getToken(this.fenXCreds);
  }

  private async makeCall<T>({
    relativeEndpoint,
    method,
    payload,
  }: SCIM.MakeCallParams): Promise<T | void> {
    const options: RequestInit = {
      method,
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        "Content-Type": "application/json",
        "x-tenant-id": this.tenantId,
      },
    };
    if (payload) {
      options.body = JSON.stringify(payload);
    }
    const url = `${this.baseUrl}/${relativeEndpoint}`;
    let response = await fetch(url, options);
    let responseText = await response.text();
    let statusCode = response.status;
    let isSuccessful = statusCode.toString().startsWith("2") || statusCode.toString() === "304";

    if (!isSuccessful && statusCode === 401) {
      Logger.info("Unauthorized. Attempting to refresh token and retry.");
      await this.refetchAccessToken();
      if (options.headers instanceof Headers) {
        options.headers.set("Authorization", `Bearer ${this.getAccessToken()}`);
      }
      response = await fetch(url, options);
      responseText = await response.text();
      statusCode = response.status;
      isSuccessful = statusCode.toString().startsWith("2") || statusCode.toString() === "304";
    }

    if (!isSuccessful) {
      throw new Error(`Call to URL failed, status code: ${statusCode}, message: ${responseText}`);
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
  }

  /**
   * Fetches all users.
   * This method works with the base API.
   *
   * @param {string[]} [emails] - Optional list of emails to filter the users.
   * @returns {Promise<FenX.User[]>} - The list of users.
   * @throws {Error} - Throws an error if the API call fails or if the emails are not found.
   */
  public async getAllUsers(emails?: string[]): Promise<FenX.User[]> {
    Logger.info(`Retrieving all users`);
    const relativeEndpoint = `authorizationquery/api/user`;
    const method = "GET";
    const users = await this.makeCall<FenX.User[]>({ relativeEndpoint, method });

    if (!users) {
      throw new Error("Failed to retrieve users from FenX.");
    }

    if (emails) {
      const searchedEmails = users.filter((user: FenX.User) => {
        if (!user.email) return false;
        return emails.includes(user.email);
      });
      const userNotFoundInFenX = searchedEmails.length === 0;
      if (userNotFoundInFenX) {
        throw new Error(
          `Email directions not found in the FenX list of users.`,
        );
      }
      return searchedEmails;
    }
    Logger.info(`Retrieved ${users.length} users`);
    return users;
  }
}
