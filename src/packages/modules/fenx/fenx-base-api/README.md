# FenX Base API Module.

This is a module to be used as a client for FenX API calls.

## Nuances

Ensure that the environment variable `FENX_CREDENTIALS` is set with the appropriate credentials.

## Authorizing

To authorize API calls to the FenX platform, you need to provide the appropriate credentials. This module uses the base API credentials and URL for processes such as user authorization and access management.

### Types of Secrets and URL

- Secret: `FENX_CREDENTIALS`
- URL: https://identity.fenergox.com

### How to Use

To use the `FenXClient`, you need to call the `getFenXClient` function, which will load the appropriate credentials and URL.

#### Example

```typescript
import { getFenXClient } from "modules/fenx/fenx-client.ts";

(async () => {
  const fenXClient = await getFenXClient();
  const users = await fenXClient.getAllUsers();
});
```

## Methods

- **getAllUsers** : Fetches all users. Optionally, you can provide a list of emails to filter the users.
  - Returns: A promise that resolves to a list of users.
