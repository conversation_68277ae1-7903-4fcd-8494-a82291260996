import { FenXCreds } from "modules/fenx/fenx-base-api/types.ts";
import { FenXSCIMCreds } from "modules/fenx/fenx-scim/types.ts";

/**
 * Authenticate in FenX and get the access token.
 *
 * @param {FenXCreds | FenXSCIMCreds} fenXCreds - FenX API credentials.
 * @returns {Promise<string>} - The access token.
 * @throws {Error} - Throws an error if the authentication fails.
 */
export async function getToken(fenXCreds: FenXCreds | FenXSCIMCreds): Promise<string> {
  const url = "https://identity.fenergox.com/connect/token";
  const headers = {
    "Content-Type": "application/x-www-form-urlencoded",
  };
  const scope = (fenXCreds as FenXSCIMCreds).scope || "fenx.all";
  const payload = new URLSearchParams({
    client_id: fenXCreds.clientId,
    client_secret: fenXCreds.clientSecret,
    grant_type: "client_credentials",
    scope,
  });

  const options: RequestInit = {
    method: "POST",
    headers,
    body: payload,
  };

  const response = await fetch(url, options);
  if (!response.ok) {
    const responseText = await response.text();
    throw new Error(
      `Authentication failed, status code: ${response.status}, message: ${responseText}`,
    );
  }
  const parsedResponse = await response.json() as { access_token: string };

  const { access_token } = parsedResponse;
  if (!access_token) {
    throw new Error("Failed to retrieve access token");
  }
  return access_token;
}
