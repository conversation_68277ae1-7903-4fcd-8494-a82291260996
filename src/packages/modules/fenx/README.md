# FenX

This module contains two separate submodules to interact with the FenX API: `FenXClient` and `FenXScimClient`. The reason for separating them into two submodules is that each requires different credentials and has specific methods for different processes.

## Submodules

### FenXClient

The `FenXClient` submodule is used for processes that interact with the base FenX API, such as user authorization and access management.

- **Secret**: `FENX_CREDENTIALS`
- **URL**: https://identity.fenergox.com

### FenXScimClient

The `FenXScimClient` submodule is used for processes that interact with the FenX SCIM API, such as user onboarding and user management.

- **Secret**: `FENX_SCIM_CREDENTIALS`
- **URL**: https://api.fenergox.com

## Helpers

In the `helpers` folder, reusable logic for both submodules has been extracted. This includes function `getToken`, which is used by both clients to make API calls and obtain access tokens.
