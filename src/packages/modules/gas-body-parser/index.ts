import { zodValidationGAS } from "../zod-validation/gas.ts";
import { z, ZodObject } from "zod";

/**
 * Sometimes GAS payload is stringified twice. To avoid errors, we try to parse it twice.
 * @param {string} body The body of the request.
 * @returns {T} The parsed body.
 */
export const bodyParserForGAS = <T>(body: string): T => {
  console.log({ body });
  const firstTryParsedBody = JSON.parse(body);
  const parsedBody = typeof firstTryParsedBody === "string"
    ? JSON.parse(firstTryParsedBody)
    : firstTryParsedBody;
  return parsedBody as T;
};

/**
 * This function will parse the body of a request and will validate it with a Zod schema.
 * If the validation fails, it will throw an error.
 * @param {object} options
 * @param {GoogleAppsScript.Events.DoPost} options.event The event object
 * @param {ZodObject} options.schema The Zod schema to validate the body
 * @returns {z.infer<ZodObject>} The parsed body
 */
export const bodyParserForGasWithZodValidation = <T extends z.ZodRawShape>({
  body,
  Schema,
}: {
  body: string;
  Schema: ZodObject<T>;
}): z.infer<ZodObject<T>> => {
  console.log("bodyParserForGasWithZodValidation called");

  const parsedBody = bodyParserForGAS(body);

  console.log("ParsedBody: ", parsedBody);

  console.log("Starting zod validation");
  zodValidationGAS({
    objectToTest: parsedBody,
    schema: Schema,
    description: "Checking request body validity",
  });
  console.log("Zod validation passed");

  return parsedBody as z.infer<ZodObject<T>>;
};
