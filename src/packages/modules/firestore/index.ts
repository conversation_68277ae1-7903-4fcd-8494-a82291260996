import admin from "firebase-admin";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["FIRESTORE_SERVICE_ACCOUNT", "FIRESTORE_PREFER_REST"]);

export const initializeFirebaseApp = (): admin.app.App => {
  Logger.info("initializing firebase");
  try {
    const app = admin.app();
    Logger.info("firebase already initialized");
    return app;
  } catch (error) {
    Logger.info("firebase not initialized, initializing");
    const serviceAccountString = Deno.env.get("FIRESTORE_SERVICE_ACCOUNT")!;

    const serviceAccountJson = JSON.parse(
      serviceAccountString,
    ) as admin.ServiceAccount;
    return admin.initializeApp({
      credential: admin.credential.cert(serviceAccountJson),
    });
  }
};

export const initializeFirestore = async (
  databaseName: string,
): Promise<
  admin.firestore.CollectionReference<
    admin.firestore.DocumentData,
    admin.firestore.DocumentData
  >
> => {
  Logger.info("initializing firestore", databaseName);
  const app = initializeFirebaseApp();
  return app.firestore().collection(databaseName);
};
