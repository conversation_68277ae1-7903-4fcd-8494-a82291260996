# Google Docs Module

This module provides a client for interacting with the Google Docs API. It includes methods for fetching documents, manipulating tables, replacing text, and inserting text into specific locations within a document.

See the [Google Docs API documentation](https://developers.google.com/docs/api) for more information.

## Usage

```typescript
import { GoogleDocs } from "modules/google-docs/index.ts";

const documentId = "your-document-id";

// Replace text in a document
await GoogleDocs.replaceTextInDocument({
  documentId,
  searchText: "Placeholder",
  replaceText: "New Text",
});

// Fetch all tables in a document
const document = await GoogleDocs.getDocument(documentId);
const tables = await GoogleDocs.getAllTables(document);
```

## Nuances

### Batch Updates Must Be of the Same Type

When using batchUpdate, ensure that all requests in the batch are of the same type (e.g., all `insertText` or all `replaceAllText`). Mixing request types in a single batch can lead to unexpected behavior or errors.

### Handling batchUpdate Index Shifts

When sending multiple requests in a single batchUpdate, Google Docs processes them sequentially. Each insertion modifies the document structure, causing subsequent indices to shift. This can lead to incorrect indices for later operations.

#### Solution: Process Insertions in Reverse Index Order

To avoid this issue, sort your requests in descending order of their index values before sending them in a batchUpdate. This ensures that earlier insertions do not affect the indices of later ones.
