import { Logger } from "modules/logger/index.ts";
import { getImpersonatorDocsClient } from "modules/cloud-run-impersonator/index.ts";
import { docs_v1 } from "googleapis";
import {
  GoogleDocsDocument,
  InsertTextRequest,
  StructuralElement,
  Table,
  TableRow,
} from "./types.ts";

export class GoogleDocs {
  private static docsClient?: docs_v1.Docs;

  /**
   * Initializes and retrieves the Google Docs client.
   *
   * @returns The initialized Google Docs client.
   */
  private static async initializeDocClient() {
    if (this.docsClient) {
      return this.docsClient;
    }
    return this.docsClient = await getImpersonatorDocsClient();
  }

  /**
   * Fetches the document data from Google Docs.
   *
   * @returns The document data.
   */
  public static async getDocument(documentId: string) {
    Logger.debug(`Fetching document with ID: ${documentId}`);
    const docsClient = await GoogleDocs.initializeDocClient();
    const document = await docsClient.documents.get({
      documentId: documentId,
    }) as unknown as GoogleDocsDocument;
    const content = document.data.body?.content || [];
    Logger.debug(`Fetched document with ID: ${documentId}`);
    return content;
  }

  /**
   * Retrieves all tables in the document.
   *
   * @returns An array of tables in the document.
   */
  public static getAllTables(document: StructuralElement[]) {
    Logger.info(`Fetching all tables from document`);
    const tables = document
      .filter((element) => !!element.table)
      .map((element) => element.table as Table);
    Logger.debug(`Found ${tables.length} tables`);
    return tables;
  }

  /**
   * Retrieves a specific table from the document by its index.
   *
   * @returns The table at the specified index.
   * @throws {Error} If the table index is out of bounds.
   */
  public static async getTableByIndex(
    options: { document: StructuralElement[]; tableIndex: number },
  ) {
    const { document, tableIndex } = options;
    Logger.info(`Fetching table at index ${tableIndex}`);
    const tables = await GoogleDocs.getAllTables(document);
    if (tableIndex >= tables.length) {
      throw new Error(
        `Table index ${tableIndex} out of bounds. Total tables: ${tables.length}`,
      );
    }
    Logger.debug(`Fetched table at index ${tableIndex}`);
    return tables[tableIndex];
  }

  /**
   * Finds a cell in the table rows based on the provided search text.
   *
   * @returns The target cell and its start index, or null if not found.
   */
  public static findCellInTableByText(
    options: { searchText: string; tableRows: TableRow[] | undefined },
  ) {
    const { searchText, tableRows } = options;
    if (!tableRows) {
      Logger.warning(`Table rows are undefined.`);
      return null;
    }

    Logger.info(`Searching for text "${searchText}" in table rows`);
    for (const row of tableRows) {
      if (!row.tableCells || row.tableCells.length === 0) {
        continue;
      }

      for (const cell of row.tableCells) {
        const cellContent = cell?.content?.[0]?.paragraph?.elements?.[0]
          ?.textRun?.content;

        if (cellContent && cellContent?.includes(searchText)) {
          Logger.debug(`Found matching cell with text "${searchText}"`);
          return cell;
        }
      }
    }
    Logger.warning(`No cell found with text "${searchText}"`);
    return null;
  }

  /**
   * Replaces all occurrences of a specific text in the document with the provided replacement text.
   * Uses `replaceAllText` to perform the operation.
   *
   * @returns A promise that resolves when the operation is complete.
   */
  public static async replaceTextInDocument(options: {
    documentId: string;
    searchText: string;
    replaceText: string;
    matchCase?: boolean;
  }) {
    const { documentId, searchText, replaceText, matchCase = true } = options;
    const docsClient = await GoogleDocs.initializeDocClient();
    await docsClient.documents.batchUpdate({
      documentId,
      requestBody: {
        requests: [
          {
            replaceAllText: {
              containsText: {
                text: searchText,
                matchCase,
              },
              replaceText,
            },
          },
        ],
      },
    });
  }

  /**
   * Creates requests to insert text into specific cells in a table based on the provided fields to populate.
   *
   * This method is designed for tables where the key (search text) is located in one column,
   * and the corresponding value (text to insert) should be inserted into the adjacent column.
   * It searches for the key in the table rows and inserts the value in the cell to the right of the matching cell.
   *
   * @returns A list of InsertTextRequest objects.
   */
  public static async insertTextInTable(options: {
    document: StructuralElement[];
    fieldsToPopulate: Record<string, string>;
    tableIndex: number;
  }): Promise<InsertTextRequest[]> {
    const { document, fieldsToPopulate, tableIndex } = options;
    const requestsList: InsertTextRequest[] = [];

    Logger.info(`Creating requests to populate table at index ${tableIndex}`);
    const table = await GoogleDocs.getTableByIndex({
      document,
      tableIndex,
    });
    if (!table) {
      Logger.warning(`Table at index ${tableIndex} not found`);
      return requestsList;
    }

    Object.entries(fieldsToPopulate).forEach(([searchText, textToInsert]) => {
      const cell = GoogleDocs.findCellInTableByText({
        tableRows: table.tableRows,
        searchText,
      });
      if (cell && cell.endIndex) {
        requestsList.push({
          insertText: {
            location: {
              index: cell.endIndex + 1,
            },
            text: textToInsert,
          },
        });
      }
    });
    return requestsList;
  }

  /**
   * Executes a batch update on a Google Docs document with the provided `insertText` requests.
   * Uses `insertText` to perform the operation.
   *
   * @returns A promise that resolves when the operation is complete.
   */
  public static async insertTextInDocument(options: {
    documentId: string;
    requests: InsertTextRequest[];
  }) {
    const { documentId, requests } = options;

    if (!requests || requests.length === 0) {
      Logger.warning(
        `No requests provided for batch update on document ID: ${documentId}`,
      );
      return;
    }

    Logger.info(
      `Executing update on document ID: ${documentId} with ${requests.length} requests`,
    );

    const docsClient = await GoogleDocs.initializeDocClient();
    await docsClient.documents.batchUpdate({
      documentId,
      requestBody: {
        requests,
      },
    });
  }
}
