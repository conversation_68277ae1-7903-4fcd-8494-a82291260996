export type GoogleDocsDocument = {
  documentId?: string | null;
  title?: string | null;
  data: {
    body?: Body;
  };
  headers?: { [key: string]: Header };
  footers?: { [key: string]: Footer };
  footnotes?: { [key: string]: Footnote };
  documentStyle?: DocumentStyle;
  namedStyles?: NamedStyles;
  revisionId?: string | null;
  suggestionsViewMode?: string | null;
  inlineObjects?: { [key: string]: InlineObject };
  lists?: { [key: string]: List };
  namedRanges?: { [key: string]: NamedRanges };
  positionedObjects?: { [key: string]: PositionedObject };
};

export type Body = {
  content?: StructuralElement[];
};

export type Header = {
  headerId?: string | null;
  content?: StructuralElement[];
};

export type Footer = {
  footerId?: string | null;
  content?: StructuralElement[];
};

export type Footnote = {
  footnoteId?: string | null;
  content?: StructuralElement[];
};

export type StructuralElement = {
  startIndex?: number | null;
  endIndex?: number | null;
  paragraph?: Paragraph;
  sectionBreak?: SectionBreak;
  table?: Table;
  tableOfContents?: TableOfContents;
};

export type Paragraph = {
  elements?: ParagraphElement[];
  paragraphStyle?: ParagraphStyle;
  bullet?: Bullet;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedParagraphStyleChanges?: { [key: string]: SuggestedParagraphStyle };
  suggestedBulletChanges?: { [key: string]: SuggestedBullet };
};

export type ParagraphElement = {
  startIndex?: number | null;
  endIndex?: number | null;
  textRun?: TextRun;
  autoText?: AutoText;
  pageBreak?: PageBreak;
  columnBreak?: ColumnBreak;
  footnoteReference?: FootnoteReference;
  horizontalRule?: HorizontalRule;
  inlineObjectElement?: InlineObjectElement;
  equation?: Equation;
  person?: Person;
  richLink?: RichLink;
  inlineSmartChip?: InlineSmartChip;
};

export type TextRun = {
  content?: string | null;
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type AutoText = {
  type?: string | null; // e.g., "PAGE_NUMBER", "TOTAL_PAGE_COUNT"
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type PageBreak = {
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type ColumnBreak = {
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type FootnoteReference = {
  footnoteId?: string | null;
  footnoteNumber?: string | null;
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type HorizontalRule = {
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type InlineObjectElement = {
  inlineObjectId?: string | null;
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
  suggestedInlineObjectIdChanges?: { [key: string]: SuggestedInlineObjectId };
};

export type Equation = {
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
};

export type Person = {
  personId?: string | null;
  textStyle?: TextStyle;
  personProperties?: PersonProperties;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type PersonProperties = {
  name?: string | null;
  email?: string | null;
};

export type RichLink = {
  richLinkProperties?: RichLinkProperties;
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type RichLinkProperties = {
  uri?: string | null;
  title?: string | null;
  mimeType?: string | null;
  // Further properties like previewUri, chipProperties etc. can be added if needed
};

export type InlineSmartChip = {
  smartChipProperties?: SmartChipProperties;
  textStyle?: TextStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTextStyleChanges?: { [key: string]: SuggestedTextStyle };
};

export type SmartChipProperties = {
  uri?: string | null;
  title?: string | null;
  type?: string | null; // e.g., "DOCUMENT", "SPREADSHEET", "PRESENTATION", "CALENDAR_EVENT", "PLACE", "PERSON", "FILE"
  // Additional properties based on the type
};

export type ParagraphStyle = {
  headingId?: string | null;
  namedStyleType?: string | null; // e.g., "NORMAL_TEXT", "TITLE", "SUBTITLE", "HEADING_1"
  alignment?: string | null; // e.g., "START", "CENTER", "END", "JUSTIFIED"
  lineSpacing?: number | null;
  direction?: string | null; // e.g., "LEFT_TO_RIGHT", "RIGHT_TO_LEFT"
  spacingMode?: string | null; // e.g., "NEVER_COLLAPSE", "COLLAPSE_LISTS"
  spaceAbove?: Dimension;
  spaceBelow?: Dimension;
  borderBetween?: ParagraphBorder;
  borderTop?: ParagraphBorder;
  borderBottom?: ParagraphBorder;
  borderLeft?: ParagraphBorder;
  borderRight?: ParagraphBorder;
  indentFirstLine?: Dimension;
  indentStart?: Dimension;
  indentEnd?: Dimension;
  keepLinesTogether?: boolean | null;
  keepWithNext?: boolean | null;
  avoidWidowAndOrphan?: boolean | null;
  shading?: Shading;
  pageBreakBefore?: boolean | null;
  tabs?: TabStop[];
  // ... other paragraph style properties
};

export type SuggestedParagraphStyle = {
  paragraphStyle?: ParagraphStyle;
  paragraphStyleSuggestionState?: ParagraphStyleSuggestionState;
};

export type ParagraphStyleSuggestionState = {
  headingIdSuggested?: boolean | null;
  namedStyleTypeSuggested?: boolean | null;
  alignmentSuggested?: boolean | null;
  lineSpacingSuggested?: boolean | null;
  // ... other suggested states
};

export type Bullet = {
  listId?: string | null;
  nestingLevel?: number | null;
  textStyle?: TextStyle;
  bulletGlyphPreset?: string | null;
  glyphFormat?: string | null;
  glyphSymbol?: string | null;
  glyphType?: string | null; // e.g., "GLYPH_TYPE_UNSPECIFIED", "NONE", "NUMBER", "UPPER_ALPHA", "LOWER_ALPHA", "ROMAN_UPPER", "ROMAN_LOWER"
};

export type SuggestedBullet = {
  bullet?: Bullet;
  bulletSuggestionState?: BulletSuggestionState;
};

export type BulletSuggestionState = {
  listIdSuggested?: boolean | null;
  nestingLevelSuggested?: boolean | null;
  textStyleSuggestionState?: TextStyleSuggestionState;
  // ... other suggested states
};

export type TextStyle = {
  bold?: boolean | null;
  italic?: boolean | null;
  underline?: boolean | null;
  strikethrough?: boolean | null;
  smallCaps?: boolean | null;
  backgroundColor?: OptionalColor;
  foregroundColor?: OptionalColor;
  fontSize?: Dimension;
  weightedFontFamily?: WeightedFontFamily;
  baselineOffset?: string | null; // e.g., "NONE", "SUPERSCRIPT", "SUBSCRIPT"
  link?: Link;
  // ... other text style properties
};

export type SuggestedTextStyle = {
  textStyle?: TextStyle;
  textStyleSuggestionState?: TextStyleSuggestionState;
};

export type TextStyleSuggestionState = {
  boldSuggested?: boolean | null;
  italicSuggested?: boolean | null;
  underlineSuggested?: boolean | null;
  strikethroughSuggested?: boolean | null;
  // ... other suggested states
};

export type SuggestedInlineObjectId = {
  inlineObjectId?: string | null;
  inlineObjectIdSuggestionState?: {
    inlineObjectIdSuggested?: boolean | null;
  };
};

export type Dimension = {
  magnitude?: number | null;
  unit?: string | null; // e.g., "PT", "DXA"
};

export type OptionalColor = {
  color?: Color;
};

export type Color = {
  rgbColor?: RgbColor;
};

export type RgbColor = {
  red?: number | null;
  green?: number | null;
  blue?: number | null;
};

export type WeightedFontFamily = {
  fontFamily?: string | null;
  weight?: number | null;
};

export type Link = {
  url?: string | null;
  bookmarkId?: string | null;
  headingId?: string | null;
};

export type ParagraphBorder = {
  color?: OptionalColor;
  width?: Dimension;
  padding?: Dimension;
  dashStyle?: string | null; // e.g., "SOLID", "DASH", "DOT"
};

export type Shading = {
  backgroundColor?: OptionalColor;
};

export type TabStop = {
  offset?: Dimension;
  alignment?: string | null; // e.g., "START", "CENTER", "END"
};

export type SectionBreak = {
  sectionStyle?: SectionStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
};

export type SectionStyle = {
  columnSeparatorStyle?: string | null; // e.g., "NONE", "BETWEEN_EACH_COLUMN"
  contentDirection?: string | null; // e.g., "LEFT_TO_RIGHT", "RIGHT_TO_LEFT"
  sectionType?: string | null; // e.g., "CONTINUOUS", "NEXT_PAGE"
  // ... other section style properties
  // For example, margins (marginTop, marginBottom, marginLeft, marginRight), headerId, footerId etc.
  // See official docs for complete list:
  // https://developers.google.com/docs/api/reference/rest/v1/documents#sectionstyle
  defaultHeaderId?: string | null;
  defaultFooterId?: string | null;
  firstPageHeaderId?: string | null;
  firstPageFooterId?: string | null;
  evenPageHeaderId?: string | null;
  evenPageFooterId?: string | null;
  useFirstPageHeaderFooter?: boolean | null;
  pageNumberStart?: number | null;
  marginTop?: Dimension;
  marginBottom?: Dimension;
  marginLeft?: Dimension;
  marginRight?: Dimension;
  marginHeader?: Dimension;
  marginFooter?: Dimension;
  columnProperties?: SectionColumnProperties[];
};

export type SectionColumnProperties = {
  width?: Dimension;
  paddingEnd?: Dimension;
};

export type Table = {
  rows?: number | null;
  columns?: number | null;
  tableRows?: TableRow[];
  tableStyle?: TableStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
};

export type TableRow = {
  startIndex?: number | null;
  endIndex?: number | null;
  tableCells?: TableCell[];
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  tableRowStyle?: TableRowStyle;
  suggestedTableRowStyleChanges?: { [key: string]: SuggestedTableRowStyle };
};

export type TableCell = {
  startIndex?: number | null;
  endIndex?: number | null;
  content?: StructuralElement[];
  tableCellStyle?: TableCellStyle;
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
  suggestedTableCellStyleChanges?: { [key: string]: SuggestedTableCellStyle };
};

export type TableStyle = {
  tableColumnProperties?: TableColumnProperties[];
};

export type TableColumnProperties = {
  width?: Dimension;
  widthType?: string | null; // e.g., "EVENLY_DISTRIBUTED", "FIXED_WIDTH"
};

export type TableRowStyle = {
  minRowHeight?: Dimension;
  tableHeader?: boolean | null; // Deprecated: Use `preventTableRowBreaking` in `TableCellStyle`.
  preventTableRowBreaking?: boolean | null;
};

export type SuggestedTableRowStyle = {
  tableRowStyle?: TableRowStyle;
  tableRowStyleSuggestionState?: TableRowStyleSuggestionState;
};

export type TableRowStyleSuggestionState = {
  minRowHeightSuggested?: boolean | null;
  // ... other suggested states
};

export type TableCellStyle = {
  rowSpan?: number | null;
  columnSpan?: number | null;
  contentAlignment?: string | null; // e.g., "TOP", "MIDDLE", "BOTTOM"
  backgroundColor?: OptionalColor;
  borderTop?: TableCellBorder;
  borderBottom?: TableCellBorder;
  borderLeft?: TableCellBorder;
  borderRight?: TableCellBorder;
  paddingTop?: Dimension;
  paddingBottom?: Dimension;
  paddingLeft?: Dimension;
  paddingRight?: Dimension;
};

export type SuggestedTableCellStyle = {
  tableCellStyle?: TableCellStyle;
  tableCellStyleSuggestionState?: TableCellStyleSuggestionState;
};

export type TableCellStyleSuggestionState = {
  rowSpanSuggested?: boolean | null;
  columnSpanSuggested?: boolean | null;
  // ... other suggested states
};

export type TableCellBorder = {
  color?: OptionalColor;
  width?: Dimension;
  dashStyle?: string | null; // e.g., "SOLID", "DASH", "DOT"
};

export type TableOfContents = {
  content?: StructuralElement[];
  suggestedInsertionIds?: string[] | null;
  suggestedDeletionIds?: string[] | null;
};

export type DocumentStyle = {
  background?: Background;
  defaultHeaderId?: string | null;
  defaultFooterId?: string | null;
  evenPageHeaderId?: string | null;
  evenPageFooterId?: string | null;
  firstPageHeaderId?: string | null;
  firstPageFooterId?: string | null;
  useFirstPageHeaderFooter?: boolean | null;
  useEvenPageHeaderFooter?: boolean | null;
  pageNumberStart?: number | null;
  marginTop?: Dimension;
  marginBottom?: Dimension;
  marginLeft?: Dimension;
  marginRight?: Dimension;
  pageSize?: Size;
  marginHeader?: Dimension;
  marginFooter?: Dimension;
  // ... other document style properties
  // See official docs: https://developers.google.com/docs/api/reference/rest/v1/documents#documentstyle
  flipPageOrientation?: boolean | null;
  defaultTabStop?: Dimension;
  // ... and more from the official spec
};

export type Background = {
  color?: OptionalColor;
};

export type Size = {
  height?: Dimension;
  width?: Dimension;
};

export type NamedStyles = {
  styles?: NamedStyle[];
};

export type NamedStyle = {
  namedStyleType?: string | null; // e.g., "NORMAL_TEXT", "TITLE", etc.
  textStyle?: TextStyle;
  paragraphStyle?: ParagraphStyle;
};

export type InlineObject = {
  objectId?: string | null;
  inlineObjectProperties?: InlineObjectProperties;
  suggestedDeletionIds?: string[] | null;
  suggestedInsertionId?: string | null;
  suggestedInlineObjectPropertiesChanges?: {
    [key: string]: SuggestedInlineObjectProperties;
  };
};

export type InlineObjectProperties = {
  embeddedObject?: EmbeddedObject;
};

export type EmbeddedObject = {
  imageProperties?: ImageProperties;
  // Can also include: embeddedDrawingProperties, embeddedObjectBorder, etc.
  // For simplicity, focusing on image here.
  title?: string | null;
  description?: string | null;
  size?: Size;
  transform?: AffineTransform;
  linkedContentReference?: LinkedContentReference;
  marginTop?: Dimension;
  marginBottom?: Dimension;
  marginLeft?: Dimension;
  marginRight?: Dimension;
};

export type ImageProperties = {
  contentUri?: string | null;
  sourceUri?: string | null;
  angle?: number | null;
  brightness?: number | null;
  contrast?: number | null;
  transparency?: number | null;
  cropProperties?: CropProperties;
  // ... other image properties
};

export type CropProperties = {
  offsetTop?: number | null;
  offsetBottom?: number | null;
  offsetLeft?: number | null;
  offsetRight?: number | null;
  angle?: number | null;
};

export type AffineTransform = {
  scaleX?: number | null;
  scaleY?: number | null;
  translateX?: number | null;
  translateY?: number | null;
  shearX?: number | null;
  shearY?: number | null;
  unit?: string | null; // e.g., "PT"
};

export type LinkedContentReference = {
  // For linked objects from other Google Workspace files
  // e.g., sheetsChartReference, slidesObjectReference, drawingObjectReference
  sheetsChartReference?: SheetsChartReference;
};

export type SheetsChartReference = {
  spreadsheetId?: string | null;
  chartId?: number | null;
};

export type SuggestedInlineObjectProperties = {
  inlineObjectProperties?: InlineObjectProperties;
  inlineObjectPropertiesSuggestionState?: unknown;
};

export type List = {
  listProperties?: ListProperties;
  suggestedInsertionId?: string | null;
  suggestedDeletionIds?: string[] | null;
  suggestedListPropertiesChanges?: { [key: string]: SuggestedListProperties };
};

export type ListProperties = {
  nestingLevels?: NestingLevel[];
};

export type NestingLevel = {
  bulletAlignment?: string | null; // e.g., "START", "CENTER", "END"
  glyphFormat?: string | null;
  glyphSymbol?: string | null;
  glyphType?: string | null; // e.g., "GLYPH_TYPE_UNSPECIFIED", "NONE", "NUMBER", "UPPER_ALPHA"
  indentFirstLine?: Dimension;
  indentStart?: Dimension;
  startNumber?: number | null;
  textStyle?: TextStyle;
};

export type SuggestedListProperties = {
  listProperties?: ListProperties;
  listPropertiesSuggestionState?: unknown;
};

export type NamedRanges = {
  name?: string | null;
  ranges?: Range[];
};

export type Range = {
  startIndex?: number | null;
  endIndex?: number | null;
  segmentId?: string | null; // Usually null for the main body
};

export type PositionedObject = {
  objectId?: string | null;
  positionedObjectProperties?: PositionedObjectProperties;
  suggestedDeletionIds?: string[] | null;
  suggestedInsertionId?: string | null;
  suggestedPositionedObjectPropertiesChanges?: {
    [key: string]: SuggestedPositionedObjectProperties;
  };
};

export type PositionedObjectProperties = {
  embeddedObject?: EmbeddedObject;
  positioning?: PositionedObjectPositioning;
};

export type PositionedObjectPositioning = {
  layout?: string | null; // e.g., "IN_FRONT_OF_TEXT", "BEHIND_TEXT", "BREAK_LEFT", "BREAK_RIGHT", "WRAP_TEXT"
  leftOffset?: Dimension;
  topOffset?: Dimension;
};

export type SuggestedPositionedObjectProperties = {
  positionedObjectProperties?: PositionedObjectProperties;
  positionedObjectPropertiesSuggestionState?: unknown;
};

export type InsertTextRequest = {
  insertText: {
    location: {
      index: number;
    };
    text: string;
  };
};
