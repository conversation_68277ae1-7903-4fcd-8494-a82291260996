import { getSheetColumnHeaders } from "../get-sheet-column-headers/index.ts";

/**
 * Returns the index of a column in a given sheet based on the name of the header
 *
 * @param sheet
 * @param headerNames
 * @returns
 */
export const getColumnIndexesByHeaders = (
  sheet: GoogleAppsScript.Spreadsheet.Sheet,
  headerNames: string[],
) => {
  console.log(`Getting indexes for header: ${headerNames}`);

  const headers = getSheetColumnHeaders(sheet);
  const columnIndexes: number[] = [];

  headerNames.forEach((headerName) => {
    const columnIndex = headers.indexOf(headerName);
    const isFound = columnIndex !== -1;

    if (!isFound) {
      throw new Error(`Cannot find index for ${headerName}`);
    }

    columnIndexes.push(columnIndex);
  });

  console.log(`found indexes: ${columnIndexes}`);

  return columnIndexes;
};
