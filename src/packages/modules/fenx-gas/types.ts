export namespace FenX {
  /**
   * depending on the passed `method` argument, the parameter `payload` should be passed or not
   */
  export type APIRequestOptions = {
    baseURL?: string;
    path: string;
    method: GoogleAppsScript.URL_Fetch.HttpMethod;
    payload?: string;
  };
  /**
   * This is the type of the object returned by the API
   */
  export type ApiRequestContent<T> = {
    data: T;
  };
  export interface User {
    id: string;
    label?: string;
    email?: string;
  }
  export interface Team {
    id: string;
    name: string;
    scopes: string[];
  }
  export interface TeamDetails extends Team {
    description: string;
    users: string[];
    version: 1;
  }
  export interface AccessLayer {
    id: string;
    dataKey: string;
    label: string;
    type: string;
    dataType: string;
  }
  export interface AccessLayerDetails extends AccessLayer {
    name: string;
    description: string;
    users: string[];
    version: 1;
  }
  export type UserConfiguration =
    & {
      userId: string;
    }
    & {
      [key in "teams" | "accessLayers"]?: string[];
    };
  export interface UserAuthorizationProfile {
    id: string;
    teams: string[];
    accessLayers: string[];
  }
  export type UpdateKeys = "teams" | "accessLayers";

  export type ReferenceData = {
    accessLayers: AccessLayer[];
    users: User[];
    teams: Team[];
    accessLayersConfiguration: {
      populateDefaultAccessLayers: boolean;
    };
  };

  export namespace SCIM {
    export type Email = {
      type: "work" | "home";
      value: string;
      primary?: boolean;
    };

    export type Name = {
      formatted?: string;
      familyName: string;
      givenName: string;
      middleName?: string;
      honorificPrefix?: string;
      honorificSuffix?: string;
    };

    export type Schema =
      | "urn:ietf:params:scim:schemas:core:2.0:User"
      | "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User";

    export type ScimAllUsers = {
      schemas: string[];
      totalResults: number;
      itemsPerPage: number;
      startIndex: number;
      Resources: Partial<User>[];
    };

    // https://docs.fenergox.com/api-docs/scim-api#tag/Users/<USER>/CreateUser
    export type User = {
      id?: string;
      schemas: Schema[];
      userName: string;
      name: Name;
      emails: Email[];
      active?: boolean;
      x509Certificates?: string[];
      ims?: string[];
      phoneNumbers?: string[];
      photos?: string[];
      entitlements?: string[];
      roles?: string[];
      addresses?: string[];
      groups?: string[];
      meta?: {
        resourceType: "User";
        created?: string;
        lastModified?: string;
        version?: number;
        location?: string;
      };
    };
  }
}
