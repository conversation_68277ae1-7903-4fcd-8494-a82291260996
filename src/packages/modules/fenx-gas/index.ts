import { FenX } from "./types.ts";

const API_BASE_URL = "https://api.fenergox.com" as const;
const SCIM_BASE_URL = "https://identity.fenergox.com" as const;

type APIResponseWithSuccess<IsSuccessful> = IsSuccessful extends true ? { success: true }
  : { success: false; error: object };
/**
 * A module to interact with the FenX API. It is a wrapper around the FenX API.
 * @param {object} options - The options to create the client: `clientId`, `clientSecret` and `tenantId`
 * @throws {Error} If any of the arguments is not a non-empty string
 * @example
 * const fenXClient = new FenXClientGAS({
 *  clientId: clientId.toString(),
 *  clientSecret: clientSecret.toString(),
 *  tenantId: tenantId.toString(),
 * });
 */
export class FenXClientGAS {
  clientId: string;
  clientSecret: string;
  tenantId: string;
  private _accessToken: string | undefined;
  constructor({
    clientId,
    clientSecret,
    tenantId,
    scope,
  }: {
    clientId: string;
    clientSecret: string;
    tenantId: string;
    scope?: string;
  }) {
    const emptyArguments = [clientId, clientSecret, tenantId].filter(
      (argument) => !argument || argument === "",
    );
    if (emptyArguments.length > 0) {
      throw new Error(`Missing arguments: ${emptyArguments.join(", ")}`);
    }
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.tenantId = tenantId;
    if (scope) {
      this._accessToken = this.generateAccessToken(scope);
    }
  }
  private get accessToken(): string {
    if (!this._accessToken) {
      this._accessToken = this.generateAccessToken();
    }
    return this._accessToken;
  }
  private generateAccessToken(scope = "fenx.all"): string {
    try {
      const url = "https://identity.fenergox.com/connect/token";
      const headers = {
        "Content-Type": "application/x-www-form-urlencoded",
      };
      const payload = {
        client_id: this.clientId,
        client_secret: this.clientSecret,
        grant_type: "client_credentials",
        scope,
      };
      const options = {
        method: "post" as GoogleAppsScript.URL_Fetch.HttpMethod,
        headers: headers,
        payload: payload,
      };
      const response = UrlFetchApp.fetch(url, options);
      const statusCode = response.getResponseCode();
      const responseText = response.getContentText();
      if (statusCode !== 200) {
        throw new Error(
          `Error generating access token. Status code: ${statusCode}. Response: ${responseText}`,
        );
      }

      const result = JSON.parse(responseText) as { access_token: string };
      return result.access_token as string;
    } catch (error) {
      throw new Error(`Error while generating access token: ${error}`);
    }
  }
  private apiRequest<T>(options: FenX.APIRequestOptions) {
    const baseURL = options.baseURL || API_BASE_URL;
    const url = `${baseURL}/${options.path}`;
    const headers = {
      "Content-Type": "application/json",
      authorization: `Bearer ${this.accessToken}`,
      "x-tenant-id": this.tenantId,
    };
    const requestOptions = {
      method: options.method,
      headers: headers,
      payload: options.payload,
    };
    const response = UrlFetchApp.fetch(url, requestOptions);
    const responseText = response.getContentText();
    const statusCode = response.getResponseCode();
    const isSuccessful = statusCode.toString().startsWith("2");
    if (!isSuccessful) {
      throw new Error(
        `Error while making request to ${url}. Status code: ${statusCode}. Response: ${responseText}`,
      );
    }
    const parsedResponse = JSON.parse(responseText);
    const result = parsedResponse as FenX.ApiRequestContent<T>;
    const resultData = result.data ? result.data : parsedResponse;
    return resultData as T;
  }
  /**
   * Get all users from the FenX API
   * @param {string[]} emails - Optional list of emails to filter the users
   * @returns {FenX.User[]} The list of users
   * @throws {Error} If the request fails
   * @example
   * const users = FenXClientGAS.getAllUsers();
   */
  getAllUsers(emails?: string[]): FenX.User[] {
    const users = this.apiRequest<FenX.User[]>({
      method: "get",
      path: "authorizationquery/api/user",
    });
    if (emails) {
      const searchedEmails = users.filter((user: FenX.User) => {
        if (!user.email) return false;
        return emails.includes(user.email);
      });
      const userNotFoundInFenX = searchedEmails.length === 0;
      if (userNotFoundInFenX) {
        throw new Error(
          `Email directions not found in the FenX list of users.`,
        );
      }
      return searchedEmails;
    }
    return users;
  }
  /**
   * Get a user by email address
   * @param {string} emailAddress - The email address of the user
   * @returns {FenX.User} The user
   * @throws {Error} If the request fails
   * @example
   * const user = FenXClientGAS.getUserByEmailAddress("<EMAIL>");
   */
  getUserByEmailAddress(emailAddress: string): FenX.User {
    const user = this.getAllUsers([emailAddress])[0];
    if (!user) {
      throw new Error(`User with email ${emailAddress} not found`);
    }
    return user;
  }
  /**
   * Create a new user in the FenX SCIM API
   *
   * @param {FenX.SCIM.User} user
   * @return {*}  {FenX.SCIM.User}
   * @memberof FenXClientGAS
   */
  createSCIMUser(user: FenX.SCIM.User): FenX.SCIM.User {
    const newUser = this.apiRequest<FenX.SCIM.User>({
      method: "post",
      path: "scim/Users",
      baseURL: SCIM_BASE_URL,
      payload: JSON.stringify(user),
    });
    if (!newUser) {
      throw new Error(
        `Error creating user: ${JSON.stringify(user)}\n\nNO USER RETURNED`,
      );
    }
    return newUser;
  }

  /**
   * Retreives an user by email from SCIM. If no email is passed it will retrieved all users.
   *
   * @param {FenX.SCIM.User} user
   * @return {*}  {FenX.SCIM.User}
   * @memberof FenXClientGAS
   */
  searchSCIMUser(email?: string): FenX.SCIM.ScimAllUsers {
    let filter = "";
    if (email) {
      filter = `?filter=userName Eq "${email}"`;
    }

    const listOfUsers = this.apiRequest<FenX.SCIM.ScimAllUsers>({
      method: "get",
      path: encodeURI(`scim/Users${filter}`),
      baseURL: SCIM_BASE_URL,
    });

    return listOfUsers;
  }

  /**
   * Updates properties of the user in SCIM. (Setting the active to false will deactivate the user).
   *
   * @param {FenX.SCIM.User} user
   * @return {FenX.SCIM.User}
   * @memberof FenXClientGAS
   */
  updateSCIMUser(
    userId: string,
    propertiesToUpdate: Partial<FenX.SCIM.User>,
  ): FenX.SCIM.User {
    const newUser = this.apiRequest<FenX.SCIM.User>({
      method: "put",
      path: `scim/Users/<USER>
      baseURL: SCIM_BASE_URL,
      payload: JSON.stringify(propertiesToUpdate),
    });
    if (!newUser) {
      throw new Error(
        `Error updating user: ${JSON.stringify(userId)}\n\nNO USER RETURNED`,
      );
    }
    return newUser;
  }
  /**
   * Update the user configurations
   * @param {FenX.UserConfiguration[]} userConfigurations - The list of user configurations
   * @returns {APIResponseWithSuccess<true | false>[]} The list of responses
   */
  updateUserConfigurations(
    userConfigurations: FenX.UserConfiguration[],
  ): APIResponseWithSuccess<true | false>[] {
    const headers = {
      "Content-Type": "application/json",
      authorization: `Bearer ${this.accessToken}`,
      "x-tenant-id": this.tenantId,
    };
    const paths = {
      teams: "team",
      accessLayers: "access-layer",
    } as const;
    const userProperties = ["teams", "accessLayers"] as const;
    const requests = [] as GoogleAppsScript.URL_Fetch.URLFetchRequest[];
    userConfigurations.forEach((user) => {
      userProperties.forEach((property) => {
        if (!user[property]) return;
        const userPropertyValue = [...new Set(user[property])].sort();
        const propertyPath = paths[property];
        const url = `${API_BASE_URL}/authorizationcommand/api/${propertyPath}/users/${user.userId}/batch`;
        const payload = {
          data: {
            [property]: userPropertyValue,
          },
        };
        const payloadString = JSON.stringify(payload, null, 0);
        console.log(payloadString);
        const options: GoogleAppsScript.URL_Fetch.URLFetchRequest = {
          url,
          method: "put" as GoogleAppsScript.URL_Fetch.HttpMethod,
          headers: headers,
          payload: payloadString,
          muteHttpExceptions: true,
        };
        requests.push(options);
      });
    });
    // const responses = [];
    // requests.forEach(request => {
    //     const response = UrlFetchApp.fetch(request.url, request);
    //     responses.push(response);
    // });
    const responses = UrlFetchApp.fetchAll(requests);
    const results: APIResponseWithSuccess<true | false>[] = responses.map(
      (response) => {
        const responseCode = response.getResponseCode();
        const responseCodeIsSuccess = responseCode.toString().startsWith("2");
        if (responseCodeIsSuccess) {
          return { success: true } as APIResponseWithSuccess<true>;
        }
        return {
          success: false,
          error: JSON.parse(response.getContentText()) as object,
        } as APIResponseWithSuccess<false>;
      },
    );
    return results;
  }
  /**
   * Get the authorization profile of a user
   * @param {string} userId - The user ID
   * @returns {FenX.UserAuthorizationProfile} The user authorization profile
   */
  getUserAuthorizationProfileByUserId(
    userId: string,
  ): FenX.UserAuthorizationProfile {
    const authorizationProfile = this.apiRequest<FenX.UserAuthorizationProfile>(
      {
        method: "get",
        path: `authorizationquery/api/user/${userId}/authorization-profile`,
      },
    );
    return authorizationProfile;
  }
  /**
   * Get all teams from the FenX API
   * @returns {FenX.TeamDetails[]} The list of teams
   * @throws {Error} If the request fails
   */
  getAllTeams(): FenX.TeamDetails[] {
    const teams = this.apiRequest<FenX.TeamDetails[]>({
      method: "get",
      path: "authorizationquery/api/team",
    });
    return teams;
  }
  getAllAccessLayers(): FenX.AccessLayerDetails[] {
    const accessLayers = this.apiRequest<FenX.AccessLayerDetails[]>({
      method: "get",
      path: "authorizationquery/api/access-layer",
    });
    return accessLayers;
  }

  getReferenceData(): FenX.ReferenceData {
    const url = `${API_BASE_URL}/authorizationquery/api/reference-data`;
    const headers = {
      "Content-Type": "application/json",
      authorization: `Bearer ${this.accessToken}`,
      "x-tenant-id": this.tenantId,
    };
    const options = {
      method: "get" as GoogleAppsScript.URL_Fetch.HttpMethod,
      headers: headers,
    };
    const response = UrlFetchApp.fetch(url, options);
    const result: FenX.ApiRequestContent<FenX.ReferenceData> = JSON.parse(
      response.getContentText(),
    ) as FenX.ApiRequestContent<FenX.ReferenceData>;
    return result.data;
  }
}
