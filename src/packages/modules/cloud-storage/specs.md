# Google Cloud Storage Module specification

## Structure

The structure of this module is based on the [Google Cloud Storage JSON API](https://cloud.google.com/storage/docs/json_api/v1/). The module is made up of a single class, `CloudStorage`, which contains static methods for each endpoint in the API. Each method takes an object as its only argument, which contains the parameters for the request. The methods return the response from the API.

The methods are organized into groups based on the type of resource they operate on. These groups are defined as static classes of the CloudStorage class. For example, the `Objects` group contains methods for interacting with objects in a bucket.

### Why use static methods?

Static methods are used instead of instance methods because the class is not meant to be instantiated. The class does not need any input to be initialized, and the methods do not need to be called on an instance of the class. The methods are simply functions that operate on the API.

In this way, it is "stateless-like" but not precisely stateless. The class does have a state, but it is not meant to be changed. The state is the API key, which is set by the class using `ScriptApp.getOAuthToken`. The API key is used in every request, so it is unnecessary to pass it as a parameter to each method.

## Methods

If you have inspected the source code, you may have noticed that all methods (at least in the `Objects` group) have the same structure. This structure is roughly as follows:

```ts
public static method(params: Input): Output{
  const { url, options } = this.createAPIRequest<QueryParameters, BodyParameters>(params);

  const response = UrlFetchApp.fetch(url, options);

  return this.handleAPIResponse<Input, Output>(response);
}
```

The `createAPIRequest` method is used to create the URL and options for the request. The `handleAPIResponse` method is used to handle the response from the API. The `method` method itself is used to combine these two methods and make the request.

### Why use `createAPIRequest` and `handleAPIResponse`?

Because, after first implementing the methods without these two methods, all methods were making the same steps to create the request and handle the response. The only substantial difference was the types of the parameters and the response. This can be generalized into two methods with generic types.

With these approach, the methods are much more concise and easier to read. And it is much easier to add new methods, because the only thing that needs to be done is to add the method itself and the types for the input and the output, and the query and body parameters (if needed).

But, if it were necessary to add a new method that did not follow this structure, it could simply ignore these methods and implement the request and response handling itself.
