# Google Cloud Storage Module

This module provides a wrapper for the Google Cloud Storage API. For more info on the specifics of the implementation of methods and the module's structure, click [here](specs.md).

> Reference: <https://cloud.google.com/storage/docs/json_api>

## Authentication

The Google Cloud Storage API uses the OAuth 2.0 protocol for authentication and authorization. The service retrieves the credentials from the user that the script is running as. The script's `appscript.json` must have the necessary scopes to access the API.

> GCS Reference: <https://cloud.google.com/storage/docs/authentication#apiauth> Apps Script Reference: <https://developers.google.com/apps-script/reference/script/script-app#getoauthtoken>

## Objects API

### Table of contents

| Name                        | Endpoint                   | Method | Function                                 |
| --------------------------- | -------------------------- | :----: | ---------------------------------------- |
| [get](#get)                 | `/b/{bucket}/o/{object}`   | `GET`  | [`Objects.get()`](index.ts#L46)          |
| [insert](#insert)           | `/upload/.../b/{bucket}/o` | `POST` | [`Objects.insert()`](index.ts#L99)       |
| [batchInsert](#batchinsert) | `/upload/.../b/{bucket}/o` | `POST` | [`Objects.batchInsert()`](index.ts#L386) |
| [list](#list)               | `/b/{bucket}/o`            | `GET`  | [`Objects.list()`](index.ts#L609)        |

## get

**Endpoint:** `/b/{bucket}/o/{object}`<br> **Method:** `GET`

**Function:**

```ts
public static get({
    bucketName,
    objectName,
    params,
    headers,
}: CloudStorage.Objects.GetInput):
    | CloudStorage.Objects.Object
    | ArrayBuffer;
```

[Code](index.ts#L46)

**Description:**

Retrieves an object or its metadata from a bucket.

> Reference: <https://cloud.google.com/storage/docs/json_api/v1/objects/get>

## insert

**Endpoint:** `/upload/.../b/{bucket}/o`<br> **Method:** `POST`

**Function:**

```ts
public static insert({
    bucketName,
    params,
    body,
    headers,
}: CloudStorage.Objects.InsertInput): CloudStorage.Objects.Object;
```

[Code](index.ts#L99)

**Description:**

Stores a new object in a bucket. The file must be passed as a `Blob` object in the body. All types of uploads are supported.

> **Warning:** For multipart uploads the size limit is 40MB. Anything larger than that will cause the JavaScript runtime to exit unexpectedly.

> Reference: <https://cloud.google.com/storage/docs/json_api/v1/objects/insert>

# batchInsert

**Endpoint:** `/upload/.../b/{bucket}/o`<br> **Method:** `POST`

**Function:**

```ts
public static batchInsert({
    bucketName,
    objects,
}: CloudStorage.Objects.BatchInsertInput): CloudStorage.Objects.BatchInsertOutput;
```

[Code](index.ts#L99)

**Description:**

Stores a batch of objects in a bucket. The files must be passed as `Blob` objects in the body of each object in the `objects` array. Currently, only media and multipart uploads are supported.

> **Warning:** For multipart uploads the size limit of the batch is 40MB. Anything larger than that will cause the JavaScript runtime to exit unexpectedly.

> Reference: <https://cloud.google.com/storage/docs/json_api/v1/objects/insert>

## list

**Endpoint:** `/b/{bucket}/o`<br> **Method:** `GET`

**Function:**

```ts
public static list({
    bucketName,
    params,
    headers,
}: CloudStorage.Objects.ListInput): CloudStorage.Objects.ListResponse;
```

[Code](index.ts#L609)

**Description:**

Retrieves a list of objects from a bucket matching the criteria.

> Reference: <https://cloud.google.com/storage/docs/json_api/v1/objects/list>

## Constructor & Usage

### Interface

This module has been implemented as a class with static methods. The constructor is not intended to be used directly.

### Usage

Example:

```js
import { CloudStorage } from "modules/cloud-storage";

const object = CloudStorage.Objects.get({
  bucketName: "my-bucket",
  objectName: "my-object",
  params: {
    alt: "media",
  },
});
```
