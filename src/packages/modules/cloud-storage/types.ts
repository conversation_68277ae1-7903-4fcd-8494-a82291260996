export namespace CloudStorage {
  export type StandardQueryParameters = {
    alt?: "json" | "media";
    fields?: string;
    key?: string;
    prettyPrint?: boolean;
    quotaUser?: string;
    userProject?: string;
  };
  export namespace Objects {
    export interface Metadata {
      name?: string;
      contentType?: string;
      contentEncoding?: string;
      contentDisposition?: string;
      contentLanguage?: string;
      cacheControl?: string;
      customTime?: string;
      crc32c?: string;
      md5Hash?: string;
      storageClass?: string;
      temporaryHold?: boolean;
      eventBasedHold?: boolean;
      metadata?: { [key: string]: string };
      acl?: AccessControls[];
    }

    export interface Object extends Metadata {
      kind?: "storage#object";
      id?: string;
      selfLink?: string;
      mediaLink?: string;
      bucket?: string;
      generation?: string;
      metageneration?: string;
      size?: string;
      componentCount?: number;
      etag?: string;
      kmsKeyName?: string;
      retentionExpirationTime?: string;
      timeCreated?: string;
      updated?: string;
      timeDeleted?: string;
      timeStorageClassUpdated?: string;
      owner?: {
        entity?: string;
        entityId?: string;
      };
      customerEncryption: {
        encryptionAlgorithm: string;
        keySha256: string;
      };
    }

    export type AccessControls = {
      kind?: "storage#objectAccessControl";
      object?: string;
      generation?: string;
      id?: string;
      selfLink?: string;
      bucket?: string;
      entity?: string;
      role?: string;
      email?: string;
      domain?: string;
      entityId?: string;
      etag?: string;
      projectTeam?: {
        projectNumber?: string;
        team?: string;
      };
    };

    export type GetInput = {
      bucketName: string;
      objectName: string;
      params?: CloudStorage.Objects.GetQueryParameters;
      headers?: CloudStorage.Objects.ExtensionHeaders;
    };

    export type GetQueryParameters = CloudStorage.StandardQueryParameters & {
      alt?: "json" | "media";
      generation?: bigint;
      ifGenerationMatch?: bigint;
      ifGenerationNotMatch?: bigint;
      ifMetagenerationMatch?: bigint;
      ifMetagenerationNotMatch?: bigint;
      projection?: "full" | "noAcl";
    };

    export type InsertObject = {
      params: CloudStorage.Objects.InsertQueryParameters;
      contentType: string;
      data: GoogleAppsScript.Base.Blob;
      metadata?: CloudStorage.Objects.Metadata;
      headers?: CloudStorage.Objects.ExtensionHeaders;
    };

    export type BatchInsertInput = {
      bucketName: string;
      objects: InsertObject[];
    };

    export type BatchInsertOutput = {
      result: CloudStorage.Objects.Object[];
      errors: string[];
    };

    export type InsertInput = {
      bucketName: string;
    } & InsertObject;

    export type InsertQueryParameters = CloudStorage.StandardQueryParameters & {
      name: string;
      uploadType: "media" | "multipart" | "resumable";
      contentEncoding?: string;
      ifGenerationMatch?: bigint;
      ifGenerationNotMatch?: bigint;
      ifMetagenerationMatch?: bigint;
      ifMetagenerationNotMatch?: bigint;
      kmsKeyName?: string;
      predifinedAcl?:
        | "authenticatedRead"
        | "bucketOwnerFullControl"
        | "bucketOwnerRead"
        | "private"
        | "projectPrivate"
        | "publicRead";
      projection?: "full" | "noAcl";
    };

    export type ListInput = {
      bucketName: string;
      params?: CloudStorage.Objects.ListQueryParameters;
      headers?: CloudStorage.Objects.ExtensionHeaders;
    };

    export type ListResponse = {
      kind?: "storage#objects";
      items?: Metadata[];
      prefixes?: string[];
      nextPageToken?: string;
    };

    export type ListQueryParameters = CloudStorage.StandardQueryParameters & {
      delimiter?: string;
      endOffset?: string;
      includeTrailingDelimiter?: boolean;
      maxResults?: number;
      pageToken?: string;
      prefix?: string;
      projection?: "full" | "noAcl";
      startOffset?: string;
      versions?: boolean;
    };

    export type ExtensionHeaders = {
      "X-Goog-Encryption-Algorithm"?: string;
      "X-Goog-Encryption-Key"?: string;
      "X-Goog-Encryption-Key-Sha256"?: string;
      "X-Goog-Copy-Source-Encryption-Algorithm"?: string;
      "X-Goog-Copy-Source-Encryption-Key"?: string;
      "X-Goog-Copy-Source-Encryption-Key-Sha256"?: string;
    };
  }
}
