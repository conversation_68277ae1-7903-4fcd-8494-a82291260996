import URLFetchRequestOptions = GoogleAppsScript.URL_Fetch.URLFetchRequestOptions;
import HttpMethod = GoogleAppsScript.URL_Fetch.HttpMethod;
import Byte = GoogleAppsScript.Byte;
import Blob = GoogleAppsScript.Base.Blob;
import { CloudStorage as CloudStorageTypes } from "./types.ts";

const FILE_SIZE_THRESHOLD = 40 * 1024 * 1024; // 40MB

export class CloudStorage {
  private static readonly CHUNK_SIZE = 20 * 1024 * 1024; // 20MB
  public static token: string = ScriptApp.getOAuthToken();
  public static baseURL = "https://storage.googleapis.com/storage/v1";

  /**
   * Obtains a new OAuth token and sets it as the token for the class
   *
   * @private
   * @static
   * @memberof CloudStorageTypes
   */
  private static refreshToken(): void {
    CloudStorage.token = ScriptApp.getOAuthToken();
  }

  /**
   * Static class for interacting with Objects of Cloud Storage Buckets API
   * https://cloud.google.com/storage/docs/json_api/v1/objects
   *
   * @static
   * @memberof CloudStorageTypes
   */
  public static Objects = class {
    /**
     * Gets an object's metadata (alt = 'json') or contents (alt = 'media') from a bucket
     * https://cloud.google.com/storage/docs/json_api/v1/objects/get
     *
     * @static
     * @param {CloudStorageTypes.Objects.GetInput} {
     *       bucketName,
     *       objectName,
     *       params,
     *       headers,
     *     }
     * @return {*}  {(CloudStorageTypes.Objects.Object
     *       | Byte[]
     *       | string)}
     */
    public static get({
      bucketName,
      objectName,
      params,
      headers,
    }: CloudStorageTypes.Objects.GetInput):
      | CloudStorageTypes.Objects.Object
      | Byte[]
      | string {
      const { url, options } = this.createAPIRequest<
        CloudStorageTypes.Objects.GetQueryParameters
      >({
        baseURL: CloudStorage.baseURL,
        path: `/b/${bucketName}/o/${objectName}`,
        method: "get",
        params,
        headers,
      });

      const response = UrlFetchApp.fetch(url, options);

      return this.handleAPIResponse<
        CloudStorageTypes.Objects.GetInput,
        CloudStorageTypes.Objects.Object | Byte[] | string
      >({
        response,
        call: CloudStorage.Objects.get,
        methodParams: {
          bucketName,
          objectName,
          params,
          headers,
        },
      });
    }

    /**
     * Uploads an object to a bucket. Supports all upload types: media, multiparth and resumable.
     * File's content must be passed as a Blob.
     * For multipart uploads, the file cannot exceed 40MB in size.
     * For the other upload types, the file cannot exceed 50MB in size.
     * https://cloud.google.com/storage/docs/json_api/v1/objects/insert
     *
     * @static
     * @param {CloudStorageTypes.Objects.InsertInput} {
     *       bucketName,
     *       params,
     *       contentType,
     *       data,
     *       metadata,
     *       headers,
     *     }
     * @return {*}  {CloudStorageTypes.Objects.Object}
     */
    public static insert(
      input: CloudStorageTypes.Objects.InsertInput,
    ): CloudStorageTypes.Objects.Object {
      const { params, contentType, data, metadata, headers, bucketName } = input;
      // Media upload type https://cloud.google.com/storage/docs/uploading-objects#:~:text=in%20the%20request.-,Media%20upload,-(a%20single%2Drequest
      if (params.uploadType === "media") {
        if (metadata) {
          CloudStorage.handleInsertError(
            "media",
            params.name,
            bucketName,
            "Metadata is not supported for media upload type.",
          );
        }

        if (data.getBytes().length > FILE_SIZE_THRESHOLD) {
          CloudStorage.handleInsertError(
            "media",
            params.name,
            bucketName,
            "Media upload type is not supported for files larger than 40MB.",
          );
        }

        const { url, options } = this.createAPIRequest<
          CloudStorageTypes.Objects.InsertQueryParameters
        >({
          baseURL: "https://storage.googleapis.com/upload/storage/v1",
          path: `/b/${bucketName}/o`,
          method: "post",
          params,
          headers,
          body: data,
        });

        options.contentType = contentType;
        const response = UrlFetchApp.fetch(url, options);

        return this.handleAPIResponse<
          CloudStorageTypes.Objects.InsertInput,
          CloudStorageTypes.Objects.Object
        >({
          response,
          call: CloudStorage.Objects.insert,
          methodParams: {
            bucketName: bucketName,
            params,
            contentType,
            data,
            headers,
          },
        });
      }

      // Resumable upload. https://cloud.google.com/storage/docs/resumable-uploads
      if (params.uploadType === "resumable") {
        const totalObjectSize = data.getBytes().length;
        const chunkSize = CloudStorage.CHUNK_SIZE;

        const { url, options } = this.createAPIRequest<
          CloudStorageTypes.Objects.InsertQueryParameters
        >({
          baseURL: "https://storage.googleapis.com/upload/storage/v1",
          path: `/b/${bucketName}/o`,
          method: "post",
          params,
          headers,
          body: metadata ? JSON.stringify(metadata) : undefined,
        });

        options.contentType = "application/json";
        options.headers = {
          ...options.headers,
          "X-Upload-Content-Type": contentType,
          "X-Upload-Content-Length": totalObjectSize.toString(),
        };

        const response = UrlFetchApp.fetch(url, options);

        // Verify that the response is valid and successful
        this.handleAPIResponse<
          CloudStorageTypes.Objects.InsertInput,
          CloudStorageTypes.Objects.Object | CloudStorageTypes.Objects.Object[]
        >({
          response,
          call: CloudStorage.Objects.insert,
          methodParams: {
            bucketName,
            params,
            contentType,
            data,
            headers,
          },
        });

        const responseHeaders = response.getAllHeaders();

        if (!("Location" in responseHeaders)) {
          CloudStorage.handleInsertError(
            "resumable",
            params.name,
            bucketName,
            "No location header in response.",
          );
          throw new Error(); // To satisfy the compiler, this line will never be reached
        }

        const sessionURI = responseHeaders.Location;

        if (!sessionURI) {
          CloudStorage.handleInsertError(
            "resumable",
            params.name,
            bucketName,
            "No session URI in response.",
          );
        }

        if (typeof sessionURI !== "string") {
          CloudStorage.handleInsertError(
            "resumable",
            params.name,
            bucketName,
            "Session URI is not a string.",
          );
          throw new Error(); // To satisfy the compiler, this line will never be reached
        }

        // Upload the file in chunks
        let chunkFirstByte = 0;
        let responseCode = 0;
        let chunkResponse;
        do {
          let chunkLastByte = chunkFirstByte + chunkSize - 1;
          chunkLastByte = chunkLastByte > totalObjectSize - 1 ? totalObjectSize - 1 : chunkLastByte;
          const chunk = data
            .getBytes()
            .slice(chunkFirstByte, chunkLastByte + 1);
          const chunkBlob = Utilities.newBlob(chunk);
          const contentRange = `bytes ${chunkFirstByte}-${chunkLastByte}/${totalObjectSize}`;
          const chunkOptions: URLFetchRequestOptions = {
            method: "put",
            headers: {
              "Content-Range": contentRange,
            },
            payload: chunkBlob,
            muteHttpExceptions: true,
          };

          chunkResponse = UrlFetchApp.fetch(sessionURI, chunkOptions);

          // Verify that the response is valid and successful
          responseCode = chunkResponse.getResponseCode();
          const isNotRedirectOrSuccess = responseCode !== 308 &&
            !responseCode.toString().startsWith("2");

          if (isNotRedirectOrSuccess) {
            CloudStorage.handleInsertError(
              "resumable",
              params.name,
              bucketName,
              `Response code ${responseCode} is not successful.\nSession URI: ${sessionURI}`,
            );
          }
          if (responseCode.toString().startsWith("2")) break;
          const responseHeaders = chunkResponse.getAllHeaders();
          if (!("Range" in responseHeaders)) {
            CloudStorage.handleInsertError(
              "resumable",
              params.name,
              bucketName,
              `Range not in response headers\nSession URI: ${sessionURI}`,
            );
            throw new Error(); // To satisfy the compiler, this line will never be reached
          }
          const range = responseHeaders.Range;
          if (!range) {
            CloudStorage.handleInsertError(
              "resumable",
              params.name,
              bucketName,
              `Range header is empty\nSession URI: ${sessionURI}`,
            );
          }
          if (typeof range !== "string") {
            CloudStorage.handleInsertError(
              "resumable",
              params.name,
              bucketName,
              `Range header is not a string\nSession URI: ${sessionURI}`,
            );
            throw new Error(); // To satisfy the compiler, this line will never be reached
          }

          // Prepare for the next chunk
          const rangeParts = range.split("-");
          chunkFirstByte = parseInt(rangeParts[1]) + 1;
        } while (responseCode === 308);

        try {
          const responseJSON = JSON.parse(chunkResponse.getContentText());
          return responseJSON as CloudStorageTypes.Objects.Object;
        } catch (e) {
          CloudStorage.handleInsertError(
            "resumable",
            params.name,
            bucketName,
            `Could not parse response JSON\nSession URI: ${sessionURI}`,
          );
        }
      }

      // Multipart upload. https://cloud.google.com/storage/docs/uploading-objects#:~:text=JSON%20API-,multipart,-upload%20(a%20single
      if (params.uploadType === "multipart") {
        const boundary = "---" + Math.trunc(Math.random() * 1e15).toString(36) +
          "---";
        const templateBeforePdf = `--${boundary}\r\n` +
          "Content-Type: application/json; charset=UTF-8\r\n\r\n" +
          `${JSON.stringify(metadata)}\r\n` +
          `--${boundary}\r\n` +
          `Content-Type: ${contentType}\r\n\r\n`;
        const templateAfterPdf = `\r\n--${boundary}--`;

        const templateBeforePdfBytes = Utilities.newBlob(templateBeforePdf)
          .getBytes();
        const templateAfterPdfBytes = Utilities.newBlob(templateAfterPdf)
          .getBytes();
        const multipartRequestBody = templateBeforePdfBytes
          .concat(data.getBytes())
          .concat(templateAfterPdfBytes);

        const { url, options } = this.createAPIRequest<
          CloudStorageTypes.Objects.InsertQueryParameters
        >({
          baseURL: "https://storage.googleapis.com/upload/storage/v1",
          path: `/b/${input.bucketName}/o`,
          method: "post",
          params,
          headers,
          body: Utilities.newBlob(multipartRequestBody),
        });
        options.contentType = `multipart/related; boundary=${boundary}`;

        const response = UrlFetchApp.fetch(url, options);

        // Verify that the response is valid and successful
        this.handleAPIResponse<
          CloudStorageTypes.Objects.InsertInput,
          CloudStorageTypes.Objects.Object | CloudStorageTypes.Objects.Object[]
        >({
          response,
          call: CloudStorage.Objects.insert,
          methodParams: {
            bucketName,
            params,
            contentType,
            data,
            headers,
          },
        });

        const responseJSON = JSON.parse(response.getContentText());
        return responseJSON as CloudStorageTypes.Objects.Object;
      }

      throw new Error(`Invalid upload type: ${params.uploadType}`);
    }

    /**
     * Uploads a batch of objects at once to a bucket. Currently only supports media and multipart upload type.
     * File's content must be passed as a Blob. The total size of the ruquest must be less than 50MB, as per GAS limitations.
     * For multipart uploads, the batch cannot exceed 40MB in size.
     * For the other upload types, the batch cannot exceed 50MB in size.
     * https://cloud.google.com/storage/docs/json_api/v1/objects/insert
     *
     * @static
     * @param {CloudStorageTypes.Objects.InsertInput} {
     *       bucketName,
     *       params,
     *       contentType,
     *       data,
     *       metadata,
     *       headers,
     *     }
     * @return {*}  {CloudStorageTypes.Objects.Object}
     */
    public static batchInsert(
      batchInput: CloudStorageTypes.Objects.BatchInsertInput,
    ): CloudStorageTypes.Objects.BatchInsertOutput {
      const { bucketName } = batchInput;

      const mediaInserts: CloudStorageTypes.Objects.InsertObject[] = [];
      const resumableInserts: CloudStorageTypes.Objects.InsertObject[] = [];
      const multipartInserts: CloudStorageTypes.Objects.InsertObject[] = [];
      const otherInserts: CloudStorageTypes.Objects.InsertObject[] = [];
      const results: CloudStorageTypes.Objects.BatchInsertOutput = {
        result: [],
        errors: [],
      };

      batchInput.objects.forEach((object) => {
        if (object.params.uploadType === "media") {
          mediaInserts.push(object);
        } else if (object.params.uploadType === "resumable") {
          resumableInserts.push(object);
        } else if (object.params.uploadType === "multipart") {
          multipartInserts.push(object);
        } else {
          otherInserts.push(object);
        }
      });
      // Media upload type https://cloud.google.com/storage/docs/uploading-objects#:~:text=in%20the%20request.-,Media%20upload,-(a%20single%2Drequest
      if (mediaInserts.length > 0) {
        const mediaInsertsWithMetadata = mediaInserts.filter(
          (insert) => insert.metadata,
        );
        if (mediaInsertsWithMetadata.length > 0) {
          try {
            CloudStorage.handleInsertError(
              "media",
              mediaInsertsWithMetadata
                .map((object) => object.params.name)
                .join(", "),
              bucketName,
              "Metadata is not supported for media upload type.",
            );
          } catch (e) {
            results.errors.push(e.message);
          }
        }

        const mediaInsertsOverThreshold = mediaInserts.filter(
          (insert) => insert.data.getBytes().length > FILE_SIZE_THRESHOLD,
        );
        if (mediaInsertsOverThreshold.length > 0) {
          try {
            CloudStorage.handleInsertError(
              "media",
              mediaInsertsOverThreshold
                .map((object) => object.params.name)
                .join(", "),
              bucketName,
              "Media upload type is not supported for files larger than 40MB.",
            );
          } catch (e) {
            results.errors.push(e.message);
          }
        }

        const requests = batchInput.objects.map((object) => {
          const { url, options } = this.createAPIRequest<
            CloudStorageTypes.Objects.InsertQueryParameters
          >({
            baseURL: "https://storage.googleapis.com/upload/storage/v1",
            path: `/b/${bucketName}/o`,
            method: "post",
            params: object.params,
            headers: object.headers,
            body: object.data,
          });
          options.contentType = object.contentType;
          const request: GoogleAppsScript.URL_Fetch.URLFetchRequest = {
            ...options,
            url,
          };
          return request;
        });

        const responses = UrlFetchApp.fetchAll(requests);

        results.result.push(
          ...(responses.map((response, index) => {
            return this.handleAPIResponse<
              CloudStorageTypes.Objects.InsertInput,
              CloudStorageTypes.Objects.Object
            >({
              response,
              call: CloudStorage.Objects.insert,
              methodParams: {
                bucketName,
                params: batchInput.objects[index].params,
                contentType: batchInput.objects[index].contentType,
                data: batchInput.objects[index].data,
                headers: batchInput.objects[index].headers,
              },
            });
          }) as CloudStorageTypes.Objects.Object[]),
        );
      }

      // Resumable upload. https://cloud.google.com/storage/docs/resumable-uploads
      if (resumableInserts.length > 0) {
        try {
          CloudStorage.handleInsertError(
            "resumable",
            resumableInserts.map((object) => object.params.name).join(", "),
            bucketName,
            "Resumable upload not supported for Batch Insert.",
          );
        } catch (e) {
          results.errors.push(e.message);
        }
      }

      // Multipart upload. https://cloud.google.com/storage/docs/uploading-objects#:~:text=JSON%20API-,multipart,-upload%20(a%20single
      if (multipartInserts.length > 0) {
        const mediaInsertsOverThreshold = mediaInserts.filter(
          (insert) => insert.data.getBytes().length > FILE_SIZE_THRESHOLD,
        );
        if (mediaInsertsOverThreshold.length > 0) {
          try {
            CloudStorage.handleInsertError(
              "media",
              mediaInsertsOverThreshold
                .map((object) => object.params.name)
                .join(", "),
              bucketName,
              "Media upload type is not supported for files larger than 40MB.",
            );
          } catch (e) {
            results.errors.push(e.message);
          }
        }

        const boundary = "---" + Math.trunc(Math.random() * 1e15).toString(36) +
          "---";
        const templateAfterData = `\r\n--${boundary}--`;
        const templateAfterDataBytes = Utilities.newBlob(templateAfterData)
          .getBytes();

        const requests = batchInput.objects.map((object) => {
          const { params, headers, contentType, metadata, data } = object;

          const { url, options } = this.createAPIRequest<
            CloudStorageTypes.Objects.InsertQueryParameters
          >({
            baseURL: "https://storage.googleapis.com/upload/storage/v1",
            path: `/b/${bucketName}/o`,
            method: "post",
            params,
            headers,
          });
          options.contentType = contentType;

          const templateBeforeData = `--${boundary}\r\n` +
            "Content-Type: application/json; charset=UTF-8\r\n\r\n" +
            `${JSON.stringify(metadata)}\r\n` +
            `--${boundary}\r\n` +
            `Content-Type: ${contentType}\r\n\r\n`;
          const templateBeforeDataBytes = Utilities.newBlob(templateBeforeData)
            .getBytes();

          const multipartRequestBody = templateBeforeDataBytes
            .concat(data.getBytes())
            .concat(templateAfterDataBytes);

          options.payload = multipartRequestBody;
          options.contentType = `multipart/related; boundary=${boundary}`;

          const request: GoogleAppsScript.URL_Fetch.URLFetchRequest = {
            ...options,
            url,
          };
          return request;
        });

        const responses = UrlFetchApp.fetchAll(requests);

        // Verify that the responses are valid and successful
        results.result.push(
          ...(responses.map((response, index) => {
            return this.handleAPIResponse<
              CloudStorageTypes.Objects.InsertInput,
              | CloudStorageTypes.Objects.Object
              | CloudStorageTypes.Objects.Object[]
            >({
              response,
              call: CloudStorage.Objects.insert,
              methodParams: {
                bucketName,
                params: batchInput.objects[index].params,
                contentType: batchInput.objects[index].contentType,
                data: batchInput.objects[index].data,
                headers: batchInput.objects[index].headers,
              },
            });
          }) as CloudStorageTypes.Objects.Object[]),
        );
      }

      // throw new Error(`Invalid upload type: ${params.uploadType}`);
      if (otherInserts.length > 0) {
        try {
          CloudStorage.handleInsertError(
            "other",
            otherInserts.map((object) => object.params.name).join(", "),
            bucketName,
            "Invalid upload type.",
          );
        } catch (e) {
          results.errors.push(e.message);
        }
      }

      return results;
    }

    /**
     * Lists objects in a bucket
     * https://cloud.google.com/storage/docs/json_api/v1/objects/list
     *
     * @static
     * @param {CloudStorageTypes.Objects.ListInput} {
     *       bucketName,
     *       params,
     *       headers,
     *     }
     * @return {*}  {CloudStorageTypes.Objects.ListResponse}
     */
    public static list({
      bucketName,
      params,
      headers,
    }: CloudStorageTypes.Objects.ListInput): CloudStorageTypes.Objects.ListResponse {
      const { url, options } = this.createAPIRequest<
        CloudStorageTypes.Objects.ListQueryParameters
      >({
        baseURL: CloudStorage.baseURL,
        path: `/b/${bucketName}/o`,
        method: "get",
        params,
        headers,
      });

      const response = UrlFetchApp.fetch(url, options);

      return this.handleAPIResponse<
        CloudStorageTypes.Objects.ListInput,
        CloudStorageTypes.Objects.ListResponse
      >({
        response,
        call: CloudStorage.Objects.list,
        methodParams: {
          bucketName,
          params,
          headers,
        },
      });
    }

    /**
     * Create a generic API request using the given types. This is used to create a request for any API call.
     *
     * @private
     * @static
     * @template Params
     * @param {({
     *       baseURL: string;
     *       path: string;
     *       method: HttpMethod;
     *       params?: Params;
     *       body?: string | Blob;
     *       headers?: CloudStorageTypes.Objects.ExtensionHeaders;
     *     })} {
     *       baseURL,
     *       path,
     *       method,
     *       params,
     *       body,
     *       headers,
     *     }
     * @return {*}  {{
     *       url: string;
     *       options: URLFetchRequestOptions;
     *     }}
     */
    private static createAPIRequest<Params>({
      baseURL,
      path,
      method,
      params,
      body,
      headers,
    }: {
      baseURL: string;
      path: string;
      method: HttpMethod;
      params?: Params;
      body?: string | Blob;
      headers?: CloudStorageTypes.Objects.ExtensionHeaders;
    }): {
      url: string;
      options: URLFetchRequestOptions;
    } {
      const urlPath = baseURL.replace(/\/$/, "") + "/" +
        path.replace(/^\//, "");
      let queryString = "?";

      if (params) {
        for (const [key, value] of Object.entries(params)) {
          queryString += `${key}=${value}&`;
        }
      }

      const url = encodeURI(urlPath + queryString);

      const options: URLFetchRequestOptions = {
        method: method,
        headers: {
          Authorization: `Bearer ${CloudStorage.token}`,
          ...headers,
        },
        muteHttpExceptions: true,
        payload: body ? body : "",
      };

      return { url, options };
    }

    /**
     * Handle the response from an API call. This is used to handle any API call.
     *
     * @private
     * @static
     * @template I
     * @template O
     * @param {{
     *       response: GoogleAppsScript.URL_Fetch.HTTPResponse;
     *       call: (params: I) => O;
     *       methodParams: I;
     *     }} {
     *       response,
     *       call,
     *       methodParams,
     *     }
     * @return {*}  {O}
     */
    private static handleAPIResponse<I, O>({
      response,
      call,
      methodParams,
    }: {
      response: GoogleAppsScript.URL_Fetch.HTTPResponse;
      call: (params: I) => O;
      methodParams: I;
    }): O {
      const responseHeaders = response.getHeaders();

      if (!("Content-Type" in responseHeaders)) {
        throw new Error("No Content-Type header in response");
      }
      const responseContentType = responseHeaders["Content-Type"];
      if (typeof responseContentType !== "string") {
        throw new Error("Content-Type header is not a string");
      }

      let responseContent;
      if (responseContentType.includes("text")) {
        responseContent = response.getContentText() as string;
      } else if (responseContentType.includes("json")) {
        responseContent = JSON.parse(
          response.getContentText(),
        ) as CloudStorageTypes.Objects.Object;
      } else responseContent = response.getBlob().getBytes() as Byte[];

      if (response.getResponseCode().toString().startsWith("2")) {
        return responseContent as O;
      }
      const errorResponse = responseContent as unknown as {
        error: {
          code: number;
          message: string;
          errors: { message: string; domain: string; reason: string }[];
        };
      };
      const isAuthError = response.getResponseCode() === 401 &&
        errorResponse?.error?.errors[0]?.reason === "authError";

      if (isAuthError) {
        CloudStorage.refreshToken();
        return call(methodParams);
      }

      Logger.log(responseContent);
      throw new Error(errorResponse?.error?.message);
    }
  };

  private static handleInsertError(
    type: string,
    name: string,
    bucketName: string,
    details: string,
  ) {
    Logger.log(
      `👀 Tried a ${type} upload for ${name} to ${bucketName}.\n${details}`,
    );
    throw new Error(details);
  }
}
