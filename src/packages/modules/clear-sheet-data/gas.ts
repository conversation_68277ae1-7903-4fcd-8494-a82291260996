/**
 * Clears sheet of previous values. Takes the sheet and a starting row.
 * @param sheet
 * @param startingRow
 */
export const clearSheetData = (
  sheet: GoogleAppsScript.Spreadsheet.Sheet,
  startingRow: number,
) => {
  console.log("clearing data in sheet", sheet.getName());
  const range = sheet.getRange(
    startingRow,
    1,
    sheet.getLastRow(),
    sheet.getLastColumn(),
  );
  range.clear();
};
