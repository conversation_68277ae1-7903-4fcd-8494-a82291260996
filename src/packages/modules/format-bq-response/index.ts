export function formatBQResponse(
  bqResponse: GoogleAppsScript.BigQuery.Schema.QueryResponse,
) {
  console.log("Formatting big query data");

  const { rows = [], schema } = bqResponse;

  const formattedClientInfo = rows.map((currentRow) => {
    const newRow: { [key: string]: string } = {};

    schema?.fields?.forEach((field, index) => {
      const nameOfField = field.name as keyof typeof newRow;
      const currentField = currentRow.f?.[index];
      const currentValue = currentField?.v as unknown as string;
      const valueOrBlank = currentValue ?? ""; // Transform null values into blank spaces.
      if (currentValue !== "N/A") {
        newRow[nameOfField] = valueOrBlank;
      }
    });

    return newRow;
  });

  console.log(`rows from bigQuery successfully formatted ✅`);

  return formattedClientInfo;
}
