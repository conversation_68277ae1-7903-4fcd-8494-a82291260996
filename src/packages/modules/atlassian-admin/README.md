# Atlassian Admin Module

This module provides a wrapper around the Organizations REST API, providing resources for managing an Atlassian organization and allowing you to interact with various Jira resources such as users and groups.

### Prerequisites

- Deno
- Atlassian Admin credentials

### Environment Variables

You need to set the following environment variables in the packageJSON of the process:

- `ATLASSIAN_ADMIN_CREDENTIALS`: JSON containing your Atlassian API credentials.

### Usage

To interact with the Atlassian Admin API, you need to create an instance of the `AtlassianAdminClient` class. The class handles authentication and provides methods to perform various operations in Jira.

### Conclusion

This module provides a comprehensive wrapper around the Organizations REST API, making it easy to interact with various Atlassian resources. By following the examples and guidelines provided in this README, you should be able to effectively use the module in your projects. For more information, refer to the [Atlassian Admin REST API Guide](https://developer.atlassian.com/cloud/admin/organization/rest/intro/#about)
