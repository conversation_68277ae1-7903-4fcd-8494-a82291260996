import { zodValidation } from "modules/zod-validation/index.ts";
import "load-env";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { AtlassianAdminSecret, atlassianAdminSecretSchema, ProductAccess, UserResponse, Workspace, WorkspacesResponse } from "modules/atlassian-admin/types.ts";
import { Jira } from "modules/jira/types.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["ATLASSIAN_ADMIN_CREDENTIALS"]);

/**
 * Returns AtlassianAdminClient.
 * @returns AtlassianAdminClient
 */
export const getAtlassianAdminClient = () => {
  const secretEnv = Deno.env.get("ATLASSIAN_ADMIN_CREDENTIALS") as string;
  const secret = JSON.parse(secretEnv) as AtlassianAdminSecret;
  zodValidation({
    objectToTest: secret,
    schema: atlassianAdminSecretSchema,
    description: "Validating Atlassian Admin Secret",
  });
  const { organizationId, key } = secret;
  const jiraClient = new AtlassianAdminClient(organizationId, key);
  return jiraClient;
};

/**
 * Wrapper class for Organizations REST API. It provides resources for managing an Atlassian organization.
 * https://developer.atlassian.com/cloud/admin/organization/rest/intro/#About
 *
 * @export
 * @class AtlassianAdminClient
 */
export class AtlassianAdminClient {
  private readonly organizationId: string = "";
  private readonly key: string;
  readonly baseUrl: string;
  params: RequestInit;

  /**
   * Constructor of AtlassianAdminClient.
   * @param organizationId - Ebury organization id
   * @param key - API key
   */
  constructor(organizationId: string, key: string) {
    this.organizationId = organizationId;
    this.key = key;
    this.baseUrl = `https://api.atlassian.com/admin/`;
    this.params = {
      headers: {
        Authorization: `Bearer ${this.key}`,
        "Content-Type": "application/json",
      },
    };
  }

  /**
   * Generalization of the API call to the Jira platform.
   *
   * @template T - The type of the response.
   * @param {string} relativeEndpoint - The URL path relative to the base URL.
   * @param {string} method - The method to use for the call.
   * @param {unknown} [payload] - The payload to send to the endpoint.
   * @returns {Promise<T>} - The response from the API.
   * @throws {Error} - Throws an error if the API call fails.
   */
  private async makeCall<T>(
    relativeEndpoint: string,
    method: string,
    payload?: unknown,
  ): Promise<T> {
    const options: RequestInit = {
      method,
      headers: {
        Authorization: `Bearer ${this.key}`,
        "Content-Type": "application/json",
      },
    };
    if (payload) {
      options.body = JSON.stringify(payload);
    }
    const url = this.baseUrl + relativeEndpoint;
    const response = await fetch(url, options);
    const responseText = await response.text();
    const statusCode = response.status;
    const isSuccessful = statusCode.toString().startsWith("2") ||
      statusCode.toString() === "304";
    if (!isSuccessful) {
      throw new Error(
        `Call to URL failed, status code: ${statusCode}, message: ${responseText}`,
      );
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
    throw new Error(`Empty response from URL: ${url}`);
  }

  /**
   * Gets the last connection of a user.
   * @see https://developer.atlassian.com/cloud/admin/organization/rest/api-group-directory/#api-orgs-orgid-directory-users-accountid-last-active-dates-get
   * This call returns the last connection of a user to all products of the organization.
   * We use the productId to filter the response and get the last connection of the user to a specific product.
   * @param {string} employee - The user.
   * @param {string} productId - The product id.
   * @returns {Promise<string>} - The last connection of the user. If the user has never logged in, it will return undefined.
   * @throws {Error} If there is an error fetching the user's last connection.
   */
  public async getUserLastConnection(
    employee: Jira.UserFromGroup,
    productId: string,
  ): Promise<string | undefined> {
    const accountId = employee.accountId;
    const relativeEndpoint = `v1/orgs/${this.organizationId}/directory/users/${accountId}/last-active-dates`;
    const method = "GET";
    const data = await this.makeCall<UserResponse>(relativeEndpoint, method);

    const lastAccessToProducts: ProductAccess[] = data.data.product_access;
    const lastAccessToWantedProduct = lastAccessToProducts.filter((product) => product.id === productId);

    if (lastAccessToWantedProduct.length > 1) {
      throw new Error(`Error accessing \`last-active-dates\` of product for user ${accountId}, more than one result found`);
    }

    if (lastAccessToWantedProduct.length === 0) {
      Logger.warning(`lastAccessToWantedProduct is empty for user ${employee.emailAddress} with id ${accountId}`);
      return undefined;
    }

    const lastLogin = lastAccessToWantedProduct[0].last_active;
    if (!lastLogin) {
      return undefined;
    }
    return lastLogin;
  }

  /**
   * Gets the workspaces of the organization.
   *
   * @param {number} [limit=100] - The maximum number of workspaces to retrieve.
   * @returns {Promise<Workspace[]>} - The list of workspaces.
   * @throws {Error} - Throws an error if there is an issue retrieving the workspaces.
   */
  public async getOrgWorkspaces(limit = 100): Promise<Workspace[]> {
    try {
      const relativeEndpoint = `v2/orgs/${this.organizationId}/workspaces`;
      const method = "POST";
      const payload = { limit };
      const data = await this.makeCall<WorkspacesResponse>(relativeEndpoint, method, payload);

      if (!data || !data.data) {
        throw new Error(`Workspaces not found for Ebury`);
      }

      return data.data as Workspace[];
    } catch (error) {
      Logger.error(`Error getting workspaces: ${error.message}`);
      throw error;
    }
  }
}
