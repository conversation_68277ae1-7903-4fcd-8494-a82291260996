import { z } from "zod";

export const atlassianAdminSecretSchema = z.object({
  organizationId: z.string(),
  key: z.string(),
  expirationDate: z.string(),
});

export type AtlassianAdminSecret = z.infer<typeof atlassianAdminSecretSchema>;

export type ProductAccess = {
  key: string;
  id: string;
  last_active?: string;
};

export type ProductAccessData = {
  product_access: ProductAccess[];
};

export type UserResponse = {
  links: {
    next: string | null;
  };
  data: ProductAccessData;
};

export type Attributes = {
  name: string;
  typeKey: string;
  type: string;
  owner: string;
  status: string;
  statusDetails: string[];
  icons: Record<string, unknown>;
  avatars: Record<string, unknown>;
  labels: string[];
  sandbox: {
    type: string;
  };
  usage: number;
  capacity: number;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  hostUrl: string;
  realm: string;
  regions: string[];
};

export type Workspace = {
  id: string;
  type: string;
  attributes: Attributes;
  links: {
    self: string;
  };
  relationships: Record<string, unknown>;
};

export type WorkspacesResponse = {
  data: Workspace[];
  links: {
    self: string;
    prev: string;
    next: string;
  };
  meta: {
    pageSize: number;
    startIndex: number;
    endIndex: number;
    total: number;
  };
};
