// deno-lint-ignore-file no-namespace
import z from "zod";
import {
  FindGroupsResponseSchema,
  GroupLabelSchema,
  GroupSchema,
} from "./schemas.ts";

export namespace Jira {
  export type Secret = {
    email: string;
    apiKey: string;
  };
  export type IssueLinkType = {
    id: string;
    inward: string;
    name: string;
    outward: string;
    self: string;
  };

  export type IssueLinkTypesResponse = {
    issueLinkTypes: IssueLinkType[];
  };

  export type UserGroup = {
    name: string;
    groupId: string;
  };

  export type FailedGroups = {
    groupId: string;
    error: string;
  };

  export type ApplicationRole = {
    key: string;
    groups: string[];
    groupDetails: GroupName[];
    name: string;
    defaultGroups: string[];
    defaultGroupsDetails: GroupName[];
    selectedByDefault: boolean;
    defined?: boolean;
    numberOfSeats: number;
    remainingSeats: number;
    userCount: number;
    userCountDescription: string;
    hasUnlimitedSeats: boolean;
    platform: boolean;
  };
  export type UserFromGroup = {
    self: string;
    accountId: string;
    avatarUrls: AvatarUrlsBean;
    displayName: string;
    active: boolean;
    timeZone: string;
    accountType: string;
    emailAddress: string;
  };

  export type GroupMembersResponse = {
    self: string;
    nextPage?: string;
    maxResults: number;
    startAt: number;
    total: number;
    isLast: boolean;
    values: UserFromGroup[];
  };

  export type AvatarUrlsBean = {
    "16x16": string;
    "24x24": string;
    "32x32": string;
    "48x48": string;
  };

  export type AddUserToGroupResponse =
    | {
      expand: string;
      groupId: string;
      name: string;
      self: string;
      users: {
        "end-index": number;
        items: {
          accountId: string;
          accountType: string;
          active: boolean;
          avatarUrls: AvatarUrlsBean;
          displayName: string;
          emailAddress: string;
          key: string;
          name: string;
          self: string;
          timeZone: string;
        }[];
        "max-results": number;
        size: number;
        "start-index": number;
      };
    }
    | {
      errorMessages: string[];
    };

  export type JiraCommentObject = {
    body: {
      content: {
        content: {
          text: string;
          type: string;
        }[];
        type: string;
      }[];
      type: string;
      version: number;
    };
  };

  /**
   * A board for a Jira project, where issues are represented as cards.
   *
   * @see https://developer.atlassian.com/cloud/jira/software/rest/api-group-board/#api-group-board
   */
  export type Board = {
    id: number;
    self: string;
    name: string;
    type: string;
    admins?: {
      users: UserBean[];
      groups: {
        name: string;
        self: string;
      }[];
    };
    location?: {
      projectId?: number;
      userId?: number;
      userAccountId?: string;
      displayName?: string;
      projectName?: string;
      projectKey?: string;
      projectTypeKey?: string;
      avatarURI?: string;
      name?: string;
    };
    canEdit?: boolean;
    isPrivate?: boolean;
    favourite?: boolean;
  };

  /**
   * Configuration details to creat a {@link Board}.
   *
   * @see https://developer.atlassian.com/cloud/jira/software/rest/api-group-board/#api-group-board
   */
  export type BoardConfig = {
    name: string;
    type: "kanban" | "scrum" | "agility";
    filterId: number;
    location?: {
      type: string;
      projectKeyOrId: string;
    };
  };

  export type BulkCreateIssuesError = {
    issues: unknown[];
    errors: BulkOperationErrorResult[];
  };

  export type BulkOperationErrorResult = {
    status: number;
    elementErrors: ErrorCollection;
    failedElementNumber: number;
  };

  export type ChangeDetails = {
    field: string;
    fieldId: string;
    fieldtype: string;
    from: string;
    fromString: string;
    to: string;
    toString: string;
  };

  export type Changelog = {
    author: UserDetails;
    created: string;
    historyMetadata: HistoryMetadata;
    id: string;
    items: ChangeDetails[];
  };

  /**
   * Details about the issues created and the errors for requests that failed.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issues/#api-rest-api-3-issue-bulk-post
   */
  export type CreatedIssues = {
    issues: CreatedIssue[];
    errors: BulkOperationErrorResult[];
  };

  export type CreatedIssue = {
    id: string;
    key: string;
    self: string;
    transition: NestedResponse;
    watchers: NestedResponse;
  };
  /**
   * A container for a list of workflow schemes together with the projects they are associated with ({@link WorkflowSchemeAssociations}).
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-workflow-scheme-project-associations/#api-rest-api-3-workflowscheme-project-get
   */
  export type ContainerOfWorkflowSchemeAssociations = {
    values: WorkflowSchemeAssociations[];
  };

  export type CoreIssueBean = {
    changelog?: PageOfChangelogs;
    editmeta?: IssueUpdateMetadata;
    expand?: string;
    fields: {
      summary?: string;
      status?: StatusDetails;
      statuscategorychangedate?: string;
      created?: string;
      reporter?: UserDetails;
      resolutiondate?: string;
      resolution?: ResolutionDetails;
      fixVersions?: FixVersion[];
      issuetype?: IssueTypeDetails;
      assignee?: UserDetails;
      [key: string]: unknown;
    };
    fieldsToInclude?: IncludedFields;
    id?: string;
    key?: string;
    names?: { [key: string]: string };
    operations?: Operations;
    properties?: unknown;
    renderedFields?: { [key: string]: string };
    schema?: unknown;
    self?: string;
    transitions?: IssueTransition[];
    versionedRepresentations?: unknown;
  };

  export interface Comment {
    self: string;
    id: string;
    author: Author;
    body: string;
    updateAuthor: Author;
    created: string;
    updated: string;
    visibility: Visibility;
  }

  export interface Author {
    self: string;
    accountId: string;
    displayName: string;
    active: boolean;
  }

  export interface Body {
    type: string;
    version: number;
    content: BodyContent[];
  }

  export interface BodyContent {
    type: string;
    content: Content[];
  }

  export interface Content {
    type: string;
    text: string;
  }

  export interface Visibility {
    type: string;
    value: string;
    identifier: string;
  }

  /**
   * An entity property.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/jira-entity-properties/
   */
  export type EntityProperty = {
    key: string;
    value: unknown;
  };

  export type ErrorCollection = {
    errorMessages: string[];
    errors: unknown;
    status?: number;
  };

  export type EventNotification = {
    expand: string;
    id: string;
    notificationType: string;
    parameter: string;
    recipient: string;
    group: GroupName;
    field: FieldDetails;
    emailAddress: string;
    projecRole: ProjectRole;
    user: UserDetails;
  };

  export type FieldDetails = {
    id: string;
    key: string;
    name: string;
    custom: boolean;
    orderable: boolean;
    navigable: boolean;
    searchable: boolean;
    clauseNames: string[];
    scope: Scope;
    schema: JsonTypeBean;
  };

  export type FieldMetadata = {
    allowedValues?: string[];
    autoCompleteUrl?: string;
    configuration?: unknown;
    defaultValue?: string;
    hasDefaultValue?: boolean;
    key: string;
    name: string;
    operations: string[];
    required: boolean;
    schema: JsonTypeBean;
  };

  /**
   * Details about a filter.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-filters/#api-rest-api-3-filter-post
   */
  export type Filter = {
    self?: string;
    id?: string;
    name: string;
    description?: string;
    owner?: User;
    jql?: string;
    viewUrl?: string;
    searchUrl?: string;
    favourite?: boolean;
    favouritedCount?: number;
    sharePermissions?: SharePermission[];
    editPermissions?: SharePermission[];
    sharedUsers?: UserList;
    subscriptions?: FilterSubscriptionList;
  };
  /**
   * Configuration details to create a {@link Filter}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-filters/#api-rest-api-3-filter-post
   */
  export type FilterConfig = {
    name: string;
    description?: string;
    jql?: string;
    favourite?: boolean;
    sharePermissions?: SharePermission[];
    editPermissions?: SharePermission[];
  };

  export type FilterSubscription = {
    id: number;
    user: User;
    group: GroupName;
  };

  export type FilterSubscriptionList = {
    size: number;
    items: FilterSubscription[];
    "max-results": number;
    "start-index": number;
    "end-index": number;
  };

  export type FixVersion = {
    self?: string;
    id?: string;
    name?: string;
    archived?: boolean;
    released?: boolean;
    releaseDate?: string;
  };

  export type GroupName = {
    name: string;
    groupId: string;
    self: string;
  };

  export type Hierarchy = {
    baseLevelId: number;
    levels: SimplifiedHierarchyLevel[];
  };

  /**
   * Details of issue history metadata ({@link IssueUpdateDetails}).
   */
  export type HistoryMetadata = {
    type: string;
    description?: string;
    descriptionKey?: string;
    activityDescription?: string;
    activityDescriptionKey?: string;
    emailDescription?: string;
    emailDescriptionKey?: string;
    actor?: HistoryMetadataParticipant;
    generator?: HistoryMetadataParticipant;
    cause?: HistoryMetadataParticipant;
    extraData?: unknown;
    [key: string]: unknown; // Additional Properties
  };

  /**
   * Details of user or system associated with a {@link HistoryMetadata} item.
   */
  export type HistoryMetadataParticipant = {
    id: string;
    displayName: string;
    displayKey: string;
    type: string;
    avatarUrl: string;
    url: string;
    [key: string]: unknown; // Additional Properties
  };

  export type IncludedFields = {
    actuallyIncluded?: string[];
    excluded?: string[];
    included?: string[];
  };

  /**
   * Details of an issue transition ({@link IssueUpdateDetails}).
   */
  export type IssueTransition = {
    id: string;
    looped?: boolean;
    [key: string]: unknown; // Additional Properties
  };

  export type Attachment = {
    self: string;
    id: number;
    filename: string;
    author: {
      self: string;
      name: null | string;
      key: null | string;
      accountId: string;
      emailAddress: null | string;
      avatarUrls: {
        "48x48": string;
        "24x24": string;
        "16x16": string;
        "32x32": string;
      };
      displayName: string;
      active: boolean;
      timeZone: string;
      groups: null | string;
      locale: null | string;
      accountType: "atlassian";
    };
    created: number;
    size: number;
    mimeType: string;
    content: string;
  };

  export type IssueCreate = {
    fields: {
      reporter?: {
        id: string;
      };
      issuetype: {
        name:
          | `Task`
          | `Epic`
          | `Subtask`
          | "Story"
          | "Bug"
          | "Netsuite Access Request"
          | "Service Request"
          | "Questions for HR"
          | "General request"
          | "Banking/Trading Platforms Access Request";
      } | {
        id: string;
      };
      summary: string;
      project: {
        id: string;
      };
      labels?: string[];
      [key: string]: unknown;
      description: {
        content: [
          {
            content: [
              {
                text: string;
                type: "text";
              },
            ];
            type: "paragraph";
          },
        ];
        type: "doc";
        version: 1;
      };
    };
  };

  export type IssueTypeEbOps = "EDR" | "PR" | "RFI";

  export type IssueCreateEburyOps = {
    fields: {
      reporter: {
        id: string;
      };
      issuetype: {
        name: IssueTypeEbOps;
      };
      summary: string;
      project: {
        id: string;
      };
      labels?: string[];
      [key: string]: unknown;
      description?: {
        content: [
          {
            content: [
              {
                text: string;
                type: "text";
              },
            ];
            type: "paragraph";
          },
        ];
        type: "doc";
        version: 1;
      };
    };
  };

  export type IssueTypeDetails = {
    self: string;
    id: string;
    description: string;
    iconUrl: string;
    name: string;
    subtask: boolean;
    avatarId: number;
    entityId: string;
    hierarchyLevel: number;
    scope: Scope;
  };

  export type IssueTypeScheme = {
    id: string;
    name: string;
    description?: string;
    defaultIssueTypeId?: string;
    isDefault?: boolean;
  };

  export type IssueTypeSchemeProjects = {
    issueTypeScheme: IssueTypeScheme;
    projectIds: string[];
  };

  export type IssueTypeScreenScheme = {
    id: string;
    name: string;
    description?: string;
  };

  export type IssueTypeScreenSchemesProjects = {
    issueTypeScreenScheme: IssueTypeScreenScheme;
    projectIds: string[];
  };

  /**
   * Details of an issue update request.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issues/#api-rest-api-3-issue-bulk-post:~:text=Array%3C-,IssueUpdateDetails,-%3E
   */
  export type IssueUpdateDetails = {
    issue: {
      transition?: IssueTransition;
      fields: {
        // Loosely defined
        [key: string]: unknown; // Additional Properties
      };
      update?: unknown;
      historyMetadata?: HistoryMetadata;
      properties?: EntityProperty[];
      [key: string]: unknown; // Additional Properties
    };
  };

  export type IssueUpdateMetadata = {
    fields: { [key: string]: FieldMetadata };
  };

  export type JsonTypeBean = {
    type: string;
    items?: string;
    system?: string;
    custom?: string;
    customId?: number;
    configuration?: unknown;
  };

  export type LinkGroup = {
    groups?: LinkGroup[];
    header?: SimpleLink;
    id?: string;
    links?: SimpleLink[];
    styleClass?: string;
    weight?: number;
  };

  export type NestedResponse = {
    status: number;
    errorCollection: ErrorCollection;
  };

  export type NotificationEvent = {
    id: string;
    name: string;
    description: string;
    templateEvent: NotificationEvent | unknown;
  };

  export type NotificationScheme = {
    expand: string;
    id: string;
    self: string;
    name: string;
    description: string;
    notificationSchemeEvents: NotificationSchemeEvent[];
    scope: Scope;
    projects: number[];
  };

  export type NotificationSchemeEvent = {
    event: NotificationEvent;
    notifications: EventNotification[];
  };

  export type Operations = {
    linkGroups?: LinkGroup[];
    [key: string]: unknown; // Additional Properties
  };

  /**
   * A page of {@link IssueTypeSchemeProjects}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issue-type-schemes/#api-rest-api-3-issuetypescheme-project-get
   */
  export type PageBeanIssueTypeSchemeProjects = {
    self: string;
    nextPage: string;
    maxResults: number;
    startAt: number;
    total: number;
    isLast: boolean;
    values: IssueTypeSchemeProjects[];
  };

  /**
   * A page of {@link IssueTypeScreenSchemesProjects}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issue-type-screen-schemes/#api-rest-api-3-issuetypescreenscheme-project-get
   */
  export type PageBeanIssueTypeScreenSchemesProjects = {
    self: string;
    nextPage: string;
    maxResults: number;
    startAt: number;
    total: number;
    isLast: boolean;
    values: IssueTypeScreenSchemesProjects[];
  };

  /**
   * A page of {@link NotificationScheme}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issue-notification-schemes/#api-rest-api-3-notificationscheme-get
   */
  export type PageBeanNotificationScheme = {
    self: string;
    nextPage: string;
    maxResults: number;
    startAt: number;
    total: number;
    isLast: boolean;
    values: NotificationScheme[];
  };

  export type PageOfChangelogs = {
    startAt: number;
    maxResults: number;
    total: number;
    histories: Changelog[];
  };

  /**
   * Details about a project.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-projects/#api-rest-api-3-project-projectidorkey-get
   */
  export type Project = {
    expand: string;
    self: string;
    id: string;
    key: string;
    description: string;
    lead: User;
    components: ProjectComponent[];
    issueTypes: IssueTypeDetails[];
    url: string;
    email: string;
    assigneeType: string;
    versions: Version[];
    name: string;
    roles: unknown;
    avartarUrls: AvatarUrlsBean;
    projectCategory: ProjectCategory;
    projectTypeKey: string;
    simplified: boolean;
    style: string;
    favourite: boolean;
    isPrivate: boolean;
    issueTypeHierarchy: Hierarchy;
    permissions: ProjectPermissions;
    properties: unknown;
    uuid: string;
    insight: ProjectInsight;
    deleted: boolean;
    retentionTillDate: string;
    deletedDate: string;
    deletedBy: User;
    archived: boolean;
    archivedDate: string;
    archivedBy: User;
    landingPageInfo: ProjectLandingPageInfo;
  };

  export type ProjectCategory = {
    self: string;
    id: string;
    name: string;
    description: string;
  };

  export type ProjectComponent = {
    self: string;
    id: string;
    name: string;
    description: string;
    lead: User;
    leadUserName: string;
    assigneeType: string;
    assignee: User;
    realAssigneeType: string;
    realAssignee: User;
    isAssigneeTypeValid: boolean;
    project: string;
    projectId: number;
  };

  /**
   * Configuration details to create a {@link Project}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-projects/#api-rest-api-3-project-post
   */
  export type ProjectConfig = {
    key: string;
    name: string;
    description?: string;
    leadAccountId?: string;
    url?: string;
    assigneeType?: string;
    avatarId?: string;
    issueSecurityScheme?: string;
    permissionScheme?: string;
    notificationScheme?: string;
    categoryId?: string;
    projectTypeKey?: string;
    projectTemplateKey?: string;
    workflowScheme?: number;
    issueTypeScreenScheme?: string;
    issueTypeScheme?: string;
    fieldConfigurationScheme?: string;
  };

  /**
   * Identifiers for a {@link Project}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-projects/#api-rest-api-3-project-post
   */
  export type ProjectIdentifiers = {
    self: string;
    id: string;
    key: string;
  };

  export type ProjectInsight = {
    totalIssueCount: number;
    lastIssueUpdateTime: string;
  };

  export type ProjectLandingPageInfo = {
    url: string;
    projectKey: string;
    projectType: string;
    boardId: number;
    boardName: string;
    simpleBoard: boolean;
    queueId: number;
    queueName: string;
    queueCategory: string;
    attributes: unknown;
    simplified: boolean;
  };

  export type ProjectPermissions = {
    canEdit: boolean;
  };

  /**
   * Details about the {@link Role} in a {@link Project}.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-project-role-actors/#api-rest-api-3-project-projectidorkey-role-id-post
   */
  export type ProjectRole = {
    self: string;
    name: string;
    id: number;
    description: string;
    actors: RoleActor[];
    scope: Scope;
    translatedName: string;
    currentUserRole: boolean;
    default: boolean;
    admin: boolean;
    roleConfigurable: boolean;
  };

  export type ProjectRoleGroup = {
    displayName: string;
    name: string;
    groupId: string;
  };

  export type ProjectRoleUser = {
    accountId: string;
  };

  export type ResolutionDetails = {
    self?: string;
    id?: string;
    description?: string;
    name?: string;
  };

  export type RoleActor = {
    id: number;
    displayName: string;
    type: string;
    name: string;
    avatarUrl: string;
    actorUser: ProjectRoleUser;
    actorGroup: ProjectRoleGroup;
  };

  /**
   * The result of a JQL search.
   *
   * @see https://developer.atlassian.com/cloud/jira/platform/rest/v3/api-group-issue-search/#api-rest-api-3-search-get
   */
  export type SearchResults = {
    expand: string;
    startAt: number;
    maxResults: number;
    total: number;
    issues: CoreIssueBean[];
    warningMessages: string[];
    names: unknown;
    schema: unknown;
  };

  export type SharePermission = {
    type: string;
    project?: Project;
    role?: ProjectRole;
    group?: GroupName;
    user?: UserBean;
  };

  export type SimpleLink = {
    id?: string;
    styleClass?: string;
    iconClass?: string;
    label?: string;
    title?: string;
    href?: string;
    weight?: number;
  };

  export type SimpleListWrapperApplicationRole = {
    size: number;
    items: ApplicationRole[];
    pagingCallback?: unknown;
    callback?: unknown;
    "max-results": number;
  };

  export type SimpleListWrapperGroupName = {
    size: number;
    items: GroupName[];
    pagingCallback?: unknown;
    callback?: unknown;
    "max-results": number;
  };

  export type SimplifiedHierarchyLevel = {
    id: number;
    name: string;
    aboveLevelId: number;
    belowLevelId: number;
    projectConfigurationId: number;
    level: number;
    issueTypeIds: number[];
    externalUuid: string;
    hierarchyLevelNumber: number;
  };

  export type Scope = {
    type: string;
    /**
     * Note! Previously this type value was specified as ProjectDetails, but there wasn't any reference to a type or import, and no any other reference in the whole monorepo. If using this type, be aware of this and check the proper typings, updating this module.
     */
    project: string;
    [key: string]: unknown; // Additional Properties
  };

  export type StatusDetails = {
    self?: string;
    description?: string;
    iconUrl?: string;
    name?: string;
    id?: string | number;
    statusCategory?: {
      self?: string;
      id?: string | number;
      key?: string;
      colorName?: string;
      name?: string;
    };
  };

  export type User = {
    self: string;
    key: string;
    accountId: string;
    accountType: string;
    name: string;
    emailAddress: string;
    avatarUrls: AvatarUrlsBean;
    displayName: string;
    active: boolean;
    timeZone: string;
    locale: string;
    groups: SimpleListWrapperGroupName;
    applicationRoles: SimpleListWrapperApplicationRole;
    expand: string;
  };

  export type UserBean = {
    key: string;
    self: string;
    name: string;
    displayName: string;
    active: boolean;
    accountId: string;
    avatarUrls: AvatarUrlsBean;
  };

  export type UserDetails = {
    self: string;
    name: string;
    key: string;
    accountId: string;
    emailAddress: string;
    avatarUrls: AvatarUrlsBean;
    displayName: string;
    active: boolean;
    timeZone: string;
    accountType: string;
  };

  export type UserList = {
    size: number;
    items: User[];
    "max-results": number;
    "start-index": number;
    "end-index": number;
  };

  export type Version = {
    expand: string;
    self: string;
    id: string;
    description: string;
    name: string;
    archived: boolean;
    released: boolean;
    startDate: string;
    releaseDate: string;
    overdue: boolean;
    userStartDate: string;
    userReleaseDate: string;
    project: string;
    projectId: number;
    moveUnfixedIssuesTo: string;
    operations: SimpleLink[];
    issuesStatusForFixVersion: VersionIssuesStatus;
  };

  export type VersionIssuesStatus = {
    unmapped: number;
    toDo: number;
    inProgress: number;
    done: number;
    [key: string]: unknown; // Additional Properties
  };

  export type WorkflowScheme = {
    id: number;
    name: string;
    description: string;
    defaultWorkflow: string;
    issueTypeMappings: unknown;
    originalDefaultWorkflow: string;
    originalIssueTypeMappings: unknown;
    draft: boolean;
    lastModifiedUser: User;
    lastModified: string;
    self: string;
    updateDraftIfNeeded: boolean;
    issueTypes: unknown;
  };

  export type WorkflowSchemeAssociations = {
    projectIds: number[];
    workflowScheme: WorkflowScheme;
  };

  export type GroupLabelSchema = z.infer<typeof GroupLabelSchema>;
  export type Group = z.infer<typeof GroupSchema>;
  export type FindGroupsResponse = z.infer<typeof FindGroupsResponseSchema>;
}
