/**
 * Typescript utility types for flattening object types.
 * The most common usage will be to use FlattenObjectType.FlattenedObject<T> to flatten an object type.
 * @example
 * type Foo = {
 *   a: {
 *    b: string;
 *   };
 *   c: number;
 * };
 * type FlattenedFoo = FlattenObjectType.FlattenedObject<Foo>;
 * // type FlattenedFoo = {
 * //  "a.b": string;
 * //  "c": number;
 * // }
 * @see https://dev.to/tylim88/typescript-type-level-object-flattening-9fn
 */
export namespace FlattenObjectType {
  /**
   * Extracts the paths of an object type.
   * @example
   * type Foo = {
   *  a: {
   *   b: string;
   * };
   * };
   * type FooPaths = ToPaths<Foo>;
   * // type FooPaths =
   */
  export type ToPaths<T, P extends string = ""> = T extends Record<number, unknown> ? {
      [K in keyof T]: ToPaths<T[K], `${P}${K & string}.`>;
    }[keyof T]
    : { path: P extends `${infer P}.` ? P : never; type: T };
  export type FromPaths<T extends { path: string; type: unknown }> = {
    [P in T["path"]]: Extract<T, { path: P }>["type"];
  };
  export type FlattenedObject<T> = FromPaths<ToPaths<T>>;
}
