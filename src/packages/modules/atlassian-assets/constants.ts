/**
 * These two objects contain configuration for attribute IDs and object type IDs used in Atlassian Insight.
 * The IDs differ depending on the environment (production or sandbox).
 * The appropriate configuration is selected based on the `isProd` boolean passed to the query functions.
 *
 * Example usage:
 * const { attributeIds, objectTypeIds } = isProd ? prodConfig : sandboxConfig;
 */
export const prodConfig = {
  attributeIds: {
    employeeName: "45",
    employeeEmail: "52",
    employeeJobTitle: "50",
    employeeDepartment: "51",
    employeeSite: "69",
    employeeCountry: "107",
    employeeStatus: "55",
    employeeManager: "49",
    employeeIsManager: "87",
    employeeGpp: "86",
    employeeGoogleUser: "70",
    employeeManagerGoogleUser: "88",
    jobTitleName: "41",
    jobTitleDepartment: "197",
    siteName: "66",
    departmentName: "37",
    departmentStatus: "115",
  },
  objectTypeIds: {
    employee: "8",
    jobTitle: "7",
    site: "11",
    department: "6",
  },
};

export const sandboxConfig = {
  attributeIds: {
    employeeName: "82",
    employeeEmail: "89",
    employeeJobTitle: "87",
    employeeDepartment: "88",
    employeeSite: "132",
    employeeCountry: "135",
    employeeStatus: "92",
    employeeManager: "86",
    employeeIsManager: "136",
    employeeGpp: "134",
    employeeGoogleUser: "133",
    employeeManagerGoogleUser: "137",
    jobTitleName: "78",
    jobTitleDepartment: "104",
    siteName: "101",
    departmentName: "74",
    departmentStatus: "338",
  },
  objectTypeIds: {
    employee: "7",
    jobTitle: "6",
    site: "9",
    department: "5",
  },
};
