import z from "zod";

export const atlassianSecretSchema = z.object({
  email: z.string(),
  apiKey: z.string(),
});
export type AtlassianSecret = z.infer<typeof atlassianSecretSchema>;

export const createObjectSchema = z.object({
  objectTypeId: z.string(),
  attributes: z.array(z.object({
    objectTypeAttributeId: z.string(),
    objectAttributeValues: z.array(z.object({
      value: z.string(),
    })),
  })),
});
export type CreateObjectSchema = z.infer<typeof createObjectSchema>;

export type AtlassianObjects = "Job Title" | "Site" | "Department" | "Employees";
export const queryObjectCreatorSchema = z.record(
  z.enum(["Job Title", "Site", "Department"]),
  z.function().args(z.boolean(), z.string()).returns(createObjectSchema),
);
export type QueryObjectCreator = z.infer<typeof queryObjectCreatorSchema>;

export type FetchObjectResponse = {
  startAt: number;
  maxResults: number;
  total: number;
  values: AtlassianObject[];
  objectTypeAttributes: ObjectTypeAttributes[];
  hasMoreResults: boolean;
  last: boolean;
  isLast: boolean;
};

export type ObjectTypeAttributes = {
  workspaceId: string;
  globalId: string;
  id: string;
  objectType: ObjectType;
};

export type AtlassianObject = {
  workspaceId: string;
  globalId: string;
  id: string;
  label: string;
  objectKey: string;
  avatar: any;
  objectType: ObjectType;
  created: string;
  updated: string;
  hasAvatar: boolean;
  timestamp: number;
  _links: {
    self: string;
  };
  name: string;
  attributes: Attribute[];
};

export type ObjectType = {
  workspaceId: string;
  globalId: string;
  id: string;
  name: string;
  type: number;
  description: string;
  icon: {
    id: string;
    name: string;
    url16: string;
    url48: string;
  };
  position: number;
  created: string;
  updated: string;
  objectCount: number;
  objectSchemaId: string;
  inherited: boolean;
  abstractObjectType: boolean;
  parentObjectTypeInherited: boolean;
};

export type AttributeValue = {
  value?: string;
  searchValue: string;
  referencedType: boolean;
  displayValue: string;
  referencedObject?: {
    workspaceId: string;
    globalId: string;
    id: string;
    label: string;
    objectKey: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
  status?: {
    id: string;
    name: string;
  };
};

export type Attribute = {
  workspaceId: string;
  globalId: string;
  id: string;
  objectTypeAttributeId: string;
  objectAttributeValues: AttributeValue[];
  objectId: string;
};
