import { zodValidation } from "../zod-validation/index.ts";
import "load-env";
import { AtlassianObjects, AtlassianSecret, atlassianSecretSchema, CreateObjectSchema, FetchObjectResponse, QueryObjectCreator } from "modules/atlassian-assets/types.ts";
import { <PERSON><PERSON><PERSON> } from "node:buffer";
import { Queries } from "modules/atlassian-assets/queries.ts";
import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { AtlassianObject } from "modules/atlassian-assets/types.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["JIRA_CREDENTIALS"]);

/**
 * Returns an Atlassian client.
 * @returns
 */
export const getAtlassianClient = (
  workspaceId: AtlassianAssets.WorkspaceIds,
) => {
  Logger.info("Fetching JIRA CREDENTIALS from environment variables");
  const secretEnv = Deno.env.get("JIRA_CREDENTIALS") as string;

  const secret = JSON.parse(secretEnv) as AtlassianSecret;

  zodValidation({
    objectToTest: secret,
    schema: atlassianSecretSchema,
    description: "Validating atlassian secret",
  });

  const { email, apiKey } = secret;
  Logger.info("Creating AtlassianClient instance");
  const atlassianClient = new AtlassianClient(email, apiKey, workspaceId);

  return atlassianClient;
};

/**
 * Wrapper class for Atlassian API.
 *
 * @export
 * @class AtlassianClient
 */
export class AtlassianClient {
  readonly email: string;
  public workspaceId: AtlassianAssets.WorkspaceIds;
  private readonly apiKey: string;
  private readonly creds: string;
  readonly baseUrl: string;
  params: RequestInit;

  /**
   * Constructor of JiraClientDeno.
   * @param client - Atlassian API client email
   * @param key - Atlassian API key
   * @param workspaceId - Atlassian workspaceId [sandbox or fxsolutions]
   */
  constructor(
    email: string,
    apiKey: string,
    workspaceId: AtlassianAssets.WorkspaceIds,
  ) {
    this.email = email;
    this.workspaceId = workspaceId;
    this.apiKey = apiKey;
    this.creds = Buffer.from(`${this.email}:${this.apiKey}`).toString(
      "base64",
    );
    this.baseUrl = `https://api.atlassian.com/jsm/assets/workspace/${workspaceId}/v1/`;
    this.params = {
      headers: {
        Authorization: `Basic ${this.creds}`,
        "Accept": "application/json",
      },
    };
  }

  /**
   * Generalization of the API call to the Atlassian platform.
   *
   * @template T - The type of the response
   * @param {string} relativeEndpoint - The URL path relative to the base URL
   * @param {string} method - The method to use for the call
   * @param {unknown} [payload] - The payload to send to the endpoint
   * @returns {Promise<T>} - The response from the API
   * @throws {Error} - Throws an error if the API call fails
   */
  private async makeCall<T>(
    relativeEndpoint: string,
    method: string,
    payload?: unknown,
  ): Promise<T | void> {
    const options: RequestInit = {
      method,
      headers: {
        ...this.params.headers,
        "Content-Type": "application/json",
      },
    };
    if (payload) {
      options.body = JSON.stringify(payload);
    }
    const url = this.baseUrl + relativeEndpoint;
    const response = await fetch(url, options);
    const responseText = await response.text();
    const statusCode = response.status;
    const isSuccessful = statusCode.toString().startsWith("2") ||
      statusCode.toString() === "304";
    if (!isSuccessful) {
      throw new Error(
        `Call to URL failed, status code: ${statusCode}, message: ${responseText}`,
      );
    }
    if (responseText !== "") {
      const responseParsed = JSON.parse(responseText) as T;
      return responseParsed;
    }
  }

  /**
   * Fetches all objects from Atlassian API.
   *
   * @param {string} [qlQuery] - The AQL query to fetch objects.
   * @param {number} [maxResults] - The maximum number of results to fetch.
   * @param {boolean} [includeAttributes] - Whether to include attributes in the response.
   * @returns {Promise<AtlassianObject[]>} - The fetched objects data.
   */
  public async fetchObjects(
    qlQuery: string = Queries.getAllEmployees(),
    maxResults: number = 1000,
    includeAttributes: boolean = true,
  ): Promise<AtlassianObject[]> {
    let allObjects: AtlassianObject[] = [];
    let startAt = 0;
    let isLast = false;

    while (!isLast) {
      const relativeEndpoint = `/object/aql?startAt=${startAt}&maxResults=${maxResults}&includeAttributes=${includeAttributes}`;
      const method = "POST";
      const payload = { qlQuery };

      const response = await this.makeCall(
        relativeEndpoint,
        method,
        payload,
      ) as FetchObjectResponse;

      allObjects = allObjects.concat(response.values);
      startAt += response.values.length;

      isLast = response.isLast;
    }
    return allObjects;
  }

  /**
   * Fetches an employee by id from Atlassian API.
   *
   * @param {string} objectId - The id of the object to fetch.
   * @returns {Promise<AtlassianEmployee>} - The fetched object data.
   * @throws {Error} - Throws an error if the deactivation fails.
   */
  public async fetchObjectById(objectId: string): Promise<AtlassianObject> {
    const relativeEndpoint = `object/${objectId}`;
    const method = "GET";
    return await this.makeCall(relativeEndpoint, method) as AtlassianObject;
  }

  /**
   * Fetches an employee by email from Atlassian API.
   *
   * @param {string} employeeEmail - The email of the employee to fetch.
   * @returns {Promise<AtlassianObject>} - The fetched employee data.
   * @throws {Error} - Throws an error if the deactivation fails.
   */
  public async fetchEmployeeByEmail(
    employeeEmail: string,
  ): Promise<AtlassianObject> {
    const relativeEndpoint = `object/aql`;
    const method = "POST";
    const qlQuery = Queries.getEmployeeByEmailQuery(employeeEmail);
    const payload = {
      qlQuery,
      maxResults: 1,
    };
    return await this.makeCall(
      relativeEndpoint,
      method,
      payload,
    ) as AtlassianObject;
  }

  /**
   * Updates an object by their ID.
   *
   * @param {string} objectId - The ID of the object to update.
   * @returns {Promise<AtlassianObject>} - The updated object data.
   * @throws {Error} - Throws an error if the deactivation fails.
   */
  public async updateObjectById(
    qlQuery: CreateObjectSchema,
    objectId: string,
  ): Promise<AtlassianObject> {
    const relativeEndpoint = `object/${objectId}`;
    const method = "PUT";
    const payload = qlQuery;
    return await this.makeCall(
      relativeEndpoint,
      method,
      payload,
    ) as AtlassianObject;
  }

  /**
   * Creates an object in Atlassian. It can be an employee, a site, job title or department.
   *
   * @param {CreateObjectSchema} qlQuery - The query with the employee data.
   * @returns {Promise<AtlassianObject>} - The created employee data.
   * @throws {Error} - Throws an error if the creation fails.
   */
  public async createObject(
    qlQuery: CreateObjectSchema,
  ): Promise<AtlassianObject> {
    const relativeEndpoint = `object/create`;
    const method = "POST";
    const payload = qlQuery;
    const createdObject = await this.makeCall(
      relativeEndpoint,
      method,
      payload,
    );
    return createdObject as AtlassianObject;
  }

  /**
   * Obtains the object ID or key for a given attribute name and value.
   * If the object does not exist, it creates the object using createObject,
   * except for objects of type "Employees" as they might not be created yet.
   *
   * @param {string} objectType - The name of the attribute.
   * @param {string} value - The value of the attribute.
   * @param {boolean} isProd - Indicates if the environment is production.
   * @returns {Promise<string | undefined>} - The object ID or key.
   * @throws {Error} - Throws an error if the fetch fails.
   */
  public async getObjectIdOrKey(
    objectType: AtlassianObjects,
    value: string,
    isProd: boolean,
  ): Promise<string | undefined> {
    const relativeEndpoint = `object/aql`;
    const method = "POST";
    const qlQuery = Queries.getObjectIdOrKey(objectType, value);
    const payload = {
      qlQuery,
      maxResults: 1,
    };
    let response = await this.makeCall(
      relativeEndpoint,
      method,
      payload,
    ) as FetchObjectResponse;

    if (!response.values || response.values.length === 0) {
      if (objectType === "Employees") {
        return undefined;
      }
      const createQuery = AtlassianAssets.queryObjectCreator[objectType];
      if (createQuery) {
        const query = createQuery(isProd, value);
        await this.createObject(query);

        response = await this.makeCall(
          relativeEndpoint,
          method,
          payload,
        ) as FetchObjectResponse;
        if (!response) {
          throw new Error(`Failed to fetch object ID or key for ${objectType}`);
        }
      }
    }

    const foundObject = response.values.find((obj) => obj.objectKey);
    if (!foundObject) {
      throw new Error(
        `No object with objectKey found for object type ${objectType} and value ${value}`,
      );
    }
    return foundObject.objectKey as string;
  }

  /**
   * Deletes an object in Atlassian.
   * When an employee leaves the company, the object "employee" should be put "Inactive".
   * Deleting an object is not recommended, as it will remove all the history of the object.
   * This method is only used for testing purposes.
   *
   * @param {string} objectId - The ID of the object to delete.
   * @returns {Promise<void>} - Resolves if the deletion is successful.
   * @throws {Error} - Throws an error if the deletion fails.
   */
  public async deleteObject(objectId: string): Promise<void> {
    const relativeEndpoint = `object/${objectId}`;
    const method = "DELETE";
    Logger.info(`Deleting object with id ${objectId}`);
    await this.makeCall(relativeEndpoint, method);
  }
}

export namespace AtlassianAssets {
  export const WORKSPACE_IDS = {
    FXSOLUTIONS: "b762c123-4a3a-4d11-a927-b3f894b11963",
    SANDBOX: "a54ede25-cd18-4a69-8bab-1d83ad52ed4e",
  } as const;

  /**
   * This function contains query creator functions for different object types.
   * Example usage:
   * const query = AtlassianAssets.queryCreator["Job Title"](isProd, "Software Engineer");
   */
  export const queryObjectCreator: QueryObjectCreator = {
    "Job Title": (isProd, value) => Queries.createJobTitleQuery(isProd, value),
    "Site": (isProd, value) => Queries.createSiteQuery(isProd, value),
    "Department": (isProd, value) => Queries.createDepartmentQuery(isProd, value),
  };

  type ValueOf<T> = T[keyof T];

  export type WorkspaceIds = ValueOf<typeof WORKSPACE_IDS>;
}
