# Atlassian Assets Module

This module provides a wrapper around the Atlassian Assets REST API, allowing you to interact with Atlassian Insight objects. The object types are defined in `AtlassianObjects type` [here](/src/packages/modules/atlassian-assets/types.ts). The module supports both production and sandbox environments, with different configurations for each.

### Prerequisites

- Deno
- Atlassian API credentials

### Environment Variables

You need to set the following environment variables in the packageJSON of the process:

- `JIRA_CREDENTIALS`: JSON containing your Atlassian API credentials.

### Usage

To interact with the Atlassian API, you need to create an instance of the `AtlassianClient` class. The [workspaceId](https://developer.atlassian.com/cloud/assets/assets-rest-api-guide/workflow/) determines whether you are working in the production or sandbox environment.

- To check the workspaceId for production: [Production Workspace](https://fxsolutions.atlassian.net/rest/servicedeskapi/assets/workspace)
- To check the workspaceId for sandbox: [Sandbox Workspace](https://fxsolutions-sandbox-294.atlassian.net/rest/servicedeskapi/assets/workspace)

### Goal

This module is designed to manipulate Atlassian Assets. Its goal is to dynamically create objects in Atlassian Assets with the purpose of being able to create all objects from scratch (employees, sites, job titles and departments) and establish the relationships amongst them. The logic is as follows:

- An object of type "employee" is created with the `createObject` method and its respective query [createEmployee](/src/packages/modules/atlassian-assets/queries.ts#L105).
- In that query, a fetch is performed to access the IDs of other object types (currently job title, department, and site) using the `getObjectIdOrKey` method. If the object does not exist, and therefore there is no ID, that object is created.
- The types of objects that can be created are defined in the `queryObjectCreatorSchema` zod [schema](/src/packages/modules/atlassian-assets/types.ts), so that if more types of objects are introduced, it is easy to modify.

### Configuration

The module uses different configurations for production and sandbox environments. Unfortunately the objectTypeIds and attributeIds of the objects (employee, site, department and job title) are different in prod and sandbox environtment. The appropriate configuration is selected based on the isProd boolean passed to the query functions and it is defined in the [constants.ts](/src/packages/modules/atlassian-assets/constants.ts)

### Testing

Comprehensive tests have been implemented to validate the functionality of the `fetchObjects` method. These include:

- **Unit Tests**: Focused on verifying the recursive fetching logic using mock data to simulate API responses.
- **Integration Tests**: Ensure the method interacts correctly with the real Atlassian API, validating its behavior in a live environment.

To execute the tests, use the following command:

```
deno test src/packages/modules/atlassian-assets/tests/fetchObjects.test.ts
```

### Queries

The `Queries` class provides various methods to generate queries for fetching and manipulating data in Atlassian Insight. By encapsulating the query generation logic within a class, we achieve better organization and modularity. This approach allows for easier maintenance and scalability, as all query-related methods are centralized in one place.

#### Query Structure

For simple queries, the Queries class returns a string. However, for more complex queries, it returns an object that is later stringified in the call. This allows for more flexibility and the ability to both include multiple attributes and values and type the structure of the query.

#### Query Params

The isProd parameter is used to determine whether the query should use production or sandbox IDs. This ensures that the correct objectTypeIds and attributeIds are used based on the environment.

### Conclusion

This module provides a comprehensive wrapper around the Atlassian Assets REST API, making it easy to interact with various objects in Atlassian Insight. By following the examples and guidelines provided in this README, you should be able to effectively use the module in your projects. For more information, refer to the [Atlassian Assets REST API Guide](https://developer.atlassian.com/cloud/assets/rest/api-group-aql/#api-group-aql).
