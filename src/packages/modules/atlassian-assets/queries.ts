import { Hibob } from "modules/hibob/types.ts";
import { AtlassianClient } from "modules/atlassian-assets/index.ts";
import { CreateObjectSchema } from "modules/atlassian-assets/types.ts";
import { prodConfig, sandboxConfig } from "modules/atlassian-assets/constants.ts";
import { JiraClient } from "modules/jira/index.ts";
import { Logger } from "modules/logger/index.ts";
export class Queries {
  /**
   * Generates a query to fetch active employees whose emails are not in Hibob.
   *
   * @param {string[]} emailList - The list of emails to exclude.
   * @returns {string} - The generated query.
   */
  static getActiveEmployeesToDeactivate(emailList: string): string {
    return `objectType = Employees AND Email NOT IN (${emailList}) AND Status = "Active"`;
  }

  /**
   * Generates a query to fetch employees whose emails are in the provided list and are inactive.
   *
   * @param {string[]} emailList - The list of emails of HiBob to include.
   * @returns {string} - The generated query.
   */
  static getInactiveEmployeesInEmailList(emailList: string): string {
    return `objectType = Employees AND Email IN (${emailList}) AND Status = "Inactive"`;
  }

  /**
   * Generates a query to fetch all employees.
   *
   * @returns {string} - The generated query.
   */
  static getAllEmployees(): string {
    return `objectType = Employees`;
  }

  /**
   * Generates a query to fetch an object ID or key based on the object type and value.
   *
   * @param {string} objectType - The name of the attribute.
   * @param {string} value - The value of the attribute.
   * @returns {string} - The generated query.
   */
  static getObjectIdOrKey(objectType: string, value: string): string {
    return `objectType = \"${objectType}\" AND Name = \"${value}\"`;
  }

  /**
   * Generates a query to deactivate an employee by ID.
   *
   * In production, the query sets the attribute with ID "55" to "Inactive".
   * In sandbox, the query sets the attribute with ID "152" to "Closed".
   *
   * @param {boolean} isProd - The environment (prod or sandbox).
   * @returns {string} - The generated query.
   */
  static updateEmployeeStatus(isProd: boolean, status: string = "Inactive"): CreateObjectSchema {
    const { objectTypeIds: { employee: objectTypeId }, attributeIds: { employeeStatus: objectTypeAttributeId } } = isProd ? prodConfig : sandboxConfig;

    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId,
          objectAttributeValues: [
            {
              value: status,
            },
          ],
        },
      ],
    };
  }

  /**
   * Generates a query to fetch an employee by email.
   *
   * @param {string} employeeEmail - The email of the employee.
   * @returns {string} - The generated query.
   */
  static getEmployeeByEmailQuery(employeeEmail: string): string {
    return `objectType = Employees AND Email = "${employeeEmail}"`;
  }

  /**
   * Creates a query object for creating an employee in Atlassian Insight.
   *
   * In Atlassian Insight, certain attributes may require an object ID or a key instead of a text value.
   * In Sandbox and production, those objectTypeAttributeId are different so we need to specify them.
   *
   * Solution:
   * - : We need to make additional requests to obtain the correct IDs for certain attributes before creating the employee.
   *
   * @param {Hibob.Types.Employee[]} hiBobEmployees - The HiBob employees to be used for creating the account.
   * @param {Hibob.Types.Employee} employee - The HiBob employee data.
   * @param {AtlassianClient} atlassianClient - The Atlassian client instance.
   * @param {boolean} isProd - Indicates if the environment is production.
   * @returns {Promise<CreateEmployeeQuery>} - The query object for creating the employee.
   * @throws {Error} - Throws an error if the creation of the query fails.
   */
  static async createEmployee(
    hiBobEmployees: Hibob.Types.Employee[],
    employee: Hibob.Types.Employee,
    atlassianClient: AtlassianClient,
    jiraClient: JiraClient,
    isProd: boolean,
  ): Promise<CreateObjectSchema> {
    const { attributeIds, objectTypeIds } = isProd ? prodConfig : sandboxConfig;
    const objectTypeId = objectTypeIds.employee;

    const attributes = [
      {
        objectTypeAttributeId: attributeIds.employeeName,
        objectAttributeValues: [
          {
            value: `${employee.displayName}`,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeJobTitle,
        objectAttributeValues: [
          {
            value: await atlassianClient.getObjectIdOrKey(
              "Job Title",
              `${employee.work?.title}`,
              isProd,
            ) as string,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeEmail,
        objectAttributeValues: [
          {
            value: `${employee.email}`,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeCountry,
        objectAttributeValues: [
          {
            value: `${employee.work?.customColumns?.column_1619179960366}`,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeDepartment,
        objectAttributeValues: [
          {
            value: await atlassianClient.getObjectIdOrKey(
              "Department",
              `${employee.work?.department}`,
              isProd,
            ) as string,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeIsManager,
        objectAttributeValues: [
          {
            value: `${employee.work?.isManager}`,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeSite,
        objectAttributeValues: [
          {
            value: await atlassianClient.getObjectIdOrKey(
              "Site",
              `${employee.work?.site}`,
              isProd,
            ) as string,
          },
        ],
      },
      {
        objectTypeAttributeId: attributeIds.employeeStatus,
        objectAttributeValues: [
          {
            value: "Active",
          },
        ],
      },
    ];

    /*
     * Manager and GPP are included only if the values are not empty
     * because it retrieves an employee that has maybe not been created yet.
     *
     * If manager is present, we also retrieve the atlassian ID of the manager.
     */
    const managerId = await atlassianClient.getObjectIdOrKey(
      "Employees",
      `${employee.work?.manager}`,
      isProd,
    );
    if (managerId) {
      attributes.push({
        objectTypeAttributeId: attributeIds.employeeManager,
        objectAttributeValues: [
          {
            value: managerId,
          },
        ],
      });
      const manager = hiBobEmployees.find((emp) => emp.displayName === employee.work?.manager);
      const managerEmail = manager?.email;
      const managerAtlassianId = await jiraClient.getUserId(managerEmail!);
      if (managerAtlassianId) {
        attributes.push({
          objectTypeAttributeId: attributeIds.employeeManagerGoogleUser,
          objectAttributeValues: [
            {
              value: managerAtlassianId,
            },
          ],
        });
      }
    }

    const gppId = await atlassianClient.getObjectIdOrKey(
      "Employees",
      `${employee.work?.customColumns?.column_1718378012762}`,
      isProd,
    );
    if (gppId) {
      attributes.push({
        objectTypeAttributeId: attributeIds.employeeGpp,
        objectAttributeValues: [
          {
            value: gppId,
          },
        ],
      });
    }

    /*
     * The atlassian account should be created already.
     * However, to avoid the solution crashing, if the atlasian account is not created,
     * we will not add the google user id. It will be updated when updating user.
     * We will log the error and continue to be notified.
     */
    try {
      const googleUserId = await jiraClient.getUserId(employee.email!);
      if (googleUserId) {
        attributes.push({
          objectTypeAttributeId: attributeIds.employeeGoogleUser,
          objectAttributeValues: [
            {
              value: googleUserId,
            },
          ],
        });
      }
    } catch (error) {
      Logger.error(
        `Error fetching Google id for ${employee.email} - ${error}`,
        `Error fetching Google id for ${employee.email} - ${error}`,
      );
    }

    return {
      objectTypeId,
      attributes,
    };
  }

  /**
   * Generates a query to create a job title in Atlassian Insight.
   * @param {boolean} isProd - Indicates if the environment is production.
   * @param {string} jobTitleName - The name of the job title.
   * @returns {string} - The generated query.
   */
  static createJobTitleQuery(isProd: boolean, jobTitleName: string): CreateObjectSchema {
    const { attributeIds, objectTypeIds: { jobTitle: objectTypeId } } = isProd ? prodConfig : sandboxConfig;

    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId: attributeIds.jobTitleName,
          objectAttributeValues: [
            {
              value: jobTitleName,
            },
          ],
        },
      ],
    };
  }

  /**
   * Generates a query to create a site in Atlassian Insight.
   *
   * @param {boolean} isProd - Indicates if the environment is production.
   * @param {string} siteName - The name of the site.
   * @returns {string} - The generated query.
   */
  static createSiteQuery(isProd: boolean, siteName: string): CreateObjectSchema {
    const { attributeIds: { siteName: objectTypeAttributeId }, objectTypeIds: { site: objectTypeId } } = isProd ? prodConfig : sandboxConfig;

    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId,
          objectAttributeValues: [
            {
              value: siteName,
            },
          ],
        },
      ],
    };
  }

  /**
   * Generates a query to create a department in Atlassian Insight.
   *
   * @param {boolean} isProd - Indicates if the environment is production.
   * @param {string} departmentName - The name of the department.
   * @returns {string} - The generated query.
   */
  static createDepartmentQuery(isProd: boolean, departmentName: string): CreateObjectSchema {
    const { attributeIds, objectTypeIds } = isProd ? prodConfig : sandboxConfig;
    const objectTypeId = objectTypeIds.department;

    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId: attributeIds.departmentName,
          objectAttributeValues: [
            {
              value: departmentName,
            },
          ],
        },
        {
          objectTypeAttributeId: attributeIds.departmentStatus,
          objectAttributeValues: [
            {
              value: "Active",
            },
          ],
        },
      ],
    };
  }

  /**
   * Generates a query to fetch all departments.
   *
   * @returns {string} - The generated query.
   */
  static getAllDepartments(): string {
    return `objectType = Department`;
  }

  /**
   * Generates a query to fetch all job titles.
   *
   * @returns {string} - The generated query.
   */
  static getAllJobTitles(): string {
    return `objectType = "Job Title"`;
  }

  /**
   * Generates a query to fetch active employees in the department.
   *
   * @param {string[]} department - The name of the department.
   * @returns {string} - The generated query.
   */
  static getActiveEmployeesFromDepartment(department: string): string {
    return `objectType = Employees AND Department IN ("${department}") AND Status = "Active"`;
  }

  /**
   * Generates a query to fetch active employees who have that job title.
   *
   * @param {string[]} jobTitle - The name of the job title.
   * @returns {string} - The generated query.
   */
  static getActiveEmployeesFromJobTitle(jobTitle: string): string {
    return `objectType = Employees AND Role IN ("${jobTitle}") AND Status = "Active"`;
  }

  /**
   * Generates a query to update a job title to introduce the relationship with multiple departments in Atlassian Insight.
   * This function creates an object for each department in the provided array.
   *
   * @param {boolean} isProd - Indicates if the environment is production.
   * @param {string[]} departmentIdFromJobTitle - The ids of the departments associated with the job title.
   * @returns {CreateObjectSchema} - The generated query.
   */
  static updateJobTitleQuery(isProd: boolean, departmentIdFromJobTitle: string[]): CreateObjectSchema {
    const { attributeIds, objectTypeIds: { jobTitle: objectTypeId } } = isProd ? prodConfig : sandboxConfig;
    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId: attributeIds.jobTitleDepartment,
          objectAttributeValues: departmentIdFromJobTitle.map((id) => ({ value: id })),
        },
      ],
    };
  }

  /**
   * Generates a query to update the status of a department to "Inactive" in Atlassian Insight.
   * @param {boolean} isProd - Indicates if the environment is production.
   * @returns {CreateObjectSchema} - The generated query to update the department status.
   */
  static updateDepartmentStatus(isProd: boolean, status: string = "Inactive"): CreateObjectSchema {
    const { attributeIds, objectTypeIds: { department: objectTypeId } } = isProd ? prodConfig : sandboxConfig;

    return {
      objectTypeId,
      attributes: [
        {
          objectTypeAttributeId: attributeIds.departmentStatus,
          objectAttributeValues: [
            {
              value: status,
            },
          ],
        },
      ],
    };
  }
}
