import { FetchObjectResponse } from "../types.ts";

export const mockData: FetchObjectResponse[] = [
  {
    startAt: 0,
    maxResults: 2,
    total: 4,
    values: [
      {
        workspaceId: "workspace-1",
        globalId: "global-1",
        id: "1",
        label: "Object 1",
        objectKey: "key-1",
        avatar: null,
        objectType: {
          id: "type-1",
          name: "Type 1",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T10:00:00Z",
        updated: "2025-05-02T10:00:00Z",
        hasAvatar: false,
        timestamp: 1620000000,
        _links: { self: "https://example.com/object/1" },
        name: "Object 1",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-1",
            id: "attr-1",
            objectTypeAttributeId: "attr-type-1",
            objectAttributeValues: [
              {
                value: "Value 1",
                searchValue: "Search Value 1",
                referencedType: false,
                displayValue: "Display Value 1",
              },
            ],
            objectId: "1",
          },
        ],
      },
      {
        workspaceId: "workspace-1",
        globalId: "global-2",
        id: "2",
        label: "Object 2",
        objectKey: "key-2",
        avatar: null,
        objectType: {
          id: "type-2",
          name: "Type 2",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T11:00:00Z",
        updated: "2025-05-02T11:00:00Z",
        hasAvatar: false,
        timestamp: 1620000001,
        _links: { self: "https://example.com/object/2" },
        name: "Object 2",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-2",
            id: "attr-2",
            objectTypeAttributeId: "attr-type-2",
            objectAttributeValues: [
              {
                value: "Value 2",
                searchValue: "Search Value 2",
                referencedType: false,
                displayValue: "Display Value 2",
              },
            ],
            objectId: "2",
          },
        ],
      },
    ],
    objectTypeAttributes: [],
    hasMoreResults: true,
    last: false,
    isLast: false,
  },
  {
    startAt: 0,
    maxResults: 2,
    total: 4,
    values: [
      {
        workspaceId: "workspace-1",
        globalId: "global-1",
        id: "1",
        label: "Object 1",
        objectKey: "key-1",
        avatar: null,
        objectType: {
          id: "type-1",
          name: "Type 1",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T10:00:00Z",
        updated: "2025-05-02T10:00:00Z",
        hasAvatar: false,
        timestamp: 1620000000,
        _links: { self: "https://example.com/object/1" },
        name: "Object 1",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-1",
            id: "attr-1",
            objectTypeAttributeId: "attr-type-1",
            objectAttributeValues: [
              {
                value: "Value 1",
                searchValue: "Search Value 1",
                referencedType: false,
                displayValue: "Display Value 1",
              },
            ],
            objectId: "1",
          },
        ],
      },
      {
        workspaceId: "workspace-1",
        globalId: "global-2",
        id: "2",
        label: "Object 2",
        objectKey: "key-2",
        avatar: null,
        objectType: {
          id: "type-2",
          name: "Type 2",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T11:00:00Z",
        updated: "2025-05-02T11:00:00Z",
        hasAvatar: false,
        timestamp: 1620000001,
        _links: { self: "https://example.com/object/2" },
        name: "Object 2",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-2",
            id: "attr-2",
            objectTypeAttributeId: "attr-type-2",
            objectAttributeValues: [
              {
                value: "Value 2",
                searchValue: "Search Value 2",
                referencedType: false,
                displayValue: "Display Value 2",
              },
            ],
            objectId: "2",
          },
        ],
      },
    ],
    objectTypeAttributes: [],
    hasMoreResults: true,
    last: false,
    isLast: false,
  },
  {
    startAt: 0,
    maxResults: 2,
    total: 4,
    values: [
      {
        workspaceId: "workspace-1",
        globalId: "global-1",
        id: "1",
        label: "Object 1",
        objectKey: "key-1",
        avatar: null,
        objectType: {
          id: "type-1",
          name: "Type 1",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T10:00:00Z",
        updated: "2025-05-02T10:00:00Z",
        hasAvatar: false,
        timestamp: 1620000000,
        _links: { self: "https://example.com/object/1" },
        name: "Object 1",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-1",
            id: "attr-1",
            objectTypeAttributeId: "attr-type-1",
            objectAttributeValues: [
              {
                value: "Value 1",
                searchValue: "Search Value 1",
                referencedType: false,
                displayValue: "Display Value 1",
              },
            ],
            objectId: "1",
          },
        ],
      },
      {
        workspaceId: "workspace-1",
        globalId: "global-2",
        id: "2",
        label: "Object 2",
        objectKey: "key-2",
        avatar: null,
        objectType: {
          id: "type-2",
          name: "Type 2",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T11:00:00Z",
        updated: "2025-05-02T11:00:00Z",
        hasAvatar: false,
        timestamp: 1620000001,
        _links: { self: "https://example.com/object/2" },
        name: "Object 2",
        attributes: [
          {
            workspaceId: "workspace-1",
            globalId: "attr-global-2",
            id: "attr-2",
            objectTypeAttributeId: "attr-type-2",
            objectAttributeValues: [
              {
                value: "Value 2",
                searchValue: "Search Value 2",
                referencedType: false,
                displayValue: "Display Value 2",
              },
            ],
            objectId: "2",
          },
        ],
      },
    ],
    objectTypeAttributes: [],
    hasMoreResults: true,
    last: false,
    isLast: false,
  },
  {
    startAt: 2,
    maxResults: 2,
    total: 4,
    values: [
      {
        workspaceId: "workspace-1",
        globalId: "global-3",
        id: "3",
        label: "Object 3",
        objectKey: "key-3",
        avatar: null,
        objectType: {
          id: "type-3",
          name: "Type 3",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T12:00:00Z",
        updated: "2025-05-02T12:00:00Z",
        hasAvatar: false,
        timestamp: 1620000002,
        _links: { self: "https://example.com/object/3" },
        name: "Object 3",
        attributes: [],
      },
      {
        workspaceId: "workspace-1",
        globalId: "global-4",
        id: "4",
        label: "Object 4",
        objectKey: "key-4",
        avatar: null,
        objectType: {
          id: "type-4",
          name: "Type 4",
          workspaceId: "",
          globalId: "",
          type: 0,
          description: "",
          icon: {
            id: "",
            name: "",
            url16: "",
            url48: "",
          },
          position: 0,
          created: "",
          updated: "",
          objectCount: 0,
          objectSchemaId: "",
          inherited: false,
          abstractObjectType: false,
          parentObjectTypeInherited: false,
        },
        created: "2025-05-01T13:00:00Z",
        updated: "2025-05-02T13:00:00Z",
        hasAvatar: false,
        timestamp: 1620000003,
        _links: { self: "https://example.com/object/4" },
        name: "Object 4",
        attributes: [],
      },
    ],
    objectTypeAttributes: [],
    hasMoreResults: false,
    last: true,
    isLast: true,
  },
];
