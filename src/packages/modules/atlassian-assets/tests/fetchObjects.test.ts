import { assertEquals } from "@std/assert/equals";
import { AtlassianAssets, AtlassianClient } from "../index.ts";
import { FetchObjectResponse } from "../types.ts";
import { mockData } from "modules/atlassian-assets/tests/mockData.ts";
const { getAtlassianClient } = await import("../index.ts");

function createMockedAtlassianClient(mockMakeCall: <T>(relativeEndpoint: string, method: string, payload?: unknown) => Promise<void | T>) {
  const client = new AtlassianClient("<EMAIL>", "apiKey", AtlassianAssets.WORKSPACE_IDS.SANDBOX);
  client["makeCall"] = mockMakeCall;
  return client;
}

Deno.test("fetchObjects should fetch all objects with maxResults = 2 and refetch until completion", async () => {
  const mockMakeCall = async (relativeEndpoint: string, method: string, payload: unknown): Promise<FetchObjectResponse> => {
    const startAt = Number(new URLSearchParams(relativeEndpoint.split("?")[1]).get("startAt"));
    // Dividing `startAt` by 2 directly gives the correct index in `mockData` because maxResults = 2
    return mockData[startAt / 2];
  };

  const client = createMockedAtlassianClient(mockMakeCall as <T>(relativeEndpoint: string, method: string, payload?: unknown) => Promise<void | T>);

  // Call fetchObjects with maxResults = 2
  const result = await client.fetchObjects("mockQuery", 2, true);

  const expected = mockData.flatMap((response) => response.values);
  assertEquals(result, expected);
});

Deno.test("fetchObjects should fetch all objects with maxResults = 1 and refetch until completion", async () => {
  const mockMakeCall = async (relativeEndpoint: string, method: string, payload: unknown): Promise<FetchObjectResponse> => {
    const startAt = Number(new URLSearchParams(relativeEndpoint.split("?")[1]).get("startAt"));
    // Since `mockData` is structured for pages of size 2, but maxResults = 1,
    // we need to adjust the index accordingly.We use `Math.floor(startAt / 2)`
    return mockData[Math.floor(startAt / 2)];
  };

  const client = createMockedAtlassianClient(mockMakeCall as <T>(relativeEndpoint: string, method: string, payload?: unknown) => Promise<void | T>);

  // Call fetchObjects with maxResults = 1
  const result = await client.fetchObjects("mockQuery", 1, true);

  const expected = mockData.flatMap((response) => response.values);
  assertEquals(result, expected);
});

Deno.test("fetchObjects should terminate and not enter an infinite loop with real API calls", async () => {
  const atlassianClient = await getAtlassianClient(AtlassianAssets.WORKSPACE_IDS.SANDBOX);
  const employeesFetched = await atlassianClient.fetchObjects();
  console.log(`Fetched ${employeesFetched.length} employees`);
  // Assert that the function terminates and returns a result
  assertEquals(Array.isArray(employeesFetched), true);
  assertEquals(employeesFetched.length > 0, true);
});
