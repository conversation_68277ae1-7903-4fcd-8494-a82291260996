import { checkEnvVars } from "modules/check-env-vars/index.ts";
import { zodValidation } from "modules/zod-validation/index.ts";
import { AuthData, authDataSchema } from "modules/dalo-radius/types.ts";
import { GetCommandInvocationCommand, SendCommandCommand, SSMClient } from "@aws-sdk/client-ssm";
import { generatePassword } from "modules/generate-password/index.ts";
import { commandParams, STATUS_FAILED, STATUS_INPROGRESS, STATUS_PENDING } from "modules/dalo-radius/constants.ts";
import { Logger } from "modules/logger/index.ts";

checkEnvVars(["DALORADIUS_INSTANCE_DATA"]);

let daloRadiusClient: DaloRadius | null;

export const getDaloRadiusClient = () => {
  if (daloRadiusClient) return daloRadiusClient;

  const authData = Deno.env.get("DALORADIUS_INSTANCE_DATA");
  if (!authData) {
    throw new Error("DALORADIUS_INSTANCE_DATA is not set in the env vars");
  }
  const authDataJson = JSON.parse(authData) as AuthData;

  zodValidation({
    objectToTest: authDataJson,
    schema: authDataSchema,
  });

  const {
    keyId,
    accessKey,
    region,
  } = authDataJson;

  const config = {
    credentials: {
      accessKeyId: keyId,
      secretAccessKey: accessKey,
    },
    region,
  };
  const ssmClient = new SSMClient(config);
  daloRadiusClient = new DaloRadius(ssmClient, authDataJson);
  return daloRadiusClient;
};

export class DaloRadius {
  ssmClient: SSMClient;
  authData: AuthData;
  cookie: string | undefined;
  constructor(ssmClient: SSMClient, authData: AuthData) {
    this.ssmClient = ssmClient;
    this.authData = authData;
  }

  async SendCommandToEC2(command: string) {
    Logger.info("sending command");
    const params = {
      InstanceIds: [this.authData.instanceId],
      DocumentName: commandParams.DocumentName,
      Parameters: {
        commands: [command],
      },
      HttpRedirectCode: commandParams.HttpRedirectCode,
    };
    const sendCommandCommand = new SendCommandCommand(params);
    const commandResponse = await this.ssmClient.send(sendCommandCommand);
    const commandId = commandResponse.Command?.CommandId;
    const getCommandInvocationParams = {
      CommandId: commandId,
      InstanceId: this.authData.instanceId,
      Error: true,
    };
    // timesout is set here because calling GetCommandInvocationCommand immediately after SendCommandCommand results in a Invocation does not exist error
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const getCommandInvocationCommand = new GetCommandInvocationCommand(
      getCommandInvocationParams,
    );
    let status = STATUS_PENDING;
    let getCommandInvocationResult;
    while (status === STATUS_PENDING || status === STATUS_INPROGRESS) {
      getCommandInvocationResult = await this.ssmClient.send(getCommandInvocationCommand);
      status = getCommandInvocationResult.Status as string;
      Logger.info(`Command Status: ${status}`);
      // timeout here so that if the status is pending, we dont overload the instance with requests
      await new Promise((resolve) => setTimeout(resolve, 5000));
    }
    if (status === STATUS_FAILED) {
      const errorMessage = "Comman executed on instance failed";
      Logger.error(errorMessage);
      throw new Error(errorMessage);
    }
    const commandResult = getCommandInvocationResult?.StandardOutputContent;

    if (!commandResult) {
      throw new Error("Command result not found");
    }

    return commandResult;
  }

  async getCookie() {
    Logger.info("retrieving cookie to use in other calls");
    if (this.cookie) {
      return this.cookie;
    }
    const { username, password, baseUrl, ip, instanceId } = this.authData;
    const url = `http://${baseUrl}/daloradius/dologin.php`;
    const data = `operator_user=${username}&operator_pass=${password}&location=default`;
    const contentType = "application/x-www-form-urlencoded";
    const command = `curl -v -L -i -c "" -d "${data}" -H "Content-Type: ${contentType}" -X POST "${url}" --resolve ${baseUrl}:${ip}`;
    const commandResult = await this.SendCommandToEC2(command);
    const regex = /Set-Cookie: PHPSESSID=([^;]+)/i;
    const matches = regex.exec(commandResult);
    const sessionId = matches ? matches[1] : null;
    if (!sessionId) {
      throw new Error("Could not obtain cookie");
    }
    Logger.info("cookie retrieved");
    this.cookie = `PHPSESSID=${sessionId}`;
    return this.cookie;
  }

  async createUser(email: string) {
    Logger.info(`---------------------------`);
    Logger.info(`creating wifi username and password for ${email}`);
    const authType = "userAuth";
    const employeePassword = generatePassword(8);
    const passwordType = "Cleartext-password";
    const submit = "Apply";
    const data = `authType=${authType}&username=${email}&password=${employeePassword}&passwordType=${passwordType}&submit=${submit}`;
    const { baseUrl, ip } = this.authData;
    const cookie = await this.getCookie();
    const url = `http://${baseUrl}/daloradius/mng-new.php`;
    const command = `curl -i -d "${data}" -H "Content-Type: application/x-www-form-urlencoded" -H "Cookie: ${cookie}" -X POST "${url}" --resolve ${baseUrl}:${ip}`;
    const commandResult = await this.SendCommandToEC2(command);
    const regex = /Added to database new user/i;
    const isSuccessfull = regex.test(commandResult);
    if (!isSuccessfull) {
      throw new Error(
        `Could not create wifi credentials for ${email}\ncommandResult: ${commandResult}`,
      );
    }
    Logger.info("wifi credentials created");
    return employeePassword;
  }

  async getWifiPassword(employeeEmail: string) {
    Logger.info(`---------------------------`);
    Logger.info(`Getting wifi password for ${employeeEmail}`);
    const { baseUrl, ip } = this.authData;
    const url = `http://${baseUrl}/daloradius/mng-edit.php?username=${employeeEmail}`;
    const cookie = await this.getCookie();
    const command = `curl -i -H "Cookie: ${cookie}" "${url}" --resolve ${baseUrl}:${ip}`;
    const commandResult = await this.SendCommandToEC2(command);
    const regex = /id='password' value='([^']+)/;
    const match = commandResult.match(regex);
    if (!match) {
      Logger.info("could not find password");
      return undefined;
    }
    Logger.info("password found");
    const password = match[1];
    return password;
  }
  async deleteUser(email: string) {
    Logger.info(`---------------------------`);
    Logger.info(`deleting wifi username and password for ${email}`);
    const { baseUrl, ip } = this.authData;
    const url = `http://${baseUrl}/daloradius/mng-del.php?username%5B%5D=${email}&delradacct=yes&submit=Apply`;
    const cookie = await this.getCookie();
    const command = `curl -i -H "Cookie: ${cookie}" "${url}" --resolve ${baseUrl}:${ip}`;
    await this.SendCommandToEC2(command);
    Logger.info("wifi credentials deleted");
    return true;
  }
}
