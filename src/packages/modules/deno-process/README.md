# denoProcess

This folder contains the code for the Deno process module.

## Usage

To use this module, you need to import the namespace `DenoProcess`.

Example:

```ts
import { DenoProcess } from "modules/deno-process/index.ts";
import packageJson from "./package.json" with { type: "json" };

DenoProcess.serve({
  packageJson,
  port: 8080, // note: port defaults to 8080, should be changed only in case of conflicts when serving locally
  handler: async ({ name: string }) => {
    return { expected: true };
  },
  bodySchema: z.object({
    name: z.string(),
  }),
  responseDataSchema: z.object({
    expected: z.boolean(),
  }),
});
```

For the **injectEnvVars** function, usage as below:

```ts
import packageJson from "../../package.json" with { type: "json" };

await injectEnvVars(packageJson);

const { yourFunctionToTest } = await import("../yourFunctionToTest.ts");

/**
 * All your testing code
 */
```

## Testing

Some test cases are provided in the [test.ts](./test.ts) file.

To run them

```
deno test src/packages/modules/deno-process/tests.ts
```
