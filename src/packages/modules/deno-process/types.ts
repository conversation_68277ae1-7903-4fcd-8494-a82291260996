import { StatusCodes } from "modules/error-with-status-code/index.ts";
import { z } from "zod";
export namespace DenoProcessTypes {
  export type OptionalSchema = z.ZodSchema | undefined;
  export type InferredType<T extends OptionalSchema> = T extends z.ZodSchema ? z.infer<T> : undefined;
  export type SuccessfulResult<T extends OptionalSchema> = {
    success: true;
    data: InferredType<T>;
  };
  export type FailureResult = {
    success: false;
    error: string;
  };
  export type Result<T extends OptionalSchema> = SuccessfulResult<T> | FailureResult;
  export interface WrappedResponse<T extends OptionalSchema> extends Response {
    status: StatusCodes;
    json: () => Promise<Result<T>>;
  }
  export const ERRORS_CONFIG = {
    noBody: {
      prefix: "No body provided",
      code: 400,
    },
    failedBodyParse: {
      prefix: "Failed to parse body:",
      code: 400,
    },
    failedBodyValidation: {
      prefix: "Body validation failed",
      code: 400,
    },
    failedResponseValidation: {
      prefix: "Response validation failed",
      code: 500,
    },
  } as const;
}
