import { getEnvVarsAndSecrets } from "utilities/get-env-vars-and-secrets.ts";
import { ProcessSchema } from "types/process-schema.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * This function is used to inject environment variables and secrets from the environment stage into the current process.
 * It is used in the tests to inject the environment variables and secrets before running the tests.
 * It assumes the folder structure as shown below. However, if the path is provided, it will use that path instead.
 *
 * Folder structure:
 * - src
 *  - packages
 *   - processes
 *    - [process-name]
 *      - src
 *        - tests
 *         - [test-file].ts
 * @param {string} [path] - The path to the root folder of the process. If not provided, it will use the default path.
 * @returns {Promise<void>}
 */
export const injectEnvVars = async (packageJson: ProcessSchema.Base) => {
  Logger.info("Injecting env vars");
  const pathToUse = `../../processes/${packageJson.name}`;
  const absolutePath = new URL(pathToUse, import.meta.url).pathname;
  Logger.info({ absolutePath });
  const env = await getEnvVarsAndSecrets({
    processFolderAbsolutePath: absolutePath,
    stageName: "staging",
  });
  Object.keys(env).forEach((key) => {
    Logger.info(key);
    Deno.env.set(key, env[key]);
  });
};
