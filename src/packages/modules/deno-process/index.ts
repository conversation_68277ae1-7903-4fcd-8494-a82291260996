import { DenoProcessTypes } from "modules/deno-process/types.ts";
import { _bodyParser } from "modules/deno-process/methods/_bodyParser.ts";
import { _ServeError } from "modules/deno-process/methods/_ServeError.ts";
import { _serve } from "modules/deno-process/methods/_serve.ts";

/**
 * This namespace contains functions that are used to create a Deno server to standardise endpoint development and avoid repetitive code.
 */
export namespace DenoProcess {
  /**
   * This function will parse the body of a request and will validate it with a Zod schema.
   * If the validation fails, it will throw an error.
   * If no schema is provided, the parsing will be skipped entirely.
   * @param {Request} request The request object
   * @param {ZodSchema} [schema] The Zod schema to validate the body. If not provided, the parsing will be skipped entirely.
   * @returns {T | undefined} The parsed body, or undefined if no schema was provided.
   */
  export const bodyParser = _bodyParser;
  /**
   * A utility class to handle errors in a Deno server, providing common practices for error handling, logging and status code reporting.
   */
  export class ServeError extends _ServeError {}

  /**
   * This function will start a Deno server with a handler that:
   * - Parses the body of a request using the provided schema (if provided)
   * - Calls the provided handler with the parsed body
   * - Returns a response with the format of {@link DenoProcessTypes.WrappedResponse}
   * - Catches any errors and returns a response with the format of {@link DenoProcessTypes.WrappedResponse}
   * - Notifies the error to the error-notifier service if the error is not a {@link ServeError}
   * @param {Object} options - The options object
   * @param {Function} options.handler - The handler to be called with the parsed body
   * @param {ProcessSchema.Base} options.packageJson - The package.json of the process
   * @param {ZodSchema} options.bodySchema - The schema to validate the body. This must be provided if the handler expects a body
   * @param {ZodSchema} options.responseDataSchema - The schema to validate the response data. If not provided, the response data will not be validated
   * @param {number} options.port - The port to listen on
   * @returns {Server} - The Deno server
   */
  export const serve = _serve;
}
