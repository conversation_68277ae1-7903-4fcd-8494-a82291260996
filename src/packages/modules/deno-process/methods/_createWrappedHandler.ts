import { DenoProcessTypes } from "modules/deno-process/types.ts";
import ErrorNotifier from "modules/error-notifier-gcr/index.ts";
import { zodValidation } from "modules/zod-validation/index.ts";
import { DenoProcess } from "modules/deno-process/index.ts";
import { ProcessSchema } from "types/process-schema.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * This environment variable should not be set in production, it's here only for test purposes, that's also the reason why it's prefixed with `_` and has a name so convoluted.
 */
export const shouldSkipNotifyErrorEnvVar = "_TEST_DENO_WRAPPER_SHOULD_SKIP_NOTIFY_ERROR";
const shouldSkipNotifyErrorEnv = Deno.env.get(shouldSkipNotifyErrorEnvVar) === "true";
if (shouldSkipNotifyErrorEnv) {
  Logger.warning(`The environment variable '${shouldSkipNotifyErrorEnvVar}' is set to 'true'!\nThis is meant only when you're testing the Deno wrapper!\n`);
  prompt(`Continue?`);
}

export const _createWrappedHandler = <T extends DenoProcessTypes.OptionalSchema, R extends DenoProcessTypes.OptionalSchema>(options: {
  handler: (body: DenoProcessTypes.InferredType<T>, request?: Request) => Promise<DenoProcessTypes.InferredType<R>> | (() => Promise<DenoProcessTypes.InferredType<R>>);
  packageJson: ProcessSchema.Base;
  bodySchema?: T;
  responseDataSchema?: R;
  port?: number;
}): (request: Request) => Promise<DenoProcessTypes.WrappedResponse<R>> => {
  return async (request: Request): Promise<DenoProcessTypes.WrappedResponse<R>> => {
    // A request has arrived, we assign an execution Id per request.
    Logger.config.addExecutionId();
    const { handler, packageJson, bodySchema, responseDataSchema } = options;
    try {
      const allowedBodyMethods = ["POST", "PATCH", "PUT"];
      const isBodyAllowed = allowedBodyMethods.includes(request.method);
      let response: R;
      if (isBodyAllowed) {
        const body = await DenoProcess.bodyParser(request, bodySchema);
        Logger.info(`Calling handler with parsed body`);
        response = await handler(body, request) as R;
      } else {
        Logger.info(`Calling handler without body`);
        // @ts-ignore this use case is in the verge of typescripts capabilities, it can not defined correctly the type of the handler function.
        response = await handler(undefined, request) as R;
      }

      const responseContent = {
        success: true,
        ...response,
      };
      if (responseDataSchema) {
        zodValidation({
          objectToTest: response,
          schema: responseDataSchema,
          errorHeader: DenoProcessTypes.ERRORS_CONFIG.failedResponseValidation.prefix,
        });
      }
      return Response.json(responseContent);
    } catch (error) {
      Logger.error(error);
      const serveError = new DenoProcess.ServeError(error);
      const status = serveError.statusCode || 500;
      if (!shouldSkipNotifyErrorEnv) {
        await ErrorNotifier.notifyError(serveError.message, packageJson, status);
      }
      const response = {
        success: false,
        error: serveError.message,
      };
      return Response.json(response, { status });
    }
  };
};
