import { ProcessSchema } from "types/process-schema.ts";
import { DenoProcessTypes } from "modules/deno-process/types.ts";
import { _createWrappedHandler } from "modules/deno-process/methods/_createWrappedHandler.ts";

/**
 * This function will start a Deno server with a handler that:
 * - Parses the body of a request using the provided schema (if provided)
 * - Calls the provided handler with the parsed body
 * - Returns a response with the format of {@link DenoProcessTypes.WrappedResponse}
 * - Catches any errors and returns a response with the format of {@link DenoProcessTypes.WrappedResponse}
 * - Notifies the error to the error-notifier service if the error is not a {@link ServeError}
 * @param {Object} options - The options object
 * @param {Function} options.handler - The handler to be called with the parsed body
 * @param {ProcessSchema.Base} options.packageJson - The package.json of the process
 * @param {ZodSchema} options.bodySchema - The schema to validate the body. This must be provided if the handler expects a body
 * @param {ZodSchema} options.responseDataSchema - The schema to validate the response data. If not provided, the response data will not be validated
 * @param {number} options.port - The port to listen on
 * @returns {Server} - The Deno server
 */
export const _serve = <T extends DenoProcessTypes.OptionalSchema, R extends DenoProcessTypes.OptionalSchema>(options: {
  handler: (body: DenoProcessTypes.InferredType<T>, request?: Request) => Promise<DenoProcessTypes.InferredType<R>> | (() => Promise<DenoProcessTypes.InferredType<R>>);
  packageJson: ProcessSchema.Base;
  bodySchema?: T;
  responseDataSchema?: R;
  port?: number;
  shouldSkipNotifyError?: boolean;
}) => {
  const { handler, packageJson, bodySchema, port = 8080, responseDataSchema } = options;
  const wrappedHandler = _createWrappedHandler({ handler, packageJson, bodySchema, responseDataSchema });
  return Deno.serve({ port }, wrappedHandler);
};
