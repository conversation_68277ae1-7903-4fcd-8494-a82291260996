import { ErrorWithStatusCode } from "modules/error-with-status-code/index.ts";
import { DenoProcess } from "modules/deno-process/index.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * A utility class to handle errors in a Deno server, providing common practices for error handling, logging and status code reporting.
 */
export class _ServeError extends ErrorWithStatusCode {
  /**
   * Constructs a new ServeError instance.
   * @param {unknown} error - The error object or message.
   * @param {number} [statusCode=500] - The HTTP status code associated with the error.
   */
  constructor(error: unknown, statusCode: number = 500) {
    /** avoid double processing and logging */
    if (error instanceof DenoProcess.ServeError) return error;
    const errorMessage = error instanceof Error ? error.message : String(error);
    Logger.debug({ errorMessage });
    super(errorMessage, statusCode);
  }
}
