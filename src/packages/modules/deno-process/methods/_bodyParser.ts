import { DenoProcess } from "modules/deno-process/index.ts";
import { DenoProcessTypes } from "../types.ts";
import { zodValidation } from "modules/zod-validation/index.ts";
import { Logger } from "modules/logger/index.ts";
/**
 * This function will parse the body of a request and will validate it with a Zod schema.
 * If the validation fails, it will throw an error.
 * If no schema is provided, the parsing will be skipped entirely.
 * @param {Request} request The request object
 * @param {ZodSchema} [schema] The Zod schema to validate the body. If not provided, the parsing will be skipped entirely.
 * @returns {T | undefined} The parsed body, or undefined if no schema was provided.
 */
export const _bodyParser = async <T extends DenoProcessTypes.OptionalSchema>(request: Request, schema?: T): Promise<DenoProcessTypes.InferredType<T>> => {
  const rawRequestBody = await request.text();
  Logger.debug({ rawRequestBody });
  if (!rawRequestBody) {
    throw new DenoProcess.ServeError(DenoProcessTypes.ERRORS_CONFIG.noBody.prefix, DenoProcessTypes.ERRORS_CONFIG.noBody.code);
  }
  let parsedBody: DenoProcessTypes.InferredType<T>;
  try {
    parsedBody = JSON.parse(rawRequestBody) as DenoProcessTypes.InferredType<T>;
    Logger.debug({ rawRequestBody, parsedBody });
  } catch (error) {
    throw new DenoProcess.ServeError(`${DenoProcessTypes.ERRORS_CONFIG.failedBodyParse.prefix} ${rawRequestBody}`, DenoProcessTypes.ERRORS_CONFIG.failedBodyParse.code);
  }

  if (schema) {
    zodValidation({
      objectToTest: parsedBody,
      schema,
      errorHeader: DenoProcessTypes.ERRORS_CONFIG.failedBodyValidation.prefix,
    });
  }
  return parsedBody as DenoProcessTypes.InferredType<T>;
};
