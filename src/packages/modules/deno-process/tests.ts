import packageJson from "processes/a-dummy-project/package.json" with { type: "json" };
import z from "zod";
import { zodValidation } from "modules/zod-validation/index.ts";
import { assertEquals } from "@std/assert";
import { DenoProcessTypes } from "./types.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Skipping error notification for test purposes
 */
Deno.env.set("_TEST_DENO_WRAPPER_SHOULD_SKIP_NOTIFY_ERROR", "true");
/**
 * Importing asynchronously so the environment variables can be set
 */
const { DenoProcess } = await import("./index.ts");

/**
 * Utility type to help define test cases consistently
 */
type TestCase = {
  description: string;
  /**
   * Switch on and off to work on specific tests. The target is to have all tests run by default.
   */
  active: true; // This type must be `true` when merging PRs
  bodySchema?: z.ZodSchema;
  responseDataSchema?: z.ZodSchema;
  handler: (
    options?: z.infer<z.ZodSchema>,
  ) => Promise<z.infer<z.ZodSchema>>;
  testBody?: z.infer<z.ZodSchema>;
  testOutcome: {
    expectedSuccess: boolean;
    errorMessagePrefix?: string;
    expectResponseValidationFail?: boolean;
  };
};

/**
 * Test cases with different configurations
 */
const testCases: TestCase[] = [
  {
    description: "should successfully serve when making a request with a body",
    active: true,
    testOutcome: {
      expectedSuccess: true,
    },
    bodySchema: z.object({
      name: z.string(),
    }),
    responseDataSchema: z.object({
      expected: z.literal(true),
    }),
    handler: async (
      options?: {
        name: string;
      },
    ) => {
      return { expected: true };
    },
    testBody: {
      name: "John Doe",
    },
  },
  {
    description: "should successfully serve when making a request without a body",
    active: true,
    testOutcome: {
      expectedSuccess: true,
    },
    handler: async () => {
      return;
    },
  },
  {
    description: "should error when making a request without a body, but with a body schema",
    active: true,
    testOutcome: {
      expectedSuccess: false,
      errorMessagePrefix: DenoProcessTypes.ERRORS_CONFIG.noBody.prefix,
    },
    bodySchema: z.object({
      name: z.string(),
    }),
    handler: async () => {
      return;
    },
  },
  {
    description: "should error when making a request with invalid body",
    active: true,
    testOutcome: {
      expectedSuccess: false,
      errorMessagePrefix: DenoProcessTypes.ERRORS_CONFIG.failedBodyParse.prefix,
    },
    testBody: "invalid json",
    bodySchema: z.object({
      name: z.string(),
    }),
    responseDataSchema: z.object({
      expected: z.boolean(),
    }),
    handler: async (
      options?: {
        name: string;
      },
    ) => {
      return { expected: true };
    },
  },
  {
    description: "should error when the response does not match the schema",
    active: true,
    testOutcome: {
      expectedSuccess: false,
      errorMessagePrefix: DenoProcessTypes.ERRORS_CONFIG.failedResponseValidation.prefix,
      expectResponseValidationFail: true,
    },
    responseDataSchema: z.object({
      expected: z.boolean(),
    }),
    handler: async () => {
      return { notExpected: 1 };
    },
  },
];

const baseSuccessfulResultSchema = z.object({
  success: z.literal(true),
});

const errorResultSchema = z.object({
  success: z.literal(false),
  error: z.string(),
});

for (const testCase of testCases) {
  const { handler, description, testBody, bodySchema, responseDataSchema, testOutcome, active } = testCase;
  if (!active) {
    Logger.debug(`Skipping test as marked inactive: ${description}`);
    continue;
  }
  const { expectedSuccess, errorMessagePrefix = "", expectResponseValidationFail } = testOutcome;
  Deno.test(description, async () => {
    const server = DenoProcess.serve({
      handler,
      packageJson,
      bodySchema,
      responseDataSchema,
    });
    const options: RequestInit = {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
    };
    if (testBody) {
      /**
       * Stringify only if the body is an object to avoid false positives when testing passing wrong types
       */
      options.body = typeof testBody === "object" ? JSON.stringify(testBody) : testBody;
    }
    const request = new Request("http://localhost:8080", options);
    const response = await fetch(request) as DenoProcessTypes.WrappedResponse<typeof responseDataSchema>;
    const responseJson = await response.json();
    server.shutdown(); // close the server to avoid leaks
    Logger.debug({ responseJson });
    const isSuccessful = responseJson.success;
    Logger.debug({ isSuccessful, expectedSuccess });
    assertEquals(isSuccessful, expectedSuccess);
    if (isSuccessful) {
      try {
        /**
         * If the response is successful we check if the response matches the exact schema of this specific test case
         */
        const resultSchema = responseDataSchema ? z.intersection(baseSuccessfulResultSchema, z.object({ data: responseDataSchema })) : baseSuccessfulResultSchema;
        zodValidation({
          objectToTest: responseJson,
          schema: resultSchema,
        });
      } catch (error) {
        // In some cases we expect the validation to fail on purpose s
        Logger.debug({ error });
        assertEquals(expectResponseValidationFail, true);
      }
    } else {
      // validate the appropriate response is returned in case of an error
      Logger.debug({ errorMessagePrefix, responseJson, matches: responseJson.error.startsWith(errorMessagePrefix) });
      const specificErrorSchema = errorResultSchema.extend({
        error: z.string().startsWith(errorMessagePrefix),
      });
      zodValidation({
        objectToTest: responseJson,
        schema: specificErrorSchema,
      });
    }
  });
}
