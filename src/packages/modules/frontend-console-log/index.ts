/**
 * Logs the given data to the console if the current stage is not production.
 */
export const frontendConsoleLog = (...data: Parameters<typeof console.log>) => {
  const isCurrentStageLocalhost = window.location.hostname.includes(
    "localhost",
  );
  const isCurrentStageStaging = window.location.hostname.includes("staging");
  const isProduction = !isCurrentStageLocalhost && !isCurrentStageStaging;
  if (!isProduction) {
    console.log(data);
  }
};
