import "processes/trade-finance-checks-handler/src/tests/preparation.ts";
import { describe, it } from "jsr:@std/testing/bdd";
import { assertEquals } from "@std/assert";
import { Logger } from "modules/logger/index.ts";

const { calculateDiffBetweenDates } = await import("../index.ts");
describe("Calculate difference between dates", () => {
  const testCases = [
    {
      invoiceDate: "2025-03-01",
      invoiceDueDate: "2025-03-31",
      expectedDifference: 30,
    },
    {
      invoiceDate: "2025-03-01",
      invoiceDueDate: "2025-05-30",
      expectedDifference: 90,
    },
    {
      invoiceDate: "2025-03-01",
      invoiceDueDate: "2025-03-06",
      expectedDifference: 5,
    },
    {
      invoiceDate: "2025-03-01",
      invoiceDueDate: undefined,
      expectedDifference: undefined,
    },
    {
      invoiceDate: "2025-03-01",
      invoiceDueDate: "2025-03-01",
      expectedDifference: 0,
    },
  ];

  for (const testCase of testCases) {
    it(`Invoice date: ${testCase.invoiceDate}, Invoice due date: ${testCase.invoiceDueDate} should return difference: ${testCase.expectedDifference}`, () => {
      const difference = calculateDiffBetweenDates(testCase.invoiceDate, testCase.invoiceDueDate);
      Logger.debug({ difference });
      assertEquals(difference, testCase.expectedDifference);
    });
  }
});
