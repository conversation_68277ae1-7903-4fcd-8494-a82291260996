/**
 * @param startDate - The start date as a string.
 * @param endDate - The end date as a string.
 * @returns The difference in days between the startDate and the endDate.
 */
export const calculateDiffBetweenDates = (startDate: string, endDate: string | undefined): number | undefined => {
  if (!endDate) {
    return;
  }

  // Convert the start date and end date strings to Date objects
  const start = new Date(startDate);
  const end = new Date(endDate);

  // Calculate the difference in milliseconds
  const differenceInMilliseconds = end.getTime() - start.getTime();

  // Convert the difference from milliseconds to days
  const differenceInDays = differenceInMilliseconds / (1000 * 3600 * 24);

  return Math.ceil(differenceInDays);
};
