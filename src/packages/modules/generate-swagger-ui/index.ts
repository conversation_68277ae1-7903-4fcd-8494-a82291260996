import { Hono } from "npm:hono@4.7.6";
import { swaggerUI } from "npm:@hono/swagger-ui@0.5.1";
import { OpenAPIObject } from "npm:openapi3-ts/oas31";

/**
 * Generates a Swagger UI response for a given OpenAPI specification.
 *
 * @param {Object} options - Configuration options for generating the Swagger UI response.
 * @param {string} options.path - The path at which the Swagger UI will be served.
 * @param {OpenAPIObject} options.spec - The OpenAPI specification object to display in the Swagger UI.
 * @param {Request} options.request - The incoming request object to handle.
 *
 * @returns {Promise<Response>} The response object containing the Swagger UI for the specified OpenAPI spec.
 */

export const generateSwaggerUiResponse = (options: {
  path: string;
  spec: OpenAPIObject;
  request: Request;
}) => {
  const { path, spec, request } = options;
  const app = new Hono();
  app.get(
    path,
    swaggerUI({
      spec: spec,
      url: path,
      title: spec.info.title,
    }),
  );
  return app.fetch(request);
};
