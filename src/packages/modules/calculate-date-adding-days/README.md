# Calculate Date Adding Days

Calculates a new date by adding a specified number of days to a given start date. This function takes a start date and a number of days, adds the days to the start date, and returns the resulting date as a string in ISO format (YYYY-MM-DD).

## Test

To run tests, following command:

```
deno test src/packages/modules/calculate-date-adding-days/tests/calculateDateAddingDays.test.ts
```
