/**
 * @param startDate - The start date as a string.
 * @param days - The number of days to add to the start date.
 * @returns The resulting date as a string in ISO format (YYYY-MM-DD).
 */
export const calculateDateAddingDays = (startDate: string, days: number): string => {
  const startDateObject = new Date(startDate);
  const resultingDate = new Date(startDateObject.getTime() + days * 24 * 60 * 60 * 1000);
  return resultingDate.toISOString().split("T")[0];
};
