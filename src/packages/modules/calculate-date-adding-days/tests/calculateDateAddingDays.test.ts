import "processes/trade-finance-checks-handler/src/tests/preparation.ts";
import { describe, it } from "jsr:@std/testing/bdd";
import { assertEquals } from "@std/assert";
import { Logger } from "modules/logger/index.ts";

const { calculateDateAddingDays } = await import("../index.ts");

describe("Calculate dates adding dates", () => {
  const testCases = [
    {
      invoiceDate: "2025-03-01",
      days: 90,
      expectedDueDate: "2025-05-30",
    },
    {
      invoiceDate: "2025-03-01",
      days: 5,
      expectedDueDate: "2025-03-06",
    },
    {
      invoiceDate: "2025-03-01",
      days: 0,
      expectedDueDate: "2025-03-01",
    },
    {
      invoiceDate: "2025-02-28",
      days: 1,
      expectedDueDate: "2025-03-01",
    },
  ];

  for (const testCase of testCases) {
    it(`Invoice date: ${testCase.invoiceDate}, Days: ${testCase.days} should return due date: ${testCase.expectedDueDate}`, () => {
      const dueDate = calculateDateAddingDays(testCase.invoiceDate, testCase.days);
      Logger.debug({ dueDate });
      assertEquals(dueDate, testCase.expectedDueDate);
    });
  }
});
