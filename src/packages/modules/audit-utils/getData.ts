import { z, ZodObject } from "zod";
import { getSheetObjectArrayFromTable } from "../get-sheet-object-array-from-table/index.ts";

/**
 * @description
 * This function will obtain and serialize the data from a spreadsheet.
 * @param {object} params The params
 * @param {GoogleAppsScript.Spreadsheet.Sheet} params.sheet The sheet to obtain the data from
 * @param {(row: z.infer<typeof RowZodSchema>) => boolean} params.filterBareRowFunction The function to filter the rows from the sheet
 * @param {(row: z.infer<typeof RowZodSchema>[]) => K[]} params.formatRows The function to format the rows from the sheet. (Not one by one, the whole array)
 * @param {number} params.excludedRowsAtTop The number of rows to exclude at the top
 * @param {number} params.excludedRowsAtBottom The number of rows to exclude at the bottom
 * @param {ZodObject<T>} params.RowZodSchema The zod schema of the rows
 * @returns {object} The serialized data and the incorrect rows
 */
export function getData<K, T extends z.ZodRawShape>({
  sheet,
  filterBareRowFunction,
  formatRows,
  excludedRowsAtTop,
  excludedRowsAtBottom,
  RowZodSchema,
}: {
  sheet: GoogleAppsScript.Spreadsheet.Sheet;
  filterBareRowFunction: (row: z.infer<typeof RowZodSchema>) => boolean;
  formatRows: (row: z.infer<typeof RowZodSchema>[]) => K[];
  excludedRowsAtTop: number;
  excludedRowsAtBottom: number;
  RowZodSchema: ZodObject<T>;
}): {
  dataSerialized: K[];
  incorrectRows: string[];
} {
  const bareTable = sheet.getDataRange().getValues();

  const table = bareTable.slice(
    excludedRowsAtTop,
    excludedRowsAtBottom > 0 ? -excludedRowsAtBottom : undefined,
  );

  console.log("Table obtained");
  console.log("Table: ", table);

  const { correctRows, incorrectRows } = getSheetObjectArrayFromTable({
    table,
    RowZodSchema,
  });

  console.log("Incorrects and Correct Rows obtained");
  console.log("Incorrects Rows: ", incorrectRows);

  const correctRowsFiltered = correctRows.filter((row) => {
    return filterBareRowFunction(row);
  });
  const dataSerialized = formatRows(correctRowsFiltered);
  const incorrectRowsFormatted = incorrectRows.map(({ row }) => {
    return Object.values(row!).join(", ");
  });

  console.log("Data serialized");

  return { dataSerialized, incorrectRows: incorrectRowsFormatted };
}
