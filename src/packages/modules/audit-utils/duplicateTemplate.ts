/**
 * Creates a copy of the template file
 * @param {object} params
 * @param {string} params.originalFileName The name of the original file
 * @param {number} params.numberOfCharactersToCopy The number of characters to copy from the original file name
 * @param {string} params.suffixName The sufix name to add to the copy
 * @param {string} params.templateId The id of the template file
 * @param {string} params.resultFolderId The id of the folder where the copy will be created
 * @returns {string} The id of the copy
 */
export function getTemplateCopyId({
  originalFileName,
  numberOfCharactersToCopy,
  suffixName,
  templateId,
  resultFolderId,
}: {
  originalFileName: string;
  numberOfCharactersToCopy: number;
  suffixName: string;
  templateId: string;
  resultFolderId: string;
}): string {
  const originalCharactersToCopy = originalFileName.slice(
    0,
    numberOfCharactersToCopy,
  );

  const templateCopyName = `${originalCharactersToCopy}${suffixName}`;
  console.log(`Template copy name: ${templateCopyName}`);

  const resultFolder = DriveApp.getFolderById(resultFolderId);
  const templateCopy = DriveApp.getFileById(templateId).makeCopy(
    templateCopyName,
    resultFolder,
  );

  const templateCopyId = templateCopy.getId();
  return templateCopyId;
}
