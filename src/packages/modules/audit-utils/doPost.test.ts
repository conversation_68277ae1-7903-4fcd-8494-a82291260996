import { gasTestWrapper } from "../gas-test-wrapper/index.ts";
import { Audit } from "./types.ts";
import { scriptNetworkCall } from "../scriptNetworkCall/index.ts";

export function auditsTestEndpointNetwork({
  url,
  testData,
}: {
  url: string;
  testData: unknown;
}) {
  const networkCallWrapper = () => {
    console.log({
      urlStaging: url,
      testData,
    });

    const response: Audit.Response = scriptNetworkCall({
      url,
      body: testData,
    });

    console.log(response);

    handleResponse(response);
  };

  gasTestWrapper({
    function: networkCallWrapper,
    description: "Test audit using a network call",
  });
}

function handleResponse(response: Audit.Response) {
  if (response.success === true) {
    console.log(`Audit document ${response.documentId} was created`);
    deleteDocument(response.documentId);
  } else {
    console.log(`Error: ${response.error}`);
    throw new Error(response.error);
  }
}

function deleteDocument(documentId: string) {
  console.log(`Deleting document ${documentId}`);
  DriveApp.getFileById(documentId).setTrashed(true);
}
