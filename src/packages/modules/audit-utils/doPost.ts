import { ErrorNotifier } from "../error-notifier-gas/index.ts";
import { bodyParserForGasWithZodValidation } from "../gas-body-parser/index.ts";
import { Audit } from "./types.ts";
import notifyPATeam from "./notifyPATeam.ts";
import { ProcessSchema } from "types/process-schema.ts";

/**
 * @description
 * This function is a wrapper for making a doPost function for an audit.
 * @param {object} options The options object
 * @param {GoogleAppsScript.Events.DoPost} options.event The event object
 * @param {(fileId: string) => string} options.auditFunction The audit function
 * @param {ProcessSchema.Base | ProcessSchema.EndpointProcess} options.packageJson The package.json of the process
 * @returns {GoogleAppsScript.Content.TextOutput} The response
 */
export function auditsDoPost({
  event,
  auditFunction,
  packageJson,
}: {
  event: GoogleAppsScript.Events.DoPost;
  auditFunction: (fileId: string) => string;
  packageJson: ProcessSchema.Base | ProcessSchema.EndpointProcess;
}): GoogleAppsScript.Content.TextOutput {
  try {
    const { fileId } = bodyParserForGasWithZodValidation({
      body: event.postData.contents,
      Schema: Audit.bodySchema,
    });
    const documentId = auditFunction(fileId);
    notifyPATeam(documentId);

    const response: Audit.Response = {
      success: true,
      status: 200,
      documentId: `${documentId}`,
    };

    console.log("Response: ", response);
    return ContentService.createTextOutput(
      JSON.stringify(response),
    ).setMimeType(ContentService.MimeType.JSON);
  } catch (error) {
    ErrorNotifier.notifyError(packageJson, `Error in audit: ${error.message}`);

    const response: Audit.Response = {
      success: false,
      status: 500,
      error: error.message,
    };
    console.log("Error response: ", response);
    return ContentService.createTextOutput(
      JSON.stringify(response),
    ).setMimeType(ContentService.MimeType.JSON);
  }
}
