import { getGASScriptProperty } from "../get-gas-script-property/index.ts";

export default function notifyPATeam(documentId: string) {
  const recipients = getGASScriptProperty("EMAIL_RECIPIENTS");

  const reportLink = `<a href='https://docs.google.com/spreadsheets/d/${documentId}'>link to the report</a>`;
  const emailBody = `<p>Hi PA team,</p>
</br>
<p>The report is completed and is available at ${reportLink}</p>
</br>
<p>This is an automated email, please do not reply. If you have any questions, <NAME_EMAIL></p>`;

  MailApp.sendEmail({
    to: recipients,
    subject: `Report Completed - ${new Date().toISOString()}`,
    htmlBody: emailBody,
  });
  console.log("Email sent to PA team.");
}
