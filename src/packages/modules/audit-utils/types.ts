import { z } from "zod";

/**
 * This namespace contains all the types needed for audits.
 */
export namespace Audit {
  export type Response =
    | {
      status: number;
      success: true;
      documentId: string;
    }
    | {
      status: number;
      success: false;
      error: string;
    };

  /**
   * The body schema for the audit endpoint. (doPost)
   */
  export const bodySchema = z.object({
    fileId: z.string(),
  });

  /**
   * This type represents the results of the audit.
   */
  export type DataResults = {
    reviewedUsersCounter: number;
    validUsersCounter: number;
    usersWithDifferentLimits: string[];
    usersThatAreNotInPlatform: string[];
    usersThatAreNotInLogs: string[];
    differentPermissionsUsers: string[];
    differentCurrencyUsers: string[];
  };
}
