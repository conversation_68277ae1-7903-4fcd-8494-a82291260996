import { COLUMN_COUNTERS_USERS, COLUMN_START_TOP_DATA, COLUMN_START_USER_LISTS, ROW_COUNTERS_USERS, ROW_START_TOP_DATA, ROW_START_USER_LISTS } from "./constants.ts";
/**
 * @description
 * This function will set the headers data in the results spreadsheet.
 * @param {object} params
 * @param {unknown[][]} params.headersData The headers data
 * @param {GoogleAppsScript.Spreadsheet.Sheet} params.sheet The sheet where the headers data will be set
 */
export function setHeadersData({
  headersData,
  sheet,
}: {
  headersData: unknown[][];
  sheet: GoogleAppsScript.Spreadsheet.Sheet;
}) {
  const rows = headersData.length;
  const columsLengths = headersData.map((row) => row.length);
  const maxColums = Math.max(...columsLengths);

  const headersRange = sheet.getRange(
    ROW_START_TOP_DATA,
    COLUMN_START_TOP_DATA,
    rows,
    maxColums,
  );
  headersRange.setValues(headersData);

  console.log(
    "Headers data set (reviewed users counter, valid users counter, date)",
  );
}

/**
 * @description
 * This function will set the counters row in the results spreadsheet.
 * @param {object} params
 * @param {number[][]} params.counters The counters data
 * @param {GoogleAppsScript.Spreadsheet.Sheet} params.sheet The sheet where the counters data will be set
 */
export function setCountersRow({
  counters,
  sheet,
}: {
  counters: number[][];
  sheet: GoogleAppsScript.Spreadsheet.Sheet;
}) {
  const rows = counters.length;
  const columsLengths = counters.map((row) => row.length);
  const maxColums = Math.max(...columsLengths);

  const countersRange = sheet.getRange(
    ROW_COUNTERS_USERS,
    COLUMN_COUNTERS_USERS,
    rows,
    maxColums,
  );
  countersRange.setValues(counters);

  console.log("Counters row set");
}

/**
 * @description
 * This function will set the users lists data in the results spreadsheet.
 * @param {object} params
 * @param {string[]} params.usersThatAreNotInPlatform The users that are not in platform
 * @param {string[]} params.usersThatAreNotInLogs The users that are not in logs
 * @param {string[]} params.usersWithDifferentLimits The users with different limits
 * @param {string[]} params.differentPermissionsUsers The users with different permissions
 * @param {string[]} params.differentCurrencyUsers The users with different currency
 * @param {string[]} params.usersWithWrongFormatLogs The users with wrong format in logs
 * @param {string[]} params.usersWithWrongFormatPlatform The users with wrong format in platform
 * @param {GoogleAppsScript.Spreadsheet.Sheet} params.sheet The sheet where the users data will be set
 */
export function setUsersListsData({
  usersThatAreNotInPlatform,
  usersThatAreNotInLogs,
  usersWithDifferentLimits,
  differentPermissionsUsers,
  differentCurrencyUsers,
  usersWithWrongFormatLogs,
  usersWithWrongFormatPlatform,
  sheet,
}: {
  usersThatAreNotInPlatform: string[];
  usersThatAreNotInLogs: string[];
  usersWithDifferentLimits: string[];
  differentPermissionsUsers: string[];
  differentCurrencyUsers: string[];
  usersWithWrongFormatLogs: string[];
  usersWithWrongFormatPlatform: string[];
  sheet: GoogleAppsScript.Spreadsheet.Sheet;
}) {
  if (usersThatAreNotInPlatform.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS,
        usersThatAreNotInPlatform.length,
        1,
      )
      .setValues(usersThatAreNotInPlatform.map((user) => [user]));
    console.log("Users that are not in platform set");
  }
  if (usersThatAreNotInLogs.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 1,
        usersThatAreNotInLogs.length,
        1,
      )
      .setValues(usersThatAreNotInLogs.map((user) => [user]));
    console.log("Users that are not in logs set");
  }
  if (usersWithDifferentLimits.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 2,
        usersWithDifferentLimits.length,
        1,
      )
      .setValues(usersWithDifferentLimits.map((user) => [user]));
    console.log("Users with less limits than allowed set");
  }
  if (differentPermissionsUsers.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 3,
        differentPermissionsUsers.length,
        1,
      )
      .setValues(differentPermissionsUsers.map((user) => [user]));
    console.log("Users with different permissions set");
  }
  if (differentCurrencyUsers.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 4,
        differentCurrencyUsers.length,
        1,
      )
      .setValues(differentCurrencyUsers.map((user) => [user]));
    console.log("Users with different currency set");
  }
  if (usersWithWrongFormatLogs.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 5,
        usersWithWrongFormatLogs.length,
        1,
      )
      .setValues(usersWithWrongFormatLogs.map((user) => [user]));
    console.log("Users with wrong format in logs set");
  }
  if (usersWithWrongFormatPlatform.length > 0) {
    sheet
      .getRange(
        ROW_START_USER_LISTS,
        COLUMN_START_USER_LISTS + 6,
        usersWithWrongFormatPlatform.length,
        1,
      )
      .setValues(usersWithWrongFormatPlatform.map((user) => [user]));
    console.log("Users with wrong format in platform set");
  }
  console.log("Users data set");
}
