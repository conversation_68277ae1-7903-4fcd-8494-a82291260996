import URLFetchRequestOptions = GoogleAppsScript.URL_Fetch.URLFetchRequestOptions;
import { zodValidation } from "../zod-validation/index.ts";
import { SecretManager } from "../secret-manager/index.ts";
import { makeUrlFetchCall } from "../makeUrlFetchCall/index.ts";
import { AtlassianGraphQL, atlassianSecretSchema } from "./types.ts";
import { JiraClientGas } from "../jira-gas/index.ts";
import { Logger } from "modules/logger/index.ts";

/**
 * Returns an atlassian client
 * @returns
 */
export const getAtlassianGraphQLClient = () => {
  Logger.info("Getting atlassian graphql client");
  const secretName = JiraClientGas.SECRET_NAMES.JIRA_AUTOMATION;
  const secret = SecretManager.fetchSecret(
    secretName,
  ) as AtlassianGraphQL.AtlassianSecret;
  zodValidation({
    objectToTest: secret,
    schema: atlassianSecretSchema,
    description: "Validating atlassian secret",
  });
  const { email, apiKey } = secret;
  const atlassianClient = new AtlassianGraphQLClient(email, apiKey);
  return atlassianClient;
};

/**
 * Wrapper class for Atlassian GraphQL API.
 *
 * @export
 * @class AtlassianClient
 */
export class AtlassianGraphQLClient {
  readonly client: string;
  private readonly key: string;
  private readonly creds: string;
  public site: string;
  readonly url: string;

  /**
   * Constructor of Atlassian.
   * @param client - Atlassian API client email
   * @param key - Atlassian API key
   */
  constructor(client: string, key: string) {
    this.client = client;
    this.key = key;
    this.site = "fxsolutions";
    this.url = "https://fxsolutions.atlassian.net/gateway/api/graphql";
    this.creds = Utilities.base64EncodeWebSafe(`${this.client}:${this.key}`);
  }

  makeAtlassianCall<T extends object>(
    method: AtlassianGraphQL.RequestMethod,
    payload?: string,
    scopes: string[] = [],
  ) {
    const params: URLFetchRequestOptions = {
      headers: {
        Authorization: `Basic ${this.creds}`,
      },
      contentType: "application/json",
    };

    if (scopes.length > 0) {
      params.headers = params.headers || {};
      params.headers["X-OAuth-Scopes"] = scopes.join(" ");
    }
    params.method = method;
    if (payload) {
      params.payload = payload;
    }
    const response = makeUrlFetchCall<T>(this.url, params);
    return response;
  }

  public async compassGetAllComponents(query: string): Promise<AtlassianGraphQL.AllCompassComponents> {
    Logger.info("Getting all components");
    const allComponents: AtlassianGraphQL.AllCompassComponents = [];
    let hasNextPage = true;
    let endCursor = "";

    while (hasNextPage) {
      const variables = {
        cloudId: "d5af7922-e16b-4a9a-ba6d-c9b8117b3a98",
        query: {
          after: endCursor,
          first: 200,
        },
      };
      const method = "post";
      const payload = JSON.stringify({ query, variables });
      const response = this.makeAtlassianCall<{
        data: {
          compass: {
            searchComponents: {
              nodes: AtlassianGraphQL.AllCompassComponents;
              pageInfo: {
                hasNextPage: boolean;
                endCursor: string;
              };
            };
          };
        };
      }>(method, payload);
      if (!response.data) {
        throw new Error(`No data returned from Atlassian API: ${response}`);
      }
      hasNextPage = response.data.compass.searchComponents.pageInfo
        ?.hasNextPage;
      endCursor = response.data.compass.searchComponents.pageInfo?.endCursor;
      const nodes = response.data.compass.searchComponents.nodes;
      allComponents.push(...nodes);
    }
    Logger.info(`In total, ${allComponents.length} retrieved`);
    return allComponents;
  }

  public async getComponentById(
    query: string,
  ): Promise<AtlassianGraphQL.CompassComponentInDepth> {
    try {
      const variables = {
        outwardRelationshipsQuery: { direction: "OUTWARD" },
        inwardRelationshipsQuery: { direction: "INWARD" },
      };
      const method = "post";
      const payload = JSON.stringify({ query, variables });
      const response = this.makeAtlassianCall<{
        data: {
          compass: {
            component: AtlassianGraphQL.CompassComponentInDepth;
          };
        };
      }>(method, payload);
      return response.data.compass.component;
    } catch (error) {
      const errorMessage = `Error getting component info for ${error.message}: ${error}`;
      Logger.info(errorMessage);
      throw new Error(errorMessage);
    }
  }
}
