export const getAllComponentsQuery = `query searchCompassComponents($cloudId: String!, $query: CompassSearchComponentQuery) {
    compass {
      searchComponents(cloudId: $cloudId, query: $query) {
        ... on CompassSearchComponentConnection {
          nodes {
            link
            component {
              id
              name
              typeId
              ownerId
              labels {
                name
              }
              links {
                name
                type
                url
              }
              fields {
                definition {
                  id
                  __typename
                }
                ... on CompassEnumField {
                  value
                  __typename
                }
                __typename
              }
            }
          }
          pageInfo {
            hasNextPage
            endCursor
          }
        }
        ... on QueryError {
          message
          extensions {
            statusCode
            errorType
          }
        }
      }
    }
  }`;

export const getComponentQuery = (id: string) => `
  query compass_relationships_getComponentRelationships($outwardRelationshipsQuery: CompassRelationshipQuery!, $inwardRelationshipsQuery: CompassRelationshipQuery!) {
    compass @optIn(to: "compass-beta") {
      component(id: "${id}") {
        __typename
        ... on CompassComponent {
          ...CompassComponentInRelationshipView
          dataManager {
            ecosystemAppId
            externalSourceURL
            __typename
          }
          ...CompassComponentCustomFields
          outwardRelationships: relationships(query: $outwardRelationshipsQuery) {
            __typename
            ... on CompassRelationshipConnection {
              nodes {
                ...CompassRelationshipInRelationshipView
                __typename
              }
              __typename
            }
            ... on QueryError {
              message
              extensions {
                statusCode
                errorType
                __typename
              }
              __typename
            }
          }
          inwardRelationships: relationships(query: $inwardRelationshipsQuery) {
            __typename
            ... on CompassRelationshipConnection {
              nodes {
                ...CompassRelationshipInRelationshipView
                __typename
              }
              __typename
            }
            ... on QueryError {
              message
              extensions {
                statusCode
                errorType
                __typename
              }
              __typename
            }
          }
          __typename
        }
        ... on QueryError {
          message
          extensions {
            statusCode
            errorType
            __typename
          }
          __typename
        }
      }
      __typename
    }
  }

  fragment CompassComponentCustomFields on CompassComponent {
  ...CompassComponentCore
  customFields {
    __typename
    definition {
      id
      name
      __typename
    }
    ... on CompassCustomBooleanField {
      booleanValue
      __typename
    }
    ... on CompassCustomTextField {
      textValue
      __typename
    }
    ... on CompassCustomNumberField {
      numberValue
      __typename
    }
    ... on CompassCustomUserField {
      userIdValue
      userValue {
        accountId
        picture
        name
        __typename
      }
      __typename
    }
    ... on CompassCustomSingleSelectField {
      option {
        id
        value
        __typename
      }
      __typename
    }
    ... on CompassCustomMultiSelectField {
      options {
        id
        value
        __typename
      }
      __typename
    }
  }
  __typename
}

  fragment CompassComponentCore on CompassComponent {
    id
    __typename
  }

  fragment CompassComponentFields on CompassComponent {
    fields {
      ... on CompassEnumField {
        value
        __typename
      }
      definition {
        id
        __typename
      }
      __typename
    }
    __typename
  }

  fragment CompassComponentInRelationshipView on CompassComponent {
    ...CompassComponentCore
    id
    typeId
    name
    description
    ownerId
    dataManager {
      ecosystemAppId
      externalSourceURL
      __typename
    }
    events(query: {eventTypes: [DEPLOYMENT], first: 1}) {
      ... on CompassEventConnection {
        nodes {
          ...CompassDeploymentEventCommon
          __typename
        }
        __typename
      }
      __typename
    }
    ...CompassComponentFields
    __typename
  }

  fragment CompassDeploymentEventCommon on CompassDeploymentEvent {
    displayName
    environment {
      displayName
      __typename
    }
    lastUpdated
    state
    url
    __typename
  }

  fragment CompassRelationshipCore on CompassRelationship {
    type
    startNode {
      id
      __typename
    }
    endNode {
      id
      __typename
    }
    __typename
  }

  fragment CompassRelationshipInRelationshipView on CompassRelationship {
    ...CompassRelationshipCore
    startNode {
      ...CompassComponentInRelationshipView
      __typename
    }
    endNode {
      ...CompassComponentInRelationshipView
      __typename
    }
    changeMetadata {
      createdAt
      __typename
    }
    __typename
  }
`;
