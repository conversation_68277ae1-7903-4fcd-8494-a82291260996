import z from "zod";

export const atlassianSecretSchema = z.object({
  email: z.string(),
  apiKey: z.string(),
});

export declare namespace AtlassianGraphQL {
  export type RequestMethod = "get" | "post" | "put" | "delete" | "patch";
  export type CompassField = {
    definition: {
      id: string;
      __typename: string;
    };
    value: string;
    __typename: string;
  };

  export type CompassComponent = {
    id: string;
    name: string;
    typeId: string;
    ownerId: string;
    links: { name: string; type: string; url: string }[];
    labels: { name: string }[];
    fields: CompassField[];
  };

  export type AllCompassComponents = {
    link: string;
    component: CompassComponent;
  }[];

  export type CompassFieldDefinition = {
    id: string;
    __typename: string;
  };

  export type CompassEnumField = {
    __typename: string;
    definition: CompassFieldDefinition;
    value: string[];
  };

  export type CompassEventConnection = {
    __typename: string;
    nodes: any[];
  };

  export type CompassCustomSingleSelectField = {
    __typename: string;
    definition: CompassCustomFieldDefinition;
    option: CompassCustomSelectFieldOption;
  };

  export type CompassRelationshipNode = {
    type: string;
    startNode: {
      id: string;
      name: string;
      __typename: string;
    };
    endNode: {
      id: string;
      __typename: string;
    };
    __typename: string;
  };

  export type CompassRelationshipConnection = {
    __typename: string;
    nodes: CompassRelationshipNode[];
  };

  export type CompassComponentInDepth = {
    __typename?: string;
    id?: string;
    typeId?: string;
    name?: string;
    description?: string;
    ownerId?: string;
    dataManager?: string;
    events?: CompassEventConnection;
    fields?: CompassEnumField[];
    customFields: CompassCustomField[];
    outwardRelationships: CompassRelationshipConnection;
    inwardRelationships: CompassRelationshipConnection;
  };

  export type CompassCustomFieldDefinition = {
    id: string;
    name: string;
    __typename: string;
  };

  export type CompassCustomSelectFieldOption = {
    value: string;
    __typename: string;
    id: string;
  };

  export type CompassCustomField = {
    __typename: string;
    definition: CompassCustomFieldDefinition;
    textValue?: string | null;
    userIdValue?: string;
    userValue?: {
      accountId: string;
      picture: string;
      name: string;
      __typename: string;
    };
    option?: CompassCustomSelectFieldOption;
  };

  export type AtlassianSecret = z.infer<typeof atlassianSecretSchema>;
}
