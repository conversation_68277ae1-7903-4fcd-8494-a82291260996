import { zodValidationGAS } from "modules/zod-validation/gas.ts";
import { z, ZodObject } from "zod";

type IncorrectRowWithErrors = {
  row: unknown;
  error: Error;
};

/**
 * This function takes the headers and a Zod schema and returns the mandatory headers.
 * - The headers are validated to check if all the mandatory headers are present.
 * @param {object} options
 * @param {string[]} options.headers The headers to validate.
 * @param {z.ZodObject} options.RowZodSchema The Zod schema to validate the headers.
 * @returns The mandatory headers.
 */
function getMandatoryHeaders<T extends z.ZodRawShape>({
  headers,
  RowZodSchema,
}: {
  headers: (string | number | symbol)[];
  RowZodSchema: ZodObject<T>;
}): (keyof T)[] {
  type HeadersType = keyof T;
  const mandatoryHeaders: HeadersType[] = Object.keys(RowZodSchema.shape);

  const headersMissing = mandatoryHeaders.filter(
    (header) => !headers.includes(header),
  );

  const isThereMissingHeaders = headersMissing.length !== 0;
  if (isThereMissingHeaders) {
    throw new Error(
      `The following headers are missing: ${headersMissing.join(", ")}`,
    );
  }

  return mandatoryHeaders;
}

/**
 * This function takes a table and a Zod schema and returns an array of objects.
 *  - The table is an array of arrays, where the first array is the headers and the rest are the rows.
 *  - The Zod schema is used to validate the headers and the rows.
 *  - The headers are validated to check if all the mandatory headers are present.
 *  - The rows are serialized to an object, where the keys are the headers and the values are the cells.
 *  - The rows are validated to check if they match the Zod schema.
 * @param {object} options
 * @param {string[][]} options.table The table to convert to an array of objects.
 * @param {z.ZodObject} options.RowZodSchema The Zod schema to validate the rows.
 * @returns An object with the correct rows and the incorrect rows.
 * @example
 * const table = [
 *  ["name", "age"],
 *  ["John", 20],
 *  ["Mary", 30],
 * ];
 * const RowZodSchema = z.object({
 *  name: z.string(),
 *  age: z.number(),
 * });
 * const { correctRows, incorrectRows } = getSheetObjectArrayFromTable({
 *  table,
 *  RowZodSchema,
 * });
 * console.log(correctRows);
 * // [
 * // { name: "John", age: 20 },
 * // { name: "Mary", age: 30 },
 * // ];
 * console.log(incorrectRows);
 * // [];
 */
export function getSheetObjectArrayFromTable<T extends z.ZodRawShape>({
  table,
  RowZodSchema,
}: {
  table: (string | number | symbol)[][];
  RowZodSchema: ZodObject<T>;
}): {
  correctRows: z.infer<typeof RowZodSchema>[];
  incorrectRows: IncorrectRowWithErrors[];
} {
  console.log("Getting sheet data");
  // Headers validation
  const headers = table.shift()!;
  const mandatoryHeaders = getMandatoryHeaders({ headers, RowZodSchema });

  console.log("getSheetObjectArrayFromTable: Headers validation passed.");

  // Rows serialization
  type Row = z.infer<typeof RowZodSchema>;
  const emptyRow: Row = Object.fromEntries(
    mandatoryHeaders.map((header) => [header, ""]),
  ) as Row;

  // This array has the same length as the headers array, but only the mandatory headers are present, the rest are undefined.
  const headersArrayWithOnlyMandatories = headers.map((header) => mandatoryHeaders.find((mandatoryHeader) => mandatoryHeader === header));

  const filteredRows: Row[] = table.map((row) => {
    const filterRow: Row = { ...emptyRow };

    row.forEach((cell, index) => {
      const mandatoryHeader = headersArrayWithOnlyMandatories[
        index
      ] as Row[keyof Row];

      if (mandatoryHeader !== undefined) {
        filterRow[mandatoryHeader] = cell;
      }
    });

    return filterRow;
  });

  console.log("getSheetObjectArrayFromTable: Rows serialization passed.");

  const correctRows: Row[] = [];
  const incorrectRows: IncorrectRowWithErrors[] = [];

  filteredRows.forEach((row) => {
    try {
      zodValidationGAS({
        objectToTest: row,
        schema: RowZodSchema,
        description: "Sheet row",
        print: false,
      });
      correctRows.push(row);
    } catch (error) {
      incorrectRows.push({ row, error });
    }
  });

  console.log("getSheetObjectArrayFromTable: Rows validation passed.");
  console.log({
    correctRowsLength: correctRows.length,
    incorrectRowsLength: incorrectRows.length,
  });

  return { correctRows, incorrectRows };
}
