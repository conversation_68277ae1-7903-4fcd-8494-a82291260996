import { endTest, logTest } from "./log-functions.ts";

/**
 * @description
 * This function will wrap a function and will log that function's execution time and if it was successful or not.
 * @param {object} options
 * @param {() => void} options.function The function to execute
 * @param {string} options.description The description of the function
 * @param {boolean} [options.print=true] If true, it will print the time it took to execute the function
 */
export const gasTestWrapper = (options: {
  function: () => void;
  description: string;
  print?: boolean;
}) => {
  const { description, print = true } = options;

  if (print) {
    logTest(description);
    console.time(description);
  }

  options.function();

  if (print) {
    console.timeEnd(description);
    endTest(`${description} completed successfully`);
  }
};
