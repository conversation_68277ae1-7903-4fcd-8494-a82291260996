# GAS test wrapper

This is a simple utility to help you test your Google Apps Script code.

It will add beginning and end logging to your functions, and will also add a time calculation to the end of the function.

## Usage

You should check the result of your function and throw an error if it is not matching the expectations.

```typescript
import { gasTestWrapper } from "gas-test-wrapper";

export const testSomething = () => {
  gasTestWrapper({
    description: "Test something",
    function: testFunction,
  });
};
const testFunction = () => {
  const someResult = doSomething();
  const isTheResultMatchingExpectations = someResult === "expected result";
  if (!isTheResultMatchingExpectations) {
    throw new Error("The result is not matching the expectations");
  }
};
```
