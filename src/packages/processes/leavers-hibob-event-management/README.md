# Leavers Hibob Event Management

## Goal

To ensure all employees that have reached their leaving date on Hibob and any
Hibob profiles that have been deleted are included in the
[leavers queue](https://docs.google.com/spreadsheets/d/1zuSDR2BuAIa-61ArG81YgE4sBKqf-domTTXYEGxCekY/edit#gid=*********)

### Requirements

Either nothing happens or a new row in the
[leavers queue](https://docs.google.com/spreadsheets/d/1zuSDR2BuAIa-61ArG81YgE4sBKqf-domTTXYEGxCekY/edit#gid=*********)

## Input interface

Triggered by Hibob V2 webhooks:

`employee.left` and `employee.deleted` events are expected, containing following data:

```json
{
 "companyId": 637323,
 "type": "employee.left",
 "triggeredBy": "3332883804594373380",
 "triggeredAt": "2024-12-26T11:29:14.399997",
 "data": {
   "employeeId": "3332883894235038486"
 }
}
```

## Output interface

None

## Tools/Technologies

- [Deno](https://deno.com/)
- [Google Cloud Run](https://console.cloud.google.com/home/<USER>
- [HiBob V2 API](https://apidocs.hibob.com/reference/api-calls-for-webhook-events#employee-event-example)
- [Reverse-Proxy webhooks](https://console.cloud.google.com/firestore/databases/-default-/data/panel/reverse-proxy-webhooks/incidentWarRoomCreation?inv=1&invt=Abrpbw&project=appscript-296515)
- [Firestore](https://firebase.google.com/docs/firestore)
- [Google Sheets API](https://developers.google.com/sheets/api)
- [Jira API](https://developer.atlassian.com/cloud/jira/platform/rest/v3/intro/)

## Solution

The process is triggered by a HiBob V2 webhook event, which can be of two types:
- `employee.left`: Employee termination route (the employee left).
- `employee.deleted`: Employee profile deleted route (HiBob profile was deleted).

The new implementation uses Deno, Firestore as the source of truth (SOT), and keeps both Firestore and Google Sheets in sync. The HiBob V2 webhook event contains minimal information (only the employee ID), so all additional data is fetched from Firestore or Google Workspace APIs.

---

### Employee Termination Route (`employee.left`)

1. **Receive `employee.left` event** from HiBob V2 webhook (contains only employee ID).
2. **Look up the employee in Firestore** (Leavers DB) using the HiBob ID.
   - If the employee exists and has a `googleAccountDeletion` timestamp, no further action is taken.
3. **Fetch Google account** for the employee using the HiBob ID.
   - If no Google account is found, the process stops and logs an error.
4. **Add the employee to the Leavers Queue** (Google Sheet) with relevant details.
5. **Add the employee to Firestore** (Leavers DB) with all required metadata.
6. **Notify the Security (Infosec) team** via email about the new leaver and that they were added to the queue.

#### Flowchart

```mermaid
flowchart TD
    A[HiBob event: employee.left] --> B[Lookup employee in Firestore Leavers DB]
    B -->|Exists & googleAccountDeletion set| C[No action]
    B -->|Not found or not offboarded| D[Fetch Google account by HiBob ID]
    D -->|Not found| E[Log error & stop]
    D -->|Found| F[Add to Leavers Queue]
    F --> G[Add to Firestore Leavers DB]
    G --> H[Notify Security team]
```

---

### Employee Profile Deleted Route (`employee.deleted`)

1. **Receive `employee.deleted` event** from HiBob V2 webhook (contains only employee ID).
2. **Look up the employee in Firestore** (Onboarding DB) using the HiBob ID.
   - If not found, log an error and stop.
3. **Fetch Google account** for the employee using the HiBob ID (optional, may not exist).
4. **Create a Jira issue** for the HR team for auditing purposes.
5. **Notify the Security (SecOps) team** via email about the profile deletion.
6. **Add the employee to the Deleted Profiles sheet** (Google Sheet) for HR reporting.
7. **Check onboarding status in Firestore**:
   - If the employee was **not onboarded** (no `processedTimestamp`), update the onboarding Firestore document with a "DELETED HIBOB PROFILE" status.
8. **Check if the employee is already in Firestore (Leavers DB)**:
   - If found, no further action is needed.
   - If not found and the employee was onboarded (has Google account), add to Leavers Queue (Sheet) and Firestore (Leavers DB).
9. **Update the Deleted Profiles sheet row** with "Yes" for "Was Google account created?" and "Was added to leavers queue?" if applicable.

#### Flowchart

```mermaid
flowchart TD
    A[HiBob event: employee.deleted] --> B[Lookup employee in Firestore Onboarding DB]
    B -->|Not found| C[Log error & stop]
    B --> D[Fetch Google account by HiBob ID]
    D --> E[Create Jira issue for HR]
    E --> F[Notify Security team]
    F --> G[Add to Deleted Profiles sheet]
    G --> H[Check onboarding status in Firestore]
    H -->|Not onboarded| I[Update onboarding doc with 'DELETED HIBOB PROFILE']
    H -->|Onboarded| J[Check in Firestore Leavers DB]
    J -->|Found| K[No further action]
    J -->|Not found| L[Add to Leavers Queue & Firestore Leavers DB]
    L --> M[Update Deleted Profiles sheet row]
```

---

**Note:**  
- All lookups and updates are performed against Firestore as the source of truth, with Google Sheets kept in sync for reporting and operational visibility.


## Usage Forecast

Triggered once for every leaver and account deletion. 

## Change Log

- **2025-04-23 (Tariq)**: Changed the format of enqueued date to yyyy-mm-dd
- **2024-07-30 (Tariq)**: Changed filter when searching for user in leavers queue, now looking at deletion timestamp value as well.
- **2025-06-02 (Hector)**: Ported from Make to Deno, using `reverse-proxy` to receive new Hibob v2 event (v1 deprecated). 
