import { getCurrentCloudRunStageName } from "modules/get-current-cloud-run-stage-name/index.ts";
import { getImpersonatorSheetsClient, getImpersonatorGoogleClient } from "modules/cloud-run-impersonator/index.ts";
import { getHibobClient } from "modules/hibob/index.ts";
import packageJson from "../package.json" with { type: "json" };
import { employeeLeftWorkFlow } from "./workflows/employeeLeftWorkFlow.ts";
import { employeeDeletedWorkFlow } from "./workflows/employeeDeletedWorkFlow.ts";
import { getJiraClient, JiraClient } from "modules/jira/index.ts";
import { initializeFirestore } from "modules/firestore/index.ts";
import { getSlackClient } from "modules/slack/index.ts";

const currentStageName = getCurrentCloudRunStageName() as keyof typeof packageJson.stages;
export const isProd = currentStageName === "production";

//export const googleAccountDomain = isProd ? "ebury.com" : "test.eburypartners.com";
export const googleAccountDomain = "ebury.com";
export const securityEmailRecipient = isProd ? ["<EMAIL>"] : ["<EMAIL>"];
export const slackChannelId = isProd
  ? "C03GW0N6VPF" // #automation-monitoring
  : "C04NSJTBSFK"; // #automation-testing

// Firestore config:
export const leaversDatabaseName = isProd ? "leavers-idempotency-checks" : `leavers-idempotency-checks-staging`;
export const onboardingDatabaseName = isProd ? "onboarding-idempotency-checks" : `onboarding-idempotency-checks-staging`;
export const leaversDatabase = await initializeFirestore(leaversDatabaseName);
export const onboardingDatabase = await initializeFirestore(onboardingDatabaseName);

// Clients:
export const sheetsClient = getImpersonatorSheetsClient();
export const googleAdminClient = getImpersonatorGoogleClient();
export const hibobClient = getHibobClient();
export const slackClient = await getSlackClient();
const jiraSite = isProd ? JiraClient.SITES.FXSOLUTIONS : JiraClient.SITES.SANDBOX;
export const jiraClient = getJiraClient(jiraSite);

export const handlerHibobEventType = {
  "employee.left": employeeLeftWorkFlow,
  "employee.deleted": employeeDeletedWorkFlow,
};

// 'Leavers Queue' sheet details:
export const leaversSheetConfig = {
  leaversSpreadsheetId: "1zuSDR2BuAIa-61ArG81YgE4sBKqf-domTTXYEGxCekY",
  leaversSheetName: isProd ? "queue" : "test_queue",
  deletedProfilesSheetName: "Deleted Profiles",
  leaversHeaders: {
    emailHeader: "email",
    leavingDateHeader: "leaving-date",
    nameHeader: "name",
    departmentHeader: "department",
    siteHeader: "site",
    divisionHeader: "division",
    countryHeader: "country",
    queuedTimestampHeader: "queued timestamp",
  },
  deletedProfileHeaders: {
    hibobIdHeader: "hibob Id",
    startDateHeader: "start date",
    deletionDateHeader: "date of deletion",
    nameHeader: "name",
    siteHeader: "site",
    deletedByHeader: "deleted by",
    googleAccountCreatedHeader: "Was google account created?",
    addedToLeaversQueueHeader: "Was added to leavers queue?",
  }
};

// 'Onboarding Queue' sheet details:
export const onboardingSheetConfig = {
  onboardingSpreadsheetId: "1uF749zFiJNLrtbz_loiaoTqBy5H0tPDawLvacP972nY",
  onboardingSheetName: isProd ? "Queue" : "Test",
  onboardingHeaders: {
    processedTimestampHeader: "processed timestamp",
    newStarterEmailHeader: "newStarterEmailReceived",
    companyEmailHeader: "companyEmail",
    hibobEmailChangedHeader: "hibobEmailChanged",
    zendeskTicketIdHeader: "zendeskTicketId",
  }
};
