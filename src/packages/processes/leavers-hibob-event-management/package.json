{"name": "leavers-hibob-event-management", "description": "A scenario to make sure that a leaver exists in the leavers queue", "platform": "cloud-run", "status": "active", "runtime": "deno", "contributors": ["<EMAIL>", "<EMAIL>"], "relatedJiraIssues": ["BPA-1127", "BPA-2749"], "stages": {"staging": {"url": "https://console.cloud.google.com/run/detail/europe-west1/leavers-hibob-event-management-staging", "endpointUrl": "https://leavers-hibob-event-management-staging-cehm3wmena-ew.a.run.app"}, "production": {"url": "https://console.cloud.google.com/run/detail/europe-west1/leavers-hibob-event-management-production", "endpointUrl": "https://leavers-hibob-event-management-production-cehm3wmena-ew.a.run.app"}}, "deploymentOptions": {"memory": "2Gi", "cpu": "1", "port": 8080, "environmentVariables": {"AUTOMATION_ADMIN_IMPERSONATOR_CREDS": {"type": "secret", "secretName": {"staging": "AUTOMATION_ADMIN_IMPERSONATOR_CREDS", "production": "AUTOMATION_ADMIN_IMPERSONATOR_CREDS"}}, "GOOGLE_PASSWORD": {"type": "secret", "secretName": {"staging": "google-app-password", "production": "google-app-password"}}, "JIRA_CREDENTIALS": {"type": "secret", "secretName": {"staging": "jira-automation", "production": "jira-automation"}}, "SLACK_BPA_NOTIFIER_TOKEN": {"type": "secret", "secretName": {"staging": "SLACK_BPA_NOTIFIER_TOKEN", "production": "SLACK_BPA_NOTIFIER_TOKEN"}}, "FIRESTORE_SERVICE_ACCOUNT": {"type": "secret", "secretName": {"staging": "firestore-service-account", "production": "firestore-service-account"}}, "FIRESTORE_PREFER_REST": {"type": "variable", "value": {"local": "1", "staging": "1", "production": "1"}}, "HIBOB_SERVICE_ACCOUNT": {"type": "secret", "secretName": {"staging": "HiBob-service-account", "production": "HiBob-service-account"}}}}, "relatedProcesses": ["leavers-hibob-event-management"], "impact": [{"type": "risk", "kind": "breach", "description": "TO BE ESTIMATED: malicious actions by disgruntled employee", "costPerFailure": 1, "estimatedTimesPerYear": 1}], "stakeholders": ["<EMAIL>", "<EMAIL>"]}