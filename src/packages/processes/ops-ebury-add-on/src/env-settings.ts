import { ENVIRONMENT } from "./_ENVIRONMENT.ts"; //@ts-ignore this file is automatically generated by the build process after choosing the environment
export const testingMode: boolean = ENVIRONMENT === "testing";
export const ENV_ID: string = "*************";
export const GENERATE_QUERY = (accountNumber: string): string => {
  const UAT_QUERY = "WITH crr AS (SELECT * FROM (SELECT *, CAST(MAX(CAST(Record_Created_Date AS Int64)) OVER (PARTITION BY Id) AS String) AS max_change_date FROM data-ci.CRR.risk_rating_tracker) WHERE Record_Created_Date=max_change_date), acc AS (SELECT DISTINCT Account_Id,Account_Number,Account_Name,FX_Dealer_Name,Ebury_Country,Ebury_Office,Ebury_Entity,Status, FX_Became_Client,TF_Became_Client FROM ebury-business-intelligence.EBI_Live.dimaccount) SELECT Id, Account_Number AS `Account_Number`, Status, Account_Country__r_Name AS `Client_Country`, ShippingCountry AS `Domicile_Country`, FX_Destination_Funds_Rollup_CSV__c AS `Destionation_of_Funds`, FX_Source_Funds_Rollup_CSV__c AS `Source_of_Funds` , Legal_Entity_Type__c AS `Legal_Entity_Type`, UBO_Locations__c AS `UBO_Locations`, Special_Framework__c AS `Special_Compliance_Framework`, Company_Formed_Date__c AS `Date_of_Incorporation`, AML_Sector__c AS `AML_Sector`, coalesce(NAICS_Codes__c, NAICS_Code__c) AS `NAICs_Codes`, Complex_Legal_Structure__c AS `Complex_Legal_Structure`,New_Pep__c AS `PEP`, Sanctions__c as `Sanctions`, Negative_media__c as `Negative_Media`, Internal_matches__c as `Internal_Matches`, Other_matches__c as `Other_Matches`, Charity__c as `Charity`, Bearer_Shares__c as `Bearer_Shares`, score_pre_overrides AS `CRR_Before_overrides`, overrides AS `Override_Factor`, risk_score AS `CRR_final`, Compliance_Risk_Override__c AS `Compliance_Risk_Override_in_SF`, Account_Country__r_Name_risk AS `Client_Country_risk`, trading_address_risk AS `Domicile_country_risk`, origin_funds_max_risk AS `Origin_of_funds_max_risk`, dest_funds_max_risk AS `Destination_of_funds_max_risk`, Legal_Entity_Type__c_risk AS `Entity_Type_risk`, UBO_Locations__c_risk AS `UBO_Locations_max_risk`, Special_Framework__c_risk AS `SCF_risk`, Recently_Incorporated_risk AS `Date_of_incorp_risk`, Complex_Legal_Structure__c_risk AS `Complex_Structure_risk`, PEP_Sanctions_risk AS `Screening_status_risk`, coalesce(NAICS_risk, sector_risk) AS `Sector_risk`, country_pillar_risk AS `Country_fields_weighted_risk`, entity_pillar_risk AS `Entity_related_fields_weighted_risk`, sector_pillar_risk AS `Sector_weighted_risk`, risk_score_value_raw AS `Risk_Score_value_raw`, risk_score_value AS `CRR_value` FROM crr LEFT JOIN acc ON crr.Id=acc.Account_Id WHERE Account_Number='" + accountNumber + "';";
  const PROD_QUERY = 'WITH crr AS (SELECT * FROM (SELECT *, CAST(MAX(CAST(Record_Created_Date AS Int64)) OVER (PARTITION BY Id) AS String) AS max_change_date FROM `data-ci.CRR.risk_rating_tracker`) WHERE Record_Created_Date=max_change_date), acc AS (SELECT DISTINCT Account_Id, Account_Number, Account_Name, FX_Dealer_Name, Ebury_Country, Ebury_Office, Ebury_Entity, Status, FX_Became_Client, TF_Became_Client FROM ebury-business-intelligence.EBI_Live.dimaccount) SELECT Id, Account_Number AS `Account_Number`, Status, Account_Country__r_Name AS `Client_Country`, ShippingCountry AS `Domicile_Country`, FX_Destination_Funds_Rollup_CSV__c AS `Destionation_of_Funds`, FX_Source_Funds_Rollup_CSV__c AS `Source_of_Funds`, Legal_Entity_Type__c AS `Legal_Entity_Type`, UBO_Locations__c AS `UBO_Locations`, Special_Framework__c AS `Special_Compliance_Framework`, Company_Formed_Date__c AS `Date_of_Incorporation`, AML_Sector__c AS `AML_Sector`, coalesce(NAICS_Codes__c, NAICS_Code__c) AS `NAICs_Codes`, Complex_Legal_Structure__c AS `Complex_Legal_Structure`, New_Pep__c AS `PEP`, Sanctions__c AS `Sanctions`, Negative_media__c AS `Negative_Media`, Internal_matches__c AS `Internal_Matches`, Other_matches__c AS `Other_Matches`, Charity__c AS `Charity`, Bearer_Shares__c AS `Bearer_Shares`, Delivery_Channel__c AS `Delivery_Channel`, ProductList AS `Products`, score_pre_overrides AS `CRR_Before_overrides`, overrides AS `Override_Factor`, risk_score AS `CRR_final`, Compliance_Risk_Override__c AS `Compliance_Risk_Override_in_SF`, Account_Country__r_Name_risk AS `Client_Country_risk`, trading_address_risk AS `Domicile_country_risk`, origin_funds_max_risk AS `Origin_of_funds_max_risk`, dest_funds_max_risk AS `Destination_of_funds_max_risk`, Legal_Entity_Type__c_risk AS `Entity_Type_risk`, UBO_Locations__c_risk AS `UBO_Locations_max_risk`, SPV__c AS `SPV_Flag`, Special_Framework__c_risk AS `SCF_risk`, Recently_Incorporated_risk AS `Date_of_incorp_risk`, Complex_Legal_Structure__c_risk AS `Complex_Structure_risk`, PEP_Sanctions_risk AS `Screening_status_risk`, coalesce(NAICS_risk, sector_risk) AS `Sector_risk`, delivery_channel_risk AS `Delivery_Channel_risk`, product_risk AS `Product_risk`, country_pillar_risk AS `Country_fields_weighted_risk`, entity_pillar_risk AS `Entity_related_fields_weighted_risk`, sector_pillar_risk AS `Sector_weighted_risk`, risk_score_value_raw AS `Risk_Score_value_raw`, risk_score_value AS `CRR_value` FROM crr LEFT JOIN acc ON crr.Id=acc.Account_Id WHERE Account_Number="' + accountNumber + '"';
  return testingMode ? PROD_QUERY : UAT_QUERY;
};
