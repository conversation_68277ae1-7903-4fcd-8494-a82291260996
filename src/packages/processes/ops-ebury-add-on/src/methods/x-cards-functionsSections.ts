/**
 * @file Home Card sections that call the individual control cards
 * <AUTHOR> <<EMAIL>>
 */

import { createCardFolder } from "./c-app-onboardingFileFolders.ts";
import { createCardFolderRemediation } from "./c-app-remediationFileFolders.ts";
import { selectionTemplateCard } from "./c-letter-maker.ts";

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_ClientFileFolders(): any {
  const actionCreateFolderCard = CardService.newAction()
    .setFunctionName(createCardFolder.name);

  const buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCard);

  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders")
    .setContent("")
    .setButton(buttonEventFolder);

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_RemediationClientFileFolders(): any {
  const actionCreateFolderCardRemediation = CardService.newAction()
    .setFunctionName(createCardFolderRemediation.name);

  const buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCardRemediation);

  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders")
    .setContent("Remediation")
    .setButton(buttonEventFolder);

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_reassessmentFolders(): any {
  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders (Select a folder in Drive)")
    .setContent("Reassessment");

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_ipCheckPrompt(): any {
  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("IP Check (Select a folder in Drive)")
    .setContent("Onboarding & Reassessment")
    .setMultiline(true);

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */
export function functionSection_generatePDF(): any {
  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("CRR Breakdown PDF (Select a folder in Drive)")
    .setContent("Onboarding & Reassessment");

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

export function functionSection_fileRenaming(): any {
  const keyValueFolder = CardService.newKeyValue()
    .setTopLabel("File renaming (Select a file in Drive)")
    .setContent("Onboarding & Reassessment");

  const sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to Letter Maker
 *
 * @return {object} A card section that that holds the call to the Letter Maker
 */
export function functionSection_LetterMaker(): any {
  let actionCreateFolderCard = CardService.newAction()
    .setFunctionName(selectionTemplateCard.name);

  let buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCard);

  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Remediation Letter Maker")
    .setContent("Generate Letter")
    .setButton(buttonEventFolder);

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}
