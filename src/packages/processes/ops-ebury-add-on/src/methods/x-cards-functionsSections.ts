/**
 * @file Home Card sections that call the individual control cards
 * <AUTHOR> <<EMAIL>>
 */

import { createCardFolder } from "./c-app-onboardingFileFolders.ts";
import { createCardFolderRemediation } from "./c-app-remediationFileFolders.ts";
import { selectionTemplateCard } from "./c-letter-maker.ts";

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_ClientFileFolders(): any {
  let actionCreateFolderCard = CardService.newAction()
    .setFunctionName(createCardFolder.name);

  let buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCard);

  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders")
    .setContent("")
    .setButton(buttonEventFolder);

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_RemediationClientFileFolders(): any {
  let actionCreateFolderCardRemediation = CardService.newAction()
    .setFunctionName(createCardFolderRemediation.name);

  let buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCardRemediation);

  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders")
    .setContent("Remediation")
    .setButton(buttonEventFolder);

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_reassessmentFolders(): any {
  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Create Client File folders (Select a folder in Drive)")
    .setContent("Reassessment");

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */

export function functionSection_ipCheckPrompt(): any {
  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("IP Check (Select a folder in Drive)")
    .setContent("Onboarding & Reassessment")
    .setMultiline(true);

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to request Control - Folders
 *
 * @return {object} A card section that that holds the call to the Control - Folders
 */
export function functionSection_generatePDF(): any {
  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("CRR Breakdown PDF (Select a folder in Drive)")
    .setContent("Onboarding & Reassessment");

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

export function functionSection_fileRenaming(): any {
  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("File renaming (Select a file in Drive)")
    .setContent("Onboarding & Reassessment");

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}

/**
 * Creates Card section to Letter Maker
 *
 * @return {object} A card section that that holds the call to the Letter Maker
 */
export function functionSection_LetterMaker(): any {
  let actionCreateFolderCard = CardService.newAction()
    .setFunctionName(selectionTemplateCard.name);

  let buttonEventFolder = CardService.newTextButton()
    .setText("Launch")
    .setOnClickAction(actionCreateFolderCard);

  let keyValueFolder = CardService.newKeyValue()
    .setTopLabel("Remediation Letter Maker")
    .setContent("Generate Letter")
    .setButton(buttonEventFolder);

  let sectionFolderControl = CardService
    .newCardSection()
    .addWidget(keyValueFolder);

  return sectionFolderControl;
}
