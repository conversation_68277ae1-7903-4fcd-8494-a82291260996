import { createCardByConfig, getContactsSF, insertActionOnBigQuery } from "./x-card-utils.ts";

export function fileRenamingMainCard(selectedFileId: string) {
  const rootFolderRegEx = /[0-9]+ - [a-zA-Z0-9]+/;
  const accountOpeningRegEx = /\b[a-zA-Z0-9]+ - Account opening$/;
  const accountReassessmentRegEx = /\b[a-zA-Z0-9]+ - Reassessment - [A-Z][a-z]{2}[0-9]{2}$/;
  const clientSubFolders = ["2. NATURE AND PURPOSE OF BUSINESS", "6. ESCALATION/APPROVAL DOCUMENTATION", "1. CDD", "4. CORRESPONDENCE", "5. SOF/SOW", "7. ADDITIONAL DOCUMENTS - NOT REQUIRED FOR ACC. OPENING", "3. SCREENING"];
  const cddSubFolders = ["DIRECTORS ID+V", "BENEFICIAL OWNERS ID+V", "AUTHORISED SIGNATORIES ID+V"];
  const fileFoldersIterator = DriveApp.getFolderById(selectedFileId).getParents();
  if (!fileFoldersIterator.hasNext()) {
    return foldersBuildErrorCard("cannot find the parent folder");
  }
  const fileFolder = fileFoldersIterator.next();
  const fileFolderName = fileFolder.getName();
  const fileFolderId = fileFolder.getId();
  const user = Session.getEffectiveUser().getEmail();
  if (accountOpeningRegEx.test(fileFolderName) || accountReassessmentRegEx.test(fileFolderName)) {
    // User is inside Account Opening/Reassessment folder
    console.info("File renaming from inside Account Opening/Reassessment -> " + user);
    const data = {
      parameters: {
        folder: fileFolderId,
        fileId: selectedFileId,
        step: 0,
      },
    };
    return folderSelectionCard(data);
  }
  if (clientSubFolders.includes(fileFolderName.toUpperCase()) || cddSubFolders.includes(fileFolderName.toUpperCase())) {
    // The user is inside the folder (Scenario where he just want to rename the file)
    console.info("File renaming from inside final folder -> " + user);
    const parentFolder = fileFolder.getParents().next();
    const parentFolderId = parentFolder.getId();
    const clientFolder = DriveApp.getFolderById(parentFolderId).getParents().next();
    let clientFolderName = clientFolder.getName();
    let clientFolderId = clientFolder.getId();
    let clientName = null;
    let clientId = null;
    while (!rootFolderRegEx.test(clientFolderName) && DriveApp.getFolderById(clientFolderId).getParents().hasNext()) {
      const parentParentFolder = DriveApp.getFolderById(clientFolderId).getParents().next();
      clientFolderId = parentParentFolder.getId();
      clientFolderName = parentParentFolder.getName();
    }
    const clientFolderNameSegments = clientFolderName.split("-");
    if (clientFolderNameSegments[1]) {
      clientName = clientFolderNameSegments[1].trim();
      clientId = clientFolderNameSegments[0].trim();
    } else {
      let message = "The name of the client folder is not correct ([Client Number] - [Client Name]).";
      return foldersBuildErrorCard(message);
    }
    const e = {
      parameters: {
        fileId: selectedFileId,
        folderId: fileFolderId,
        clientName: clientName,
        step: 0,
        clientId: clientId,
      },
    };
    if (clientSubFolders.includes(fileFolderName.toUpperCase())) {
      return fileRenamingSwitch(e);
    } else {
      return fileRenamingIndividualNames(e);
    }
  } else if (rootFolderRegEx.test(fileFolderName)) {
    // The user is in the client root folder
    console.info("File renaming from inside client root folder -> " + user);
    const subFolders = DriveApp.getFolderById(fileFolderId).getFolders();
    if (subFolders.hasNext()) {
      const dropdown = [];
      while (subFolders.hasNext()) {
        const auxFolder = subFolders.next();
        dropdown.push([auxFolder.getName(), auxFolder.getId()]);
      }
      const config = {
        cardName: {
          cardName: "File Renaming",
          cardTitle: "Renaming",
        },
        dropdown: [{
          options: dropdown.sort(),
          title: "Where do you want to move the file?",
          fieldName: "folder_to_move",
          type: "radiobutton",
        }],
        button: [{
          buttonText: "Next",
          buttonFunction: folderSelectionCard.name,
          buttonParams: { fileId: selectedFileId, step: 1 },
        }],
        step: 1,
      };
      const selectionRootClientFolderCard = createCardByConfig(config);
      return selectionRootClientFolderCard.build();
    } else {
      const message = "Account Opening or Reassessment sub-folder is missing. Please review the client folder structure.";
      return foldersBuildErrorCard(message);
    }
  } else {
    const message = "The file selected is not inside a client’s folder or it’s sub-folders. Please drag the file there before initiating the add-on.";
    return foldersBuildErrorCard(message);
  }
}

export function foldersBuildErrorCard(message: string): any {
  const config = {
    cardName: {
      cardName: "File Renaming",
      cardTitle: "Renaming",
    },
    text: "<b>Error:</b> " + message,
  };
  const errorCard = createCardByConfig(config);
  return errorCard.build();
}

//Here we're reading the subfolders and presenting the list to the user to select the location to save the file
export function folderSelectionCard(e: any): any {
  console.log({ e });
  let folderId = null;
  let id = e.parameters.fileId;
  let step = parseInt(e.parameters.step + "") + 1;
  if (e.commonEventObject) {
    folderId = e.commonEventObject.formInputs.folder_to_move.stringInputs.value[0];
  } else {
    folderId = e.parameters.folder;
  }
  console.log({ folderId });
  const folder = DriveApp.getFolderById(folderId);
  let subFolders = folder.getFolders();
  console.log({ subFolders });
  if (subFolders.hasNext()) {
    let clientFolder = folder.getParents()?.next?.()?.getName?.();
    console.log({ clientFolder });
    if (clientFolder) {
      let clientName = clientFolder.split("-")[1].trim();
      let clientId = clientFolder.split("-")[0].trim();
      let dropdown = [];

      while (subFolders.hasNext()) {
        const auxFolder = subFolders.next();
        dropdown.push([auxFolder.getName(), auxFolder.getId()]);
      }

      let config = {
        cardName: {
          cardName: "File Renaming",
          cardTitle: "Renaming",
        },
        dropdown: [{
          options: dropdown.sort(),
          title: "Where do you want to move the file?",
          fieldName: "folder_to_move",
          type: "radiobutton",
        }],
        button: [{
          buttonText: "Next",
          buttonFunction: fileRenamingSwitch.name,
          buttonParams: { clientName: clientName, fileId: id, step: step, clientId: clientId },
        }],
        step: step,
      };

      let cardRenameFile = createCardByConfig(config);

      return cardRenameFile.build();
    } else {
      return errorTrimmingFolderNameCard();
    }
  } else {
    let message = "Sub-folders (1. CDD, 2. Nature and purpose of business, etc) are missing. Please review the client folder structure.";
    return foldersBuildErrorCard(message);
  }
}

export function errorTrimmingFolderNameCard() {
  console.info("ERROR: Trim Folder Name");
  let text = "Something went wrong getting the folders names, please, try again.\nIf the error persist, please contact with us.";

  const config = {
    cardName: {
      cardName: "File Renaming",
      cardTitle: "Renaming",
    },
    text: text,
  };

  let errorTrimmingNames = createCardByConfig(config);
  return errorTrimmingNames.build();
}

export function fileRenamingSwitch(e: any): any {
  let clientName = e.parameters.clientName;
  let clientId = e.parameters.clientId;
  let fileId = e.parameters.fileId;
  let step = e.parameters.step;
  let folderId = null;
  if (e.commonEventObject) {
    folderId = e.commonEventObject.formInputs.folder_to_move.stringInputs.value[0];
  } else {
    folderId = e.parameters.folderId;
  }

  let folderName = DriveApp.getFolderById(folderId).getName();

  switch (folderName.toUpperCase()) {
    case "2. NATURE AND PURPOSE OF BUSINESS":
      return fileRenamingSimpleCases(folderName, fileId, folderId, clientName, step);
    case "6. ESCALATION/APPROVAL DOCUMENTATION":
      return fileRenamingSimpleCases(folderName, fileId, folderId, clientName, step);
    case "1. CDD":
      return fileRenamingCDDFolder(fileId, folderId, clientName, step, clientId);
    case "4. CORRESPONDENCE":
      return fileRenamingCasesInput(folderName, fileId, folderId, clientName, step);
    case "5. SOF/SOW":
      return fileRenamingCasesInput(folderName, fileId, folderId, clientName, step);
    case "7. ADDITIONAL DOCUMENTS - NOT REQUIRED FOR ACC. OPENING":
      return fileRenamingCasesFullNameInput(fileId, folderId, step);
    case "3. SCREENING":
      const params = {
        parameters: {
          fileId: fileId,
          folderId: folderId,
          step: step,
          clientId: clientId,
        },
      };
      return fileRenamingIndividualNames(params);
    default:
      let message = "The folder selected doesn't match with any of the options. Please review the client folder structure.";
      return foldersBuildErrorCard(message);
  }
}

export function fileRenamingCDDRootFolder(e: any): any {
  let fileId = e.parameters.fileId;
  let folderId = e.parameters.folderId;
  let clientName = e.parameters.clientName;
  let step = parseInt(e.parameters.step + "");
  let cdd = ["Invoice", "Bill of Lading", "Bank Statement", "Memorandum of Association", "Certificate of Incorporation", "Constitution Deeds", "Bureau van Dijk report", "Fame report", "Orbis report", "Charities Commission Extract", "Companies House Report", "Companies House Report - UBO", "Companies House Report - Director", "Trading address ", "Registered address", "Salesforce", "Name change"];
  let buttons = [];

  if (step === 2) {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }];
  } else {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }, {
      buttonText: "Rename and Move",
      buttonFunction: fileMove.name,
      buttonParams: { fileId: fileId, folderId: folderId },
    }];
  }

  let dropdown = [];
  for (let i = 0; i < cdd.length; i++) {
    const name = clientName + " - " + cdd[i];
    dropdown.push([name, name]);
  }

  let config = {
    cardName: {
      cardName: "File Renaming",
      cardTitle: "Renaming",
    },
    dropdown: [{
      options: dropdown.sort(),
      title: "What is the name of the file?",
      fieldName: "file_name",
      type: "radiobutton",
    }],
    input: [{
      title: "Date (Mandatory)",
      fieldName: "optional_input",
    }],
    button: buttons,
    step: 5,
  };

  let cardRenameFileCDDRootFolder = createCardByConfig(config);

  return cardRenameFileCDDRootFolder.build();
}

export function cddSubFolderSelectionCard(e: any): any {
  let fileId = e.parameters.fileId;
  let folderId = e.parameters.folderId;
  let clientId = e.parameters.clientId;
  let step = parseInt(e.parameters.step + "");

  let subFolders = DriveApp.getFolderById(folderId).getFolders();

  if (subFolders.hasNext()) {
    let dropdown = [];

    while (subFolders.hasNext()) {
      const auxFolder = subFolders.next();
      dropdown.push([auxFolder.getName(), auxFolder.getId()]);
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      dropdown: [{
        options: dropdown.sort(),
        title: "Where do you want to move the file?",
        fieldName: "folder_to_move",
        type: "radiobutton",
      }],
      button: [{
        buttonText: "Next",
        buttonFunction: fileRenamingIndividualNames.name,
        buttonParams: { fileId: fileId, step: step, clientId: clientId },
      }],
      step: step,
    };

    let cardSelectCDDSubFolder = createCardByConfig(config);

    return cardSelectCDDSubFolder.build();
  } else {
    let message = "Sub-folders (Directors ID+V, Beneficial Owners ID+V, Authorised Signatories ID+V) are missing. Please review the CDD folder structure.";
    return foldersBuildErrorCard(message);
  }
}

export function fileRenamingCDDFolder(fileId: string, folderId: string, clientName: string, step: number, clientId: string): any {
  let config = {
    cardName: {
      cardName: "File Renaming",
      cardTitle: "Renaming",
    },
    clientName: clientName,
    text: "Do you want to move the file to the root or to any subfolder?",
    button: [{
      buttonText: "Root Folder",
      buttonFunction: fileRenamingCDDRootFolder.name,
      buttonParams: { fileId: fileId, folderId: folderId, clientName: clientName, step: (step + 1) },
    }, {
      buttonText: "Sub Folder",
      buttonFunction: cddSubFolderSelectionCard.name,
      buttonParams: { fileId: fileId, folderId: folderId, step: (step + 1), clientId: clientId },
    }],
    step: step,
  };

  let cardRenameFileCDD = createCardByConfig(config);

  return cardRenameFileCDD.build();
}

export function fileRenamingSimpleCases(folderName: string, fileId: string, folderId: string, clientName: string, step: number): any {
  let escalation = ["Checklist", "EDD Report", "Sales Cover Sheet"];
  let nature = ["Application Form", "Website", "About us", "Contact us", "Bank Statement", "Bill of Lading", "Invoice"];
  let cardRenameFileSimpleCases = null;
  step = step + 1;
  let buttons;

  if (step === 1) {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }];
  } else {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }, {
      buttonText: "Rename and Move",
      buttonFunction: fileMove.name,
      buttonParams: { fileId: fileId, folderId: folderId },
    }];
  }

  if (folderName === "2. Nature and purpose of business") {
    let dropdown = [];
    for (let i = 0; i < nature.length; i++) {
      const name = clientName + " - " + nature[i];
      dropdown.push([name, name]);
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      dropdown: [{
        options: dropdown.sort(),
        title: "What is the name of the file?",
        fieldName: "file_name",
        type: "radiobutton",
      }],
      button: buttons,
      step: 5,
    };

    cardRenameFileSimpleCases = createCardByConfig(config);
  } else {
    let dropdown = [];
    for (let i = 0; i < escalation.length; i++) {
      const name = clientName + " - " + escalation[i];
      dropdown.push([name, name]);
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      dropdown: [{
        options: dropdown.sort(),
        title: "What is the name of the file?",
        fieldName: "file_name",
        type: "radiobutton",
      }],
      button: buttons,
      step: 5,
    };

    cardRenameFileSimpleCases = createCardByConfig(config);
  }

  return cardRenameFileSimpleCases.build();
}

export function fileRenamingIndividualNames(e: any): any {
  let fileId = e.parameters.fileId;
  let folderId = null;
  if (e.commonEventObject) {
    folderId = e.commonEventObject.formInputs.folder_to_move.stringInputs.value[0];
  } else {
    folderId = e.parameters.folderId;
  }

  let step = parseInt(e.parameters.step + "") + 1;
  let clientId = e.parameters.clientId;

  let cardRenameUsingContacts = null;

  let screening = ["Comply Report", "Negative news Search", "CIFAS"];
  let directorsBeneficials = ["Proof of identity", "Proof of address", "Certification - Evidence of certifier", "Proof of Identity - Selfie", "Certification - Jumio Report", "Jumio ID Document", "Certification - F2F form", "POA - Trulio"];
  let authorited = directorsBeneficials.concat(["Avoka - confirmation link ", "Avoka - Email Address", "T&C - Email confirmation"]);

  let contactsNames = getContactsSF(clientId);
  let folderName = DriveApp.getFolderById(folderId).getName();

  if (contactsNames.length) {
    let buttons = [];
    let dropdowns = [];

    if (step === 1) {
      buttons = [{
        buttonText: "Rename",
        buttonFunction: fileRename.name,
        buttonParams: { fileId: fileId },
      }];
    } else {
      buttons = [{
        buttonText: "Rename",
        buttonFunction: fileRename.name,
        buttonParams: { fileId: fileId },
      }, {
        buttonText: "Rename and Move",
        buttonFunction: fileMove.name,
        buttonParams: { fileId: fileId, folderId: folderId },
      }];
    }

    let dropdownContacts = [];
    for (let i = 0; i < contactsNames.length; i++) {
      dropdownContacts.push([contactsNames[i], contactsNames[i]]);
    }

    switch (folderName) {
      case "Beneficial Owners ID+V":
      case "Directors ID+V":
        let dropdownBeneficials = [];
        for (let i = 0; i < directorsBeneficials.length; i++) {
          dropdownBeneficials.push([directorsBeneficials[i], directorsBeneficials[i]]);
        }
        dropdownBeneficials.sort();
        dropdownBeneficials.push(["Postident - Verification Result", "Postident - Verification Result"]);
        dropdownBeneficials.push(["Postident - Video Recording", "Postident - Video Recording"]);
        dropdownBeneficials.push(["Postident - Photo ID Front", "Postident - Photo ID Front"]);
        dropdownBeneficials.push(["Postident - Photo ID Back", "Postident - Photo ID Back"]);

        dropdowns = [{
          options: dropdownContacts.sort(),
          title: "What is the name of the contact?",
          fieldName: "contact_name",
          type: "radiobutton",
        }, {
          options: dropdownBeneficials,
          title: "What is the name of the file?",
          fieldName: "file_name",
          type: "radiobutton",
        }];
        break;
      case "Authorised Signatories ID+V":
        let dropdownAuthorised = [];
        for (let i = 0; i < authorited.length; i++) {
          dropdownAuthorised.push([authorited[i], authorited[i]]);
        }
        dropdownAuthorised.sort();
        dropdownAuthorised.push(["Postident - Verification Result", "Postident - Verification Result"]);
        dropdownAuthorised.push(["Postident - Video Recording", "Postident - Video Recording"]);
        dropdownAuthorised.push(["Postident - Photo ID Front", "Postident - Photo ID Front"]);
        dropdownAuthorised.push(["Postident - Photo ID Back", "Postident - Photo ID Back"]);

        dropdowns = [{
          options: dropdownContacts.sort(),
          title: "What is the name of the contact?",
          fieldName: "contact_name",
          type: "radiobutton",
        }, {
          options: dropdownAuthorised,
          title: "What is the name of the file?",
          fieldName: "file_name",
          type: "radiobutton",
        }];
        break;
      case "3. Screening":
        let dropdownScreening = [];
        for (let i = 0; i < screening.length; i++) {
          dropdownScreening.push([screening[i], screening[i]]);
        }

        dropdowns = [{
          options: dropdownContacts.sort(),
          title: "What is the name of the contact?",
          fieldName: "contact_name",
          type: "radiobutton",
        }, {
          options: dropdownScreening.sort(),
          title: "What is the name of the file?",
          fieldName: "file_name",
          type: "radiobutton",
        }];
        break;
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      text: "The file name is a combination of Individual Name (Option 1) and File Category (Option 2)",
      dropdown: dropdowns,
      button: buttons,
      step: 5,
    };

    cardRenameUsingContacts = createCardByConfig(config);

    return cardRenameUsingContacts.build();
  } else {
    let message = "There are no contacts associated with the account. Please review the account in Salesforce";
    return foldersBuildErrorCard(message);
  }
}

export function fileRenamingCasesInput(folderName: string, fileId: string, folderId: string, clientName: string, step: number): any {
  let cardRenameFileCasesInput = null;
  let correspondence = ["Email confirmation -", "Call back confirmation of"];
  let sofsow = ["Annual Accounts", "Management Accounts", "Financial Statements", "IES", "Modelo 200", "Liasse Fiscale", "Invoice", "Bill of Lading", "Bank Statement", "Email confirmation -"];
  let buttons = [];
  step = parseInt(step + "") + 1;

  if (step === 1) {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }];
  } else {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }, {
      buttonText: "Rename and Move",
      buttonFunction: fileMove.name,
      buttonParams: { fileId: fileId, folderId: folderId },
    }];
  }

  if (folderName === "4. Correspondence") {
    let dropdown = [];
    for (let i = 0; i < correspondence.length; i++) {
      const name = clientName + " - " + correspondence[i];
      dropdown.push([name, name]);
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      dropdown: [{
        options: dropdown,
        title: "What is the name of the file?",
        fieldName: "file_name",
        type: "radiobutton",
      }],
      input: [{
        title: "Subject (Mandatory)",
        fieldName: "optional_input",
      }],
      button: buttons,
      step: 5,
    };

    cardRenameFileCasesInput = createCardByConfig(config);
  } else {
    let dropdown = [];
    for (let i = 0; i < sofsow.length; i++) {
      const name = clientName + " - " + sofsow[i];
      dropdown.push([name, name]);
    }

    let config = {
      cardName: {
        cardName: "File Renaming",
        cardTitle: "Renaming",
      },
      dropdown: [{
        options: dropdown,
        title: "What is the name of the file?",
        fieldName: "file_name",
        type: "radiobutton",
      }],
      input: [{
        title: "Subject (Mandatory)",
        fieldName: "optional_input",
      }],
      button: buttons,
      step: 5,
    };

    cardRenameFileCasesInput = createCardByConfig(config);
  }

  return cardRenameFileCasesInput.build();
}

export function fileRenamingCasesFullNameInput(fileId: string, folderId: string, step: number): any {
  let buttons = [];
  step = parseInt(step + "") + 1;

  if (step === 1) {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }];
  } else {
    buttons = [{
      buttonText: "Rename",
      buttonFunction: fileRename.name,
      buttonParams: { fileId: fileId },
    }, {
      buttonText: "Rename and Move",
      buttonFunction: fileMove.name,
      buttonParams: { fileId: fileId, folderId: folderId },
    }];
  }

  let config = {
    cardName: {
      cardName: "File Renaming",
      cardTitle: "Renaming",
    },
    input: [{
      title: "File new name",
      fieldName: "optional_input",
    }],
    button: buttons,
    step: 5,
  };

  let cardRenameFileCasesInputFullName = createCardByConfig(config);

  return cardRenameFileCasesInputFullName.build();
}

export function fileRename(e: any): void {
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  insertActionOnBigQuery("File Renaming", "AUT06", "Success", startDate);
  let fileName = null;
  let fileId = e.parameters.fileId;
  if (e.commonEventObject.formInputs.file_name) {
    let file_name = e.commonEventObject.formInputs.file_name.stringInputs.value[0];
    if (e.commonEventObject.formInputs.contact_name) {
      let contact_name = e.commonEventObject.formInputs.contact_name.stringInputs.value[0];
      fileName = contact_name + " - " + file_name;
    } else {
      fileName = e.commonEventObject.formInputs.file_name.stringInputs.value[0];
    }
  }
  if (e.commonEventObject.formInputs.optional_input && fileName !== null) {
    let subject = e.commonEventObject.formInputs.optional_input.stringInputs.value[0];
    fileName = fileName + " " + subject;
  } else if (e.commonEventObject.formInputs.optional_input && fileName === null) {
    fileName = e.commonEventObject.formInputs.optional_input.stringInputs.value[0];
  }

  DriveApp.getFileById(fileId).setName(fileName);
}

export function fileMove(e: any): void {
  fileRename(e);
  let fileId = e.parameters.fileId;
  let folderId = e.parameters.folderId;
  DriveApp.getFileById(fileId).moveTo(DriveApp.getFolderById(folderId));
}
