/**
 * @file App - Create the Client File Folders
 * <AUTHOR>
 */

import { testingMode } from "../env-settings.ts";
import { buttonColor, rootFoldersConfig, rootFoldersConfigTest } from "./x-card-config.ts";
import { EburyLibraryClientFolder, insertActionOnBigQuery } from "./x-card-utils.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

/**
 * Creates a card (Folder App Home) to call the folder create library with inputs from the user or a different card (Folder App Complete) to show the results if it has been done
 *
 * @param {boolean} createdFolderStatus - Whether the folder structure requested has been created (true) or not (false)
 * @param {string} createdFolderLink - The URL of the folders that have been created by.  Displayed on the
 * @return {callback} Builds either Folder App Home card or Folder App Complete card
 *
 * @example
 *
 *     createCardFolder(true,'https://drive.google.com/drive/u/0/folders/1SJbZdrWriJ9kkxbcIMu0wRynZqMYAXJS')
 */

export function createCardFolderRemediation(): any {
  // ----------------------------------------------------------------------------------------------------------
  // Build elements used on both cards: Standard Home and PM Home
  // ----------------------------------------------------------------------------------------------------------

  let textExplanation = CardService.newTextParagraph()
    .setText("Enter Client Number and Client Name from Salesforce and select Create Folders");

  let textInputID = CardService.newTextInput()
    .setFieldName("clientId_input")
    .setTitle("Enter Client Number");

  let textInputName = CardService.newTextInput()
    .setFieldName("clientName_input")
    .setTitle("Enter Client Name");

  // ----------------------------------------------------------------------------------------------------------
  // Build elements used just by Standard Home and build Standard Home
  // ----------------------------------------------------------------------------------------------------------
  //Action to create Folders
  let actionRunFolder = CardService.newAction()
    .setFunctionName(createFoldersRemediation.name)
    .setLoadIndicator(CardService.LoadIndicator.SPINNER);

  let buttonCallStd = CardService.newTextButton()
    .setText("Create folders")
    .setOnClickAction(actionRunFolder)
    .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
    .setBackgroundColor(buttonColor);

  //Action to pop from Standard to PM if radio button selected
  let dropdownGroupStd = CardService.newSelectionInput()
    .setType(CardService.SelectionInputType.DROPDOWN)
    .setTitle("Where do you want to create the folders?")
    .setFieldName("remediation_folder");

  if (testingMode === true) {
    for (const key in rootFoldersConfigTest) {
      dropdownGroupStd.addItem(rootFoldersConfigTest[key]["label"], key, false);
    }
  } else {
    for (const key in rootFoldersConfig) {
      dropdownGroupStd.addItem(rootFoldersConfig[key]["label"], key, false);
    }
  }

  let textInputSectionStd = CardService.newCardSection()
    .addWidget(textExplanation)
    .addWidget(dropdownGroupStd)
    .addWidget(textInputID)
    .addWidget(textInputName)
    .addWidget(buttonCallStd);

  let cardFolderHomeRemediation = CardService.newCardBuilder()
    .setName("Folder Remediation Home")
    .setHeader(standardSectionHeader("Client File Folders", "Remediation"))
    .addSection(textInputSectionStd)
    .setFixedFooter(standardSectionFooter());

  // ----------------------------------------------------------------------------------------------------------
  // Return the three cards
  // ----------------------------------------------------------------------------------------------------------

  return cardFolderHomeRemediation.build();
}

/**
 * Creates Card "Folder App Complete"
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

export function createCardFolderCompleteRemediation(createdFolderStatus: any, createdFolderLink: string): any {
  //Action calls this function action => REPLACE THIS WITH A COMPLETION CARD
  let actionRebuild = CardService.newAction()
    .setFunctionName(createCardFolderRemediation.name)
    .setLoadIndicator(CardService.LoadIndicator.SPINNER);

  let buttonReset = CardService.newTextButton().setText("Start again").setOnClickAction(actionRebuild);

  let sectionFolderLocation = CardService.newTextParagraph().setText('<a href="' + createdFolderLink + '">Go to your new folders</a>');

  let textInputSection = CardService.newCardSection()
    .addWidget(sectionFolderLocation)
    .addWidget(buttonReset);

  let card = CardService.newCardBuilder()
    .setName("Folder App Complete")
    .setHeader(standardSectionHeader("Onboarding", "Create Client Folders"))
    .addSection(textInputSection)
    .setFixedFooter(standardSectionFooter());

  return card.build();
}

/**
 * Calls the folder library as part of an Action from Folder App Home card
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

export function createFoldersRemediation(e: any): any {
  //log details of the request
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const user = Session.getEffectiveUser().getEmail();
  console.info("Create Remediation Folders -> " + user);
  console.log(e.commonEventObject.formInputs.pm_root_folder);

  //
  if (
    e.commonEventObject.formInputs &&
    e.commonEventObject.formInputs.clientId_input &&
    e.commonEventObject.formInputs.clientName_input &&
    e.commonEventObject.formInputs.remediation_folder
  ) {
    //are all inputs present
    const clientId = e.commonEventObject.formInputs.clientId_input.stringInputs.value[0];
    const clientName = e.commonEventObject.formInputs.clientName_input.stringInputs.value[0];
    const remediationRootFolder = e.commonEventObject.formInputs.remediation_folder.stringInputs.value[0];
    const subFolderId = rootFoldersConfigTest[remediationRootFolder]["folderId"];
    const accountOpening = false;
    try {
      let folderReturns = EburyLibraryClientFolder.createSubFolderStructure(clientId, clientName, subFolderId, accountOpening);

      let folderLink = "https://drive.google.com/drive/u/0/folders/" + folderReturns["folderId"];

      if (folderReturns["errorValue"]) {
        insertActionOnBigQuery("Create Remediation Folders", "AUT05", "Success", startDate);
        //return a notification to say that it is complete
        return createCardFolderCompleteRemediation("true", folderLink);
      } else {
        //return a notification to say that the main folder requested already exists
        let actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("Folder already exists"),
          );
        return actionResponse.build();
      }
    } catch (err) {
      insertActionOnBigQuery("Create Remediation Folders", "AUT05", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
        );

      return actionResponse.build();
    }
  } else {
    //return a notification to say that one field or multiple user inputs are missing
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please enter a Client Number and Client Name"),
      );

    return actionResponse.build();
  }
}
