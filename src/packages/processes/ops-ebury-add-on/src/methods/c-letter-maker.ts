import { buildTemplate, createCardByConfig, insertActionOnBigQuery, integromatGrantAccess } from "./x-card-utils.ts";

const RFI_ID = "1LaDVDVjjw1S0fFIpj2KbsilQuxekjiFP8ghV4cfbYls";
const LETTER_1_ID = "1q2XZWhDhNDWiX26xtkZCZwrm8JmJt1X2uV4AftK5GAY";
const LETTERS_FOLDER = "1PVxiSUU_TGtY5ksUDWSpw0kBb65xNRTd";

export function selectionTemplateCard(): any {
  let config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    dropdown: [{
      options: [["RFI", "option_1"], ["Remediation Letter 108", "option_2"]],
      title: "What template do you want to complete?",
      fieldName: "template_to_fill",
    }],
    button: [{
      buttonText: "Next",
      buttonFunction: selectionTemplateLanguageCard.name,
      buttonParams: {},
    }],
  };

  let templateSelectionCard = createCardByConfig(config);
  return templateSelectionCard.build();
}

function searchLanguages(template: string, responseCode: string): any {
  let ss = SpreadsheetApp.openById("1W0DK2J1E-v1BJpNkI0zU0ckWrqX2m0bgoDCnq_tWW9g").getSheetByName("Letter templates");
  let lastRow = ss.getLastRow();
  let languages = [];

  if (responseCode == "200") {
    while (lastRow != 1) {
      const letterType = ss.getRange(lastRow, 2).getValue();
      const fileNameVariations = ss.getRange(lastRow, 9).getValue();
      const templateId = ss.getRange(lastRow, 12).getValue();
      if (template == "option_1") {
        if ((letterType == "RFI") && (fileNameVariations != "") && (templateId != "")) {
          const language = [ss.getRange(lastRow, 6).getValue(), fileNameVariations, templateId, letterType];
          languages.push(language);
        }
      } else {
        if ((letterType == "Letter 108") && (fileNameVariations != "") && (templateId != "")) {
          const language = [ss.getRange(lastRow, 6).getValue(), fileNameVariations, templateId, letterType];
          languages.push(language);
        }
      }
      lastRow = lastRow - 1;
    }

    return languages;
  } else {
    let text = "Something went wrong with the operation, please try again.";

    let config = {
      cardName: {
        cardName: "Letter Maker",
        cardTitle: "Letter Maker",
      },
      text: text,
    };

    let letterMakerErrorCard = createCardByConfig(config);

    return letterMakerErrorCard.build();
  }
}

export function selectionTemplateLanguageCard(e: any): any {
  let template = e.commonEventObject.formInputs.template_to_fill.stringInputs.value[0];

  //Grant access to shared drive
  let webhookUrl = "https://hook.integromat.com/jlyhyao1httg4j6tj42e7gm6xiwefwmy";
  let responseCode = integromatGrantAccess(webhookUrl);

  let languagesData = searchLanguages(template, responseCode);
  let languagesDropdown = [];

  for (let i = 0; i < languagesData.length; i++) {
    const language = [languagesData[i][0], languagesData[i][0]];
    languagesDropdown.push(language);
  }

  let config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    dropdown: [{
      options: languagesDropdown,
      title: "Choose the language of the letter.",
      fieldName: "letter_languaje",
    }],
    button: [{
      buttonText: "Next",
      buttonFunction: switchTemplates.name,
      buttonParams: { templatesData: languagesData.toString() },
    }],
  };

  let templateSelectionCard = createCardByConfig(config);
  return templateSelectionCard.build();
}

export function switchTemplates(e: any): any {
  let templateLanguaje = e.commonEventObject.formInputs.letter_languaje.stringInputs.value[0];
  let templatesDataString = e.parameters.templatesData;
  let templatesDataArray = templatesDataString.split(",");
  let templatesDataFormated = [];

  while (templatesDataArray.length >= 4) {
    const language = [templatesDataArray[0], templatesDataArray[1], templatesDataArray[2], templatesDataArray[3]];
    templatesDataFormated.push(language);
    templatesDataArray.splice(0, 4);
  }

  let templateId = null;
  let fileName = null;

  for (let i = 0; i < templatesDataFormated.length; i++) {
    if (templatesDataFormated[i][0] == templateLanguaje) {
      templateId = templatesDataFormated[i][2];
      fileName = templatesDataFormated[i][1];
    }
  }

  switch (templatesDataFormated[0][3]) {
    case "RFI":
      return letterRFICard(templateId, fileName);
    case "Letter 108":
      return remediationSolutionClient(templateId, fileName);
  }
}

function remediationSolutionClient(templateId: string, fileName: string): any {
  let config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    dropdown: [{
      options: [["Yes", "yes"], ["No", "no"]],
      title: "Solutions client?",
      fieldName: "solutions_client",
    }, {
      options: [["Yes", "yes"], ["No", "no"]],
      title: "Trade Finance client?",
      fieldName: "trade_finance",
    }, {
      options: [["Yes", "yes"], ["No", "no"]],
      title: "Downgraded client?",
      fieldName: "downgraded_client",
    }],
    button: [{
      buttonText: "Next",
      buttonFunction: letterSecondTemplateCard.name,
      buttonParams: { templateId: templateId, fileName: fileName },
    }],
  };

  let templateRemediationsolutions = createCardByConfig(config);
  return templateRemediationsolutions.build();
}

export function letterSecondTemplateCard(e: any): any {
  let solutions_client = e.commonEventObject.formInputs.solutions_client.stringInputs.value[0];
  let downgraded_client = e.commonEventObject.formInputs.downgraded_client.stringInputs.value[0];
  let trade_finance = e.commonEventObject.formInputs.trade_finance.stringInputs.value[0];
  let templateId = e.parameters.templateId;
  let fileName = e.parameters.fileName;

  let entities = ["Ebury Partners UK Limited", "Ebury Technology Limited", "Ebury Mass Payments Limited", "Ebury Partners Finance Limited", "Ebury Partners Markets Limited", "Ebury Partners Belgium N.V.", "Ebury Partners Switzerland AG", "Ebury Partners Australia PTY LTD"];
  let entitiesDropdown = [];

  for (let i = 0; i < entities.length; i++) {
    entitiesDropdown.push([entities[i], entities[i]]);
  }

  let inputs = [{
    title: "Account Name",
    fieldName: "account_name",
  }, {
    title: "Account Number",
    fieldName: "account_number",
  }];

  if (solutions_client === "yes") {
    const programManager = ["Abacus FX Limited", "AD Capital Markets Limited", "Alpine FX Limited", "Antonio Jose Pleguezuelo Witte", "AP FX Pty Limited", "apFX Limited", "ArcaPay Limited", "Axiom International Financial Services Limited", "Bespoke Treasury Services Limited", "Best Rate Global Limited", "Blue Ridge Exchange Limited", "Bluestone Currency Limited", "BMH Co PTY Limited", "BRF Payment Services Limited", "Bridge Exchange PTY Limited", "Bridge Place Limited", "BSFX Limited", "Caledon Partners SL", "Capex Currency Limited", "Capitex FX Limited", "Capsun Limited ", "Carinta Donna Mannarelli", "Cawdor FX Limited", "CB3 Global Payments PTY Limited", "Clear Capital Exchange Limited", "COE Solutions Limited", "CommSense FX Limited", "Construct Talent PTY Limited", "Cremello Currency Management Limited", "Currency Farm Limited", "Zapptek", "Exchange Capital Partners Limited", "Estuary FX Limited", "Farrell Fintech Limited", "FCI Currency Limited", "FinAdin Zrt.", "Finatrex Limited", "Fintuitive Limited", "Fiscal FX Limited", "Fortune Forex Pty Limited", "Future of Fintech SRL", "Fuzion Technologies Inc", "FX Prime AG", "Galcar Limited", "GeoPay PTY Limited", "Global Brands Guernsey Limited ", "Global QFX Inc", "HampdenFX Limited", "Hawk FX Limited", "Horizon Trading Partners Limited ", "Imperial Currencies Limited", "ImpexPay Limited", "International Payments Tech        ", "International Secure Payments Limited", "Jackson Swiss Trading Limited", "JBE Partners Limited", "JBH Partners Limited UK", "JBH Partners PTY Limited ", "JCF Transaction Services Limited", "JetFX PTY Limited", "KBS Financial Services Limited", "Knightsbridge International FX Limited", "Kristian Wangvisutkul", "Leap FXG Limited", "Lighthouse FX Limited", "Linear Markets Limited", "Llink Service Limited", "Lowcostfx Limited ", "Magna Financial Limited ", "Manor House Foreign Exchange Limited", "Mansion Currency Limited", "Marineforex Limited", "Mayfair FX Limited", "Mbfx Global Limited", "Medlock & Thames Limited", "Merchant Partners Group Limited", "Meridian Currency Limited", "Monetae Group Limited", "Money Mechanix Limited", "Monijump Limited", "Monteith Partners Europe Limited", "NIFX Limited", "Obsidian Partners Foreign Exchange Limited", "Ocean Financial Solutions Limited", "Optimal Currency Limited", "Petricore Partners Limited", "Pinnacle Global Payment Solutions Ltd", "Prime Cap Payments Limited", "R2D2 HK Limited", "Reb8 Limited", "Red Consulting SAS", "RMFX Limited", "Roche Fintech", "Rutland Foreign Exchange Limited", "Seven Oaks Capital PTY Limited", "Societa Financial Limited", "Sovereign International (UK) Limited", "Spartan FX Limited", "Spectrum FX Limited", "SR Capital Group Limited", "Sterling Black Limited", "Sugarcane Capital Limited", "Sunnier Climate Properties SL ", "Swift Currencies Limited", "Synergy Partnership Solutions Limited", "Synergy Payment Solutions Limited", "Tait Capital UK Limited", "Tat FX Pty Limited", "The Berkshire Exchange Limited", "The Cable International Limited", "Trade Wind Foreign Exchange Limited", "Transfer Connex Limited", "Universal Partners FX Limited", "VerityCorp Limited", "Victoriam Capital Limited", "Werz Limited", "Willu FX Limited ", "Wundr Group Limited", "Xchange Capital Limited", "Zapptek", "Finpay FX PTY Limited", "London Suisse Inc.", "Moneta Global Limited", "NTEC FX Limited", "Sterling Exchange Limited", "Sw19 Capital Limited", "Swift Fox Limited", "Trade Accelerator Limited", "Complete Currency Limited", "Currencies 4 you Limited", "Effective FX Limited", "Finomie SAS", "FX Alba Limited", "Key Currency Limited", "Millbank FX Limited", "Adamo FX Limited", "FXOC Limited", "Lotus FX Limited", "Matthew William Carr", "MTXpress Limited", "Onidexi PTY Limited", "Orange FX UK Limited", "Richardson International Currency Exchange Inc", "The Currency Garden Limited", "World Pay Solutions Limited", "CurrencyFlo Limited", "Anthony and Bronagh Farrell", "55East Services Limited", "Abacus Financial Services Limited", "Andevour Limited", "Asett Capital Limited", "Ashentree Capital Limited", "Assure FX Limited", "Audere Solutions Limited", "Avennza Limited", "Axis IPC Limited", "BCR Global Limited", "Bespoke FX Limited", "Black Pearl Securities Limited", "Blackstone Wealth Holdings Limited (BSW Exchange)", "Bluebear FX Limited", "Caulfx Limited", "Choice Money Limited", "Concorde Currency Capital Limited", "Currency Portal Limited", "Currency Sphere Limited", "Currencytransfer Limited", "Daniel Stewart and Company PLC", "Deaglo Partners LLC", "Divisa UK Limited", "Duggan Asset Management Limited", "Electus Financial Limited", "Engfin Limited", "Enhance Group Limited", "EQI Bank", "Equiti Capital UK Limited", "Fintecom Payment KFT", "FirstForex Limited", "Flo-Fx Limited", "Foreign Payments Limited", "Fortiori Consult Thomas Kottmann", "Fulcrum Private Clients Limited", "FX Paymaster Limited", "Garton Global Payments Limited", "Go Fx Limited", "Horizon Currency Limited", "IBANFX Limited", "Imperium Foreign Exchange Limited", "International Bank Exchange Limited", "Kevin Gyateng", "Knightstone Group Limited", "Liquid Complete Limited", "Lowcost Moneytransfer Limited", "M.D Adamson and Co LLP", "Mayfair Capital", "MI-FX", "MPC Treasury Solutions Limited", "Numex FX Limited", "Oakhill Paramount Limited", "Orsa Saiwai Forex Limited", "OSMO Partners LLP", "Pagos Corporativos Y Privados (C&P Payments)", "Pecunia Treasury and Finance BV", "Peregrine and Black Limited", "Phoinix Limited", "Power 4X", "PTM Capital Limited", "Pukaki Limited", "Purpose", "Quaife.Net Limited", "Salamanca London", "Seren Capital Partners Limited", "Squared Financial Services Limited", "Statefx Limited", "Stormfast Trading Limited", "Strategic Swiss Advisors SARL", "Tailored FX", "Tailored FX", "Talbot FX LLP", "TGR WS Limited", "Travel Online Group Limited", "TW Currency Exchange Limited", "Valuta Specialist BV", "Vestex Global Limited", "Vorto Trading Limited", "Monteith Partners LLP"];

    const solutions_client_inputs = {
      title: "Program Manager",
      fieldName: "program_manager",
      suggestions: programManager,
    };
    inputs.push(solutions_client_inputs);
  }

  let config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    dropdown: [{
      options: entitiesDropdown.sort(),
      title: "Select Ebury Entity",
      fieldName: "ebury_entity",
      type: "dropdown",
    }],
    input: inputs,
    datepciker: {},
    button: [{
      buttonText: "Generate",
      buttonFunction: collectDataAndShowModal.name,
      buttonParams: { solutionsClient: solutions_client, downgradedClient: downgraded_client, tradeFinance: trade_finance, templateId: templateId, fileName: fileName },
    }],
  };
  let letterMakerInputsCard = createCardByConfig(config);
  return letterMakerInputsCard.build();
}

function letterRFICard(templateId: string, fileName: string): any {
  let config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    input: [{
      title: "Account Name",
      fieldName: "account_name",
    }, {
      title: "Account Number",
      fieldName: "account_number",
    }, {
      title: "Account Adress line 1",
      fieldName: "address_line1",
    }, {
      title: "Account Address line 2",
      fieldName: "address_line2",
    }, {
      title: "Postcode",
      fieldName: "postcode",
    }, {
      title: "Salutation",
      fieldName: "salutation",
    }, {
      title: "Full Name",
      fieldName: "full_name",
    }],
    datepciker: {},
    button: [{
      buttonText: "Generate",
      buttonFunction: collectDataAndShowModal.name,
      buttonParams: { templateId: templateId, fileName: fileName },
    }],
  };
  let letterMakerInputsCard = createCardByConfig(config);
  return letterMakerInputsCard.build();
}

export function collectDataAndShowModal(e: any): any {
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  try {
    // Inputs
    let accountName = e.commonEventObject.formInputs.account_name.stringInputs.value[0];
    let accountNumber = e.commonEventObject.formInputs.account_number.stringInputs.value[0];
    let date = new Date(e.commonEventObject.formInputs.date_field.dateInput.msSinceEpoch);
    let dateFormated = date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear();
    let templateId = e.parameters.templateId;
    let fileName = e.parameters.fileName;

    //Params
    let options = null;
    if (e.parameters.solutionsClient && e.parameters.downgradedClient && e.parameters.tradeFinance) {
      options = {
        solutionsClient: e.parameters.solutionsClient,
        downgradedClient: e.parameters.downgradedClient,
        tradeFinance: e.parameters.tradeFinance,
      };
    }

    let addressLine1 = null;
    let addressLine2 = null;
    let postcode = null;
    let salutation = null;
    let fullName = null;
    let eburyEntity = null;
    let fields = {};

    if (e.commonEventObject.formInputs.ebury_entity) {
      const user = Session.getEffectiveUser().getEmail();
      console.info("Letter Maker 60 Days -> " + user);

      eburyEntity = e.commonEventObject.formInputs.ebury_entity.stringInputs.value[0];

      fields = {
        accountNumber: {
          field: "{Account Number}",
          value: accountNumber,
        },
        accountName: {
          field: "{Account Name}",
          value: accountName,
        },
        date: {
          field: "{Date}",
          value: dateFormated,
        },
        eburyEntity: {
          field: "{Ebury Entity}",
          value: eburyEntity,
        },
      };

      if (e.commonEventObject.formInputs.program_manager) {
        const program_manager = e.commonEventObject.formInputs.program_manager.stringInputs.value[0];
        fields[program_manager] = {
          field: "{Program Manager}",
          value: program_manager,
        };
      }
    } else {
      const user = Session.getEffectiveUser().getEmail();
      console.info("Letter Maker RFI -> " + user);

      addressLine1 = e.commonEventObject.formInputs.address_line1.stringInputs.value[0];
      addressLine2 = e.commonEventObject.formInputs.address_line2.stringInputs.value[0];
      postcode = e.commonEventObject.formInputs.postcode.stringInputs.value[0];
      salutation = e.commonEventObject.formInputs.salutation.stringInputs.value[0];
      fullName = e.commonEventObject.formInputs.full_name.stringInputs.value[0];

      fields = {
        accountNumber: {
          field: "{Account Number}",
          value: accountNumber,
        },
        accountName: {
          field: "{Account Name}",
          value: accountName,
        },
        addressLine1: {
          field: "{Account Address line 1}",
          value: addressLine1,
        },
        addressLine2: {
          field: "{Account Address line 2}",
          value: addressLine2,
        },
        postcode: {
          field: "{Postcode}",
          value: postcode,
        },
        date: {
          field: "{Date}",
          value: dateFormated,
        },
        salutation: {
          field: "{Salutation}",
          value: salutation,
        },
        fullName: {
          field: "{Full Name}",
          value: fullName,
        },
      };
    }

    //Fill the template and save it
    let folderId = LETTERS_FOLDER;
    let letterId = buildTemplate(fields, templateId, folderId, options, fileName);

    insertActionOnBigQuery("Letter Maker", "AUT07", "Success", startDate);
    return completedProccessCard(folderId, letterId);
  } catch (e) {
    insertActionOnBigQuery("Letter Maker", "AUT07", "Error", startDate);
    console.info("ERROR: The user didn't fill all the fields. " + e);
    return errorNotAllFieldsFilledCard();
  }
}

function completedProccessCard(folderId: string, letterId: string): any {
  let text = 'Letter created. <a href="' + DriveApp.getFolderById(folderId).getUrl() + '">Go to folder</a>\n<br>You can check it here:\n<br><a href="' + DriveApp.getFileById(letterId).getUrl() + '">Go to ' + DriveApp.getFileById(letterId).getName() + "</a>";

  const config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    text: text,
  };

  let letterMakerCreated = createCardByConfig(config);
  return letterMakerCreated.build();
}

function errorNotAllFieldsFilledCard(): any {
  let text = "You must fill all the fields, please go back and do it again filling all the fields.";

  const config = {
    cardName: {
      cardName: "Letter Maker",
      cardTitle: "Letter Maker",
    },
    text: text,
  };

  let letterMakerUnfilledFields = createCardByConfig(config);
  return letterMakerUnfilledFields.build();
}
