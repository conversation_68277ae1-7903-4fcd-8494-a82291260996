/**
 * @file App - Run an IP Check
 * <AUTHOR>
 */

import { testingMode } from "../env-settings.ts";
import { buttonColor, driveURL, ipCheckConfig } from "./x-card-config.ts";
import { exportSpreadPdf, identifyFileRevisions, insertActionOnBigQuery, integromatGrantAccess } from "./x-card-utils.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

/**
 * Creates the IP Check home card that enables user to enter an IP address to check it
 *
 * @param {string} accountId - Account Id of the target client folder.
 * @param {string} accountName - ClientName of the target client folder.
 * @param {array} logFolderArr - An array of objects with each object representing a potential output log location
 *
 * @return {callback} Builds IP Check App Home card
 */

export function createIpCheckCardSelect(accountId: string, accountName: string, logFolderArr: any[]): any {
  let logFolderPresent: boolean = false;
  let folderLocationSection;
  // ----------------------------------------------------------------------------------------------------------
  // Define behaviour based on number of potential log output locations
  // ----------------------------------------------------------------------------------------------------------

  // No log folders, return a message telling the user//Grant access to shared drive
  let webhookUrl = "https://hook.integromat.com/jlyhyao1httg4j6tj42e7gm6xiwefwmy";
  let responseCode = integromatGrantAccess(webhookUrl);
  if (logFolderArr.length === 0) {
    const targetLogLocation = 'Logging folder \n<b>"' +
      ipCheckConfig.outputLoggingFldrName +
      '"</b>\nis missing.\n\n' +
      "Please review the User Guide for help.";

    const folderCreateText = CardService.newTextParagraph()
      .setText(targetLogLocation);
    folderLocationSection = CardService.newCardSection()
      .addWidget(folderCreateText);

    logFolderPresent = false;
  } //One possible log folder, log to it
  else if (logFolderArr.length === 1) {
    const logFolderObj = logFolderArr[0];

    const targetLogLocation = 'Your control log file will be stored in: \n<a href="' +
      driveURL +
      logFolderObj.logFolderId +
      '">' +
      logFolderObj.logFolderParentName +
      " > " +
      ipCheckConfig.outputLoggingFldrName +
      "</a>";

    const folderCreateText = CardService.newTextParagraph()
      .setText(targetLogLocation);
    folderLocationSection = CardService.newCardSection()
      .addWidget(folderCreateText)
      .addWidget(createIpButton(accountId, accountName, logFolderObj.logFolderId));

    logFolderPresent = true;
  } //Multiple log folders, give the user options
  else {
    //If more than one option, loop through them and let the user choose
    const targetLogLocation = '<b>Which folder do you want to store your evidence in?</b>\nIt will be stored in the sub-folder "' +
      ipCheckConfig.outputLoggingFldrName + '"';
    const folderCreateText = CardService.newTextParagraph()
      .setText(targetLogLocation);

    folderLocationSection = CardService.newCardSection()
      .addWidget(folderCreateText);
    for (let i = 0; i < logFolderArr.length; i++) {
      const logFolderObj = logFolderArr[i];

      const targetControl = '<a href="' +
        driveURL +
        logFolderObj.logFolderId +
        '">' +
        logFolderObj.logFolderParentName +
        "</a>";
      const textKeyValue = CardService.newKeyValue()
        .setContent(targetControl)
        .setButton(createIpButton(accountId, accountName, logFolderObj.logFolderId));

      folderLocationSection.addWidget(textKeyValue);
    }

    logFolderPresent = true;
  }

  // ----------------------------------------------------------------------------------------------------------
  // IP input
  // ----------------------------------------------------------------------------------------------------------
  let textExplanation = CardService.newTextParagraph()
    .setText("Enter the IP Address (v4 or v6) you have extracted from Avoka:");

  let textInputIP = CardService.newTextInput()
    .setFieldName("ip_input")
    .setTitle("Enter IP Address e.g. 127.0.0.1");

  let textInputSectionStd = CardService.newCardSection()
    .addWidget(textExplanation)
    .addWidget(textInputIP);

  // ----------------------------------------------------------------------------------------------------------
  // Construct the card
  // ----------------------------------------------------------------------------------------------------------
  let cardIpHome;
  //if there is no log folder do not provide a button to run
  if (logFolderPresent) {
    cardIpHome = CardService.newCardBuilder()
      .setName("IP Check App Home")
      .setHeader(standardSectionHeader("BAU Onboarding", "IP Check"))
      .addSection(textInputSectionStd)
      .addSection(folderLocationSection)
      .setFixedFooter(standardSectionFooter());
  } else {
    cardIpHome = CardService.newCardBuilder()
      .setName("IP Check App Home")
      .setHeader(standardSectionHeader("BAU Onboarding", "IP Check"))
      .addSection(folderLocationSection)
      .setFixedFooter(standardSectionFooter());
  }

  // ----------------------------------------------------------------------------------------------------------
  // Return the card
  // ----------------------------------------------------------------------------------------------------------

  return cardIpHome.build();
}

/**
 * Checks the IP against the watchlist
 *
 * @param {object} e - Calling object holding parameters sent from the button
 *
 * @return {card} Response to the screen if error or card complete
 */

export function checkIpAddressSelect(e: any): any {
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const user = Session.getEffectiveUser().getEmail();
  let ipDbId: string;
  let ipDbSheets: string[];
  let ipDb: GoogleAppsScript.Spreadsheet.Spreadsheet;
  let ipDbIdVersion: {
    version: string;
    versionDate: Date;
  };

  console.info("IP Check -> " + user);
  //get config details ipDbId (the IP Address Watchlist), IpLogId (the global Control Log), ipDBSheets (the sheets within the IP Address Watchlist to check)
  switch (testingMode) {
    case true:
      ipDbId = ipCheckConfig["mode_test"]["ipDbId"];
      ipDbSheets = ipCheckConfig["mode_test"]["srcSheetNames"];
      break;
    case false:
      ipDbId = ipCheckConfig["mode_prod"]["ipDbId"];
      ipDbSheets = ipCheckConfig["mode_prod"]["srcSheetNames"];
      break;
  }

  //read parameter inputs
  const accountId = e.parameters.accountId;
  const accountName = e.parameters.accountName;
  const logFolderId = e.parameters.logFolderId;

  //Run the control

  //are the form inputs actually present?
  if (
    e.commonEventObject.formInputs &&
    e.commonEventObject.formInputs.ip_input
  ) {
    //validate that the IP address entered is valid IP
    const ipAddress = e.commonEventObject.formInputs.ip_input.stringInputs.value[0].trim();
    // @ts-ignore this is a library that is not yet typed
    const isIp = eburylibraryvalidate.validateInputs(ipAddress, "ip");

    //identify the revision of the IP Address Watchlist being tested
    try {
      //test to see if you have write access to the IP Address Watchlist and Global Log
      ipDb = SpreadsheetApp.openById(ipDbId);
      ipDbIdVersion = identifyFileRevisions(ipDbId);
    } catch (err) {
      insertActionOnBigQuery("IP Check", "AUT02", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the the IP Address Watchlist or Log"),
        );

      return actionResponse.build();
    }

    //identify the date time of the run
    const date = new Date();
    const dateUTC = date.toUTCString();
    if (isIp === false) {
      insertActionOnBigQuery("IP Check", "AUT02", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("IP address is incorrectly formatted"),
        );
      return actionResponse.build();
    } else {
      try {
        //Search through all the sheet names to see if there

        //Default is not found
        let found = false;
        let foundResult = [];
        let foundMessage = ipCheckConfig["ipNotFound"];

        for (let j = 0; j < ipDbSheets.length && found === false; j++) {
          const targetData = ipDb.getSheetByName(ipDbSheets[j])
            .getDataRange()
            .getValues();

          for (let k = 1; k < targetData.length && found === false; k++) {
            //Ignore header row (k=1), found if IP Address matches and Address is valid
            if (targetData[k][0] === ipAddress && targetData[k][3] === true) {
              //found
              found = true;
              foundResult = targetData[k];
              foundMessage = ipCheckConfig["ipFound"];
            }
          }
        }

        const logDetails = {
          "Control Performer": Session.getActiveUser(),
          "Control Execution Date": dateUTC,
          "Tested Account ID": accountId,
          "Tested Account Name": accountName,
          "Tested IP Address": ipAddress,
          "Result": foundMessage,
          "Result Found IP": foundResult[0],
          "Result Additional Information": foundResult[1],
          "Watchlist File ID": ipDbId,
          "Watchlist Version": ipDbIdVersion["version"],
          "Watchlist Version Date": ipDbIdVersion["versionDate"].toUTCString(),
        };

        //create a log of a single control run
        const tempLogSpreadsheet = SpreadsheetApp.create("IP Check Log", 30, 2);
        const tempLogSpreadsheetId = tempLogSpreadsheet.getId();
        const activeSheet = tempLogSpreadsheet.getActiveSheet();
        let i = 1;
        for (const [key, value] of Object.entries(logDetails)) {
          activeSheet.getRange("A" + i).setValue(key);
          activeSheet.getRange("B" + i).setValue(value);
          i++;
        }
        //autoresize the columns so the pdf does not have cut off date
        for (let j = 1; j <= activeSheet.getMaxColumns(); j++) {
          activeSheet.autoResizeColumn(j);
        }
        //force flush so that data is present for the pdf
        SpreadsheetApp.flush();

        //Output a pdf version of the log for storing
        let ipDbPdf = "IP Check - " + accountName + " - IP Address Watchlist - " + dateUTC;
        let ipDbPdfUrl = exportSpreadPdf(logFolderId, ipDbId, ipDbPdf);
        let ipLogPdf = "IP Check - " + accountName + " - Control Log - " + dateUTC;
        let ipLogPdfDtl = exportSpreadPdf(logFolderId, tempLogSpreadsheetId, ipLogPdf);

        //return a complete card
        let createdLogFolderLink = "https://drive.google.com/drive/u/0/folders/" + logFolderId;
        let createdLogFileLink = "https://drive.google.com/file/d/" + ipLogPdfDtl["pdfFileId"] + "/view?usp=drivesdk";
        insertActionOnBigQuery("IP Check", "AUT02", "Success", startDate);
        return createCardIpCheckComplete(foundMessage, createdLogFileLink, createdLogFolderLink);
      } catch (err) {
        insertActionOnBigQuery("IP Check", "AUT02", "Error", startDate);
        //send a notification to the card
        let actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("ERROR - check sheet names and structure"),
          );

        return actionResponse.build();
      }
    }
  } else {
    //One of the two inputs is empty
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please enter an IP address"),
      );

    return actionResponse.build();
  }
}

/**
 * Returns a card if IP Check is complete with details of the log
 *
 * @param {string} ipCheckResult - The result of the control (e.g. Pass or Fail)
 * @param {string} createdLogUrl - The URL to the control log
 * @param {string} createdLogFolderUrl - The URL to the folder holding the control log
 *
 * @return {card} The card showing completion
 */

function createCardIpCheckComplete(ipCheckResult: string, createdLogUrl: string, createdLogFolderUrl: string): any {
  let sectionResult = CardService.newTextParagraph().setText("Result: \n<b>" + ipCheckResult + "</b>");
  let sectionLogLocation = CardService.newTextParagraph().setText('<a href="' + createdLogFolderUrl + '">Log folder</a>');
  let sectionLog = CardService.newTextParagraph().setText('<a href="' + createdLogUrl + '">Log file</a>');

  let textInputSection = CardService.newCardSection()
    .addWidget(sectionResult)
    .addWidget(sectionLogLocation)
    .addWidget(sectionLog);

  let card = CardService.newCardBuilder()
    .setName("IP Check Complete")
    .setHeader(standardSectionHeader("BAU Onboarding", "IP Check"))
    .addSection(textInputSection)
    .setFixedFooter(standardSectionFooter());

  return card.build();
}

/**
 * Creates a card button to call the IP Check
 *
 * @param {string} accountId - The acount ID of the IP being checked
 * @param {string} accountName - The accout name of the IP being checked
 * @param {string} logFolderId - The ID of the folder that will be logged to
 *
 * @return {button} Card service button
 */

function createIpButton(accountId: string, accountName: string, logFolderId: string): any {
  let actionRunIpCheck = CardService.newAction()
    .setFunctionName(checkIpAddressSelect.name)
    .setLoadIndicator(CardService.LoadIndicator.SPINNER)
    .setParameters({ accountId: accountId, accountName: accountName, logFolderId: logFolderId });

  return CardService.newTextButton()
    .setText("Check IP")
    .setOnClickAction(actionRunIpCheck)
    .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
    .setBackgroundColor(buttonColor);
}
