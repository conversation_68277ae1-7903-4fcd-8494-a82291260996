/**
 * @file App - Create the Client File Folders
 * <AUTHOR>
 */

import { testingMode } from "../env-settings.ts";
import { buttonColor } from "./x-card-config.ts";
import { EburyLibraryClientFolder, insertActionOnBigQuery } from "./x-card-utils.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

/**
 * Creates a card (Folder App Home) to call the folder create library with inputs from the user or a different card (Folder App Complete) to show the results if it has been done
 *
 * @param {boolean} createdFolderStatus - Whether the folder structure requested has been created (true) or not (false)
 * @param {string} createdFolderLink - The URL of the folders that have been created by.  Displayed on the
 * @return {callback} Builds either Folder App Home card or Folder App Complete card
 *
 * @example
 *
 *     createCardFolder(true,'https://drive.google.com/drive/u/0/folders/1SJbZdrWriJ9kkxbcIMu0wRynZqMYAXJS')
 */

export function createCardFolder(): any {
  try {
    // ----------------------------------------------------------------------------------------------------------
    // Build elements used on both cards: Standard Home and PM Home
    // ----------------------------------------------------------------------------------------------------------

    let textExplanation = CardService.newTextParagraph()
      .setText("Enter Client Number and Client Name from Salesforce and select Create Folders");

    let textInputID = CardService.newTextInput()
      .setFieldName("clientId_input")
      .setTitle("Enter Client Number");

    let textInputName = CardService.newTextInput()
      .setFieldName("clientName_input")
      .setTitle("Enter Client Name");

    // ----------------------------------------------------------------------------------------------------------
    // Build elements used just by Standard Home and build Standard Home
    // ----------------------------------------------------------------------------------------------------------
    //Action to create Folders
    let actionRunFolder = CardService.newAction()
      .setFunctionName(createFoldersStd.name)
      .setLoadIndicator(CardService.LoadIndicator.SPINNER);

    let buttonCallStd = CardService.newTextButton()
      .setText("Create folders")
      .setOnClickAction(actionRunFolder)
      .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
      .setBackgroundColor(buttonColor);

    //Action to pop from Standard to PM if radio button selected
    let radioGroupStd = CardService.newSelectionInput()
      .setType(CardService.SelectionInputType.RADIO_BUTTON)
      .setTitle("Where do you want to create the folders?")
      .setFieldName("root_folder")
      .addItem("Ebury Corporate (New Clients)", "radio_ebury_corporate", true)
      .addItem("Ebury Private", "radio_ebury_private", false)
      .addItem("Ebury Affiliate", "radio_ebury_affiliate", false)
      .addItem("C4U Corporate", "radio_c4u_corporate", false)
      .addItem("C4U Private", "radio_c4u_private", false);

    let textInputSectionStd = CardService.newCardSection()
      .addWidget(textExplanation)
      .addWidget(textInputID)
      .addWidget(textInputName)
      .addWidget(radioGroupStd)
      .addWidget(buttonCallStd);

    let cardFolderHomeStd = CardService.newCardBuilder()
      .setName("Folder App Home")
      .setHeader(standardSectionHeader("Client File Folders", "Corporate and Private"))
      .addSection(textInputSectionStd)
      .setFixedFooter(standardSectionFooter());

    // ----------------------------------------------------------------------------------------------------------
    // Build elements used just by Programme Managers and build Programme Managers
    // ----------------------------------------------------------------------------------------------------------
    //Option 1: function 1: get list of subfolder names, function 2: get subfolder ID (issue may be speed), could it be cached?
    //Option 2: get a list of subfolder names and ids -> then cache the result.  Then call the cache again: use a script cache, so all script users can get it
    //list of all the sub folders under the program manager folder
    //const subFolderList = getSubFolderCache(testingMode);

    const subFolderList = EburyLibraryClientFolder.getSubFolders("radio_program_managers", testingMode);

    let actionRunFolderPm = CardService.newAction()
      .setFunctionName(createFoldersPm.name)
      .setLoadIndicator(CardService.LoadIndicator.SPINNER);

    let buttonCallPm = CardService.newTextButton()
      .setText("Create folders")
      .setOnClickAction(actionRunFolderPm)
      .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
      .setBackgroundColor(buttonColor);

    // Create a suggestion list via looping through the json
    let suggestionList = CardService.newSuggestions();
    for (const key in subFolderList) {
      if (subFolderList.hasOwnProperty(key)) {
        suggestionList.addSuggestion(key);
      }
    }

    const suggestionsResponse = CardService.newSuggestionsResponseBuilder()
      .setSuggestions(suggestionList)
      .build();

    let textDropDownGroup = CardService.newTextInput()
      .setFieldName("pm_root_folder")
      .setTitle("Choose a Programme Manager Folder")
      .setSuggestions(suggestionList);

    //build the section
    let textInputSectionPm = CardService.newCardSection()
      .addWidget(textExplanation)
      .addWidget(textInputID)
      .addWidget(textInputName)
      .addWidget(textDropDownGroup)
      .addWidget(buttonCallPm);

    let cardFolderHomePm = CardService.newCardBuilder()
      .setName("Folder App Home")
      .setHeader(standardSectionHeader("Client File Folders", "Program Managers"))
      .addSection(textInputSectionPm)
      .setFixedFooter(standardSectionFooter());

    // ----------------------------------------------------------------------------------------------------------
    // Build elements used just by AAA Partners
    // ----------------------------------------------------------------------------------------------------------
    //list of all the sub folders under the program manager folder

    const subFolderListAaa = EburyLibraryClientFolder.getSubFolders("radio_aaa_partners", testingMode);

    let actionRunFolderAaa = CardService.newAction()
      .setFunctionName(createFoldersAaa.name)
      .setLoadIndicator(CardService.LoadIndicator.SPINNER);

    let buttonCallAaa = CardService.newTextButton()
      .setText("Create folders")
      .setOnClickAction(actionRunFolderAaa)
      .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
      .setBackgroundColor(buttonColor);

    // Create a suggestion list via looping through the json
    let suggestionListAaa = CardService.newSuggestions();
    for (const key in subFolderListAaa) {
      if (subFolderListAaa.hasOwnProperty(key)) {
        suggestionListAaa.addSuggestion(key);
      }
    }

    const suggestionsResponseAaa = CardService.newSuggestionsResponseBuilder()
      .setSuggestions(suggestionListAaa)
      .build();

    let textDropDownGroupAaa = CardService.newTextInput()
      .setFieldName("aaa_root_folder")
      .setTitle("Choose a AAA Partner Folder")
      .setSuggestions(suggestionListAaa);

    //build the section
    let textInputSectionAaa = CardService.newCardSection()
      .addWidget(textExplanation)
      .addWidget(textInputID)
      .addWidget(textInputName)
      .addWidget(textDropDownGroupAaa)
      .addWidget(buttonCallAaa);

    let cardFolderHomeAaa = CardService.newCardBuilder()
      .setName("Folder App Home")
      .setHeader(standardSectionHeader("Client File Folders", "AAA Partners"))
      .addSection(textInputSectionAaa)
      .setFixedFooter(standardSectionFooter());

    // ----------------------------------------------------------------------------------------------------------
    // Return the three cards
    // ----------------------------------------------------------------------------------------------------------

    return [cardFolderHomeStd.build(), cardFolderHomePm.build(), cardFolderHomeAaa.build()];
  } catch (err) {
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
      );

    return actionResponse.build();
  }
}

/**
 * Creates Card "Folder App Complete"
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

function createCardFolderComplete(createdFolderStatus: any, createdFolderLink: string): any {
  //Action calls this function action => REPLACE THIS WITH A COMPLETION CARD
  let actionRebuild = CardService.newAction()
    .setFunctionName(createCardFolder.name)
    .setLoadIndicator(CardService.LoadIndicator.SPINNER);

  let buttonReset = CardService.newTextButton().setText("Start again").setOnClickAction(actionRebuild);

  let sectionFolderLocation = CardService.newTextParagraph().setText('<a href="' + createdFolderLink + '">Go to your new folders</a>');

  let textInputSection = CardService.newCardSection()
    .addWidget(sectionFolderLocation)
    .addWidget(buttonReset);

  let card = CardService.newCardBuilder()
    .setName("Folder App Complete")
    .setHeader(standardSectionHeader("Onboarding", "Create Client Folders"))
    .addSection(textInputSection)
    .setFixedFooter(standardSectionFooter());

  return card.build();
}

/**
 * Calls the folder library as part of an Action from Folder App Home card
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

export function createFoldersStd(e: any): any {
  //log details of the request
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const user = Session.getEffectiveUser().getEmail();
  console.info("Create Programme Manager Folders -> " + user);
  console.log(e.commonEventObject.formInputs.root_folder);

  if (
    e.commonEventObject.formInputs &&
    e.commonEventObject.formInputs.clientId_input &&
    e.commonEventObject.formInputs.clientName_input
  ) {
    //are all inputs present
    try {
      let clientId = e.commonEventObject.formInputs.clientId_input.stringInputs.value[0];
      let clientName = e.commonEventObject.formInputs.clientName_input.stringInputs.value[0];
      let rootFolder = e.commonEventObject.formInputs.root_folder.stringInputs.value[0];

      let folderReturns = EburyLibraryClientFolder.createFolderStructure(clientId, clientName, rootFolder, testingMode);

      let folderLink = "https://drive.google.com/drive/u/0/folders/" + folderReturns["folderId"];

      if (folderReturns["errorValue"]) {
        insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Success", startDate);
        //return a notification to say that it is complete
        return createCardFolderComplete("true", folderLink);
      } else {
        //return a notification to say that the main folder requested already exists
        let actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("Folder already exists"),
          );
        return actionResponse.build();
      }
    } catch (err) {
      insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
        );

      return actionResponse.build();
    }
  } else {
    //return a notification to say that one field or multiple user inputs are missing
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please enter a Client Number and Client Name"),
      );

    return actionResponse.build();
  }
}

/**
 * Calls the folder library as part of an Action from Folder App Home card
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

export function createFoldersPm(e: any): any {
  //log details of the request
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const user = Session.getEffectiveUser().getEmail();
  console.info("Create Programme Manager Folders -> " + user);
  console.log(e.commonEventObject.formInputs.pm_root_folder);

  //
  if (
    e.commonEventObject.formInputs &&
    e.commonEventObject.formInputs.clientId_input &&
    e.commonEventObject.formInputs.clientName_input &&
    e.commonEventObject.formInputs.pm_root_folder
  ) {
    //are all inputs present

    const subFolderList = EburyLibraryClientFolder.getSubFolders("radio_program_managers", testingMode);
    const clientId = e.commonEventObject.formInputs.clientId_input.stringInputs.value[0];
    const clientName = e.commonEventObject.formInputs.clientName_input.stringInputs.value[0];
    const pmRootFolder = e.commonEventObject.formInputs.pm_root_folder.stringInputs.value[0];
    const subFolderId = subFolderList[pmRootFolder];
    const accountOpening = true;
    console.log(clientId + " " + clientName + " " + subFolderId);
    try {
      let folderReturns = EburyLibraryClientFolder.createSubFolderStructure(clientId, clientName, subFolderId, accountOpening);

      let folderLink = "https://drive.google.com/drive/u/0/folders/" + folderReturns["folderId"];

      if (folderReturns["errorValue"]) {
        insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Success", startDate);
        //return a notification to say that it is complete
        return createCardFolderComplete("true", folderLink);
      } else {
        //return a notification to say that the main folder requested already exists
        let actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("Folder already exists"),
          );
        return actionResponse.build();
      }
    } catch (err) {
      insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
        );

      return actionResponse.build();
    }
  } else {
    //return a notification to say that one field or multiple user inputs are missing
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please enter a Client Number and Client Name"),
      );

    return actionResponse.build();
  }
}

/**
 * Calls the folder library as part of an Action from Folder App Home card
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

export function createFoldersAaa(e: any): any {
  //log details of the request
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  console.log(e.commonEventObject.formInputs.aaa_root_folder);

  //
  if (
    e.commonEventObject.formInputs &&
    e.commonEventObject.formInputs.clientId_input &&
    e.commonEventObject.formInputs.clientName_input &&
    e.commonEventObject.formInputs.aaa_root_folder
  ) {
    //are all inputs present

    const subFolderList = EburyLibraryClientFolder.getSubFolders("radio_aaa_partners", testingMode);
    const clientId = e.commonEventObject.formInputs.clientId_input.stringInputs.value[0];
    const clientName = e.commonEventObject.formInputs.clientName_input.stringInputs.value[0];
    const aaaRootFolder = e.commonEventObject.formInputs.aaa_root_folder.stringInputs.value[0];
    const subFolderId = subFolderList[aaaRootFolder];
    const accountOpening = true;
    try {
      let folderReturns = EburyLibraryClientFolder.createSubFolderStructure(clientId, clientName, subFolderId, accountOpening);

      let folderLink = "https://drive.google.com/drive/u/0/folders/" + folderReturns["folderId"];

      if (folderReturns["errorValue"]) {
        insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Success", startDate);
        //return a notification to say that it is complete
        return createCardFolderComplete("true", folderLink);
      } else {
        //return a notification to say that the main folder requested already exists
        let actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("Folder already exists"),
          );
        return actionResponse.build();
      }
    } catch (err) {
      insertActionOnBigQuery("Create Programme Manager Folders", "AUT03", "Error", startDate);
      let actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
        );

      return actionResponse.build();
    }
  } else {
    //return a notification to say that one field or multiple user inputs are missing
    let actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please enter a Client Number and Client Name"),
      );

    return actionResponse.build();
  }
}
