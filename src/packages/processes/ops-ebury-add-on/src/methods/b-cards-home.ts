/**
 * @file Functions that create and build the home cards when Gsuite apps open for the first time
 * <AUTHOR>
 */

import { functionSection_ClientFileFolders, functionSection_fileRenaming, functionSection_generatePDF, functionSection_ipCheckPrompt, functionSection_LetterMaker, functionSection_reassessmentFolders, functionSection_RemediationClientFileFolders } from "./x-cards-functionsSections.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

/**
 * Creates the generic Home card
 *
 * @return Builds the Home card
 */

export function cardHomeBuilder(): any {
  // Build the card
  const cardHome = CardService.newCardBuilder()
    .setName("Home")
    .setHeader(standardSectionHeader("Onboarding", "Controls and Helpers"))
    .setFixedFooter(standardSectionFooter())
    .setDisplayStyle(CardService.DisplayStyle.REPLACE);

  return cardHome.build();
}

/**
 * Creates the Drive Home card
 *
 * @return Builds the Drive Home card
 */

export function createNavigationDriveCard(): any {
  // ----------------------------------------------------------------------------------------------------------
  // Build the card
  // ----------------------------------------------------------------------------------------------------------
  const cardHome = CardService.newCardBuilder()
    .setName("Home")
    .setHeader(standardSectionHeader("Onboarding", "Controls and Helpers"))
    .addSection(functionSection_ClientFileFolders())
    .addSection(functionSection_RemediationClientFileFolders())
    .addSection(functionSection_reassessmentFolders())
    .addSection(functionSection_ipCheckPrompt())
    .addSection(functionSection_generatePDF())
    .addSection(functionSection_fileRenaming())
    .addSection(functionSection_LetterMaker())
    .setFixedFooter(standardSectionFooter())
    .setDisplayStyle(CardService.DisplayStyle.REPLACE);

  return cardHome.build();
}
