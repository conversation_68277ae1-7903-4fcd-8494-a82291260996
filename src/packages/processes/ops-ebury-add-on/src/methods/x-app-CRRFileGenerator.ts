import { GENERATE_QUERY } from "../env-settings.ts";
import { exportSpreadPdf, getOAuthService, getSecret, getTokenSF } from "./x-card-utils.ts";

export function accountCRRGeneratorCorporate(accountNumber: string, accountName: string, folderId: string): any {
  const user = Session.getEffectiveUser().getEmail();
  console.info("CRR Breakdown Corporate -> " + user);
  const service = getOAuthService("BigQuery");
  const files = [];
  service.reset();
  if (service.hasAccess()) {
    const query = GENERATE_QUERY(accountNumber);

    const queryRequest = {
      query: query,
      useLegacySql: false,
    };
    const url = "https://bigquery.googleapis.com/bigquery/v2/projects/data-ci/queries";
    const params: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions = {
      "headers": {
        "Authorization": "Bearer " + service.getAccessToken(),
        "Cache-Control": "max-age=0",
      },
      "muteHttpExceptions": true,
      "method": "post",
      "contentType": "application/json",
      "payload": JSON.stringify(queryRequest),
    };
    const response = UrlFetchApp.fetch(url, params).getContentText();
    const data = JSON.parse(response) as any;

    if (data && data.rows && data.rows[0] && data.rows[0].f) {
      const fields = data.schema.fields;
      const values = data.rows[0].f;

      const spreadsheetId = creeateCRRDocument(fields, values);
      files.push(...crrDocumentToPDF(accountName, folderId, spreadsheetId));
    }

    return files;
  } else {
    return "error";
  }
}

/** */

function crrDocumentToPDF(accountName: string, folderId: string, spreadsheetId: string): any[] {
  const crrFileRegEx = /\b[a-zA-Z0-9]+ - CRR Breakdown [A-Za-z]{3}, [0-9]{2} [A-Za-z]{3} [0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2} [A-Z]{3}$/;
  const date = new Date();
  const fileName = accountName + " - CRR Breakdown " + date.toUTCString();
  const files = [];

  const pdf = exportSpreadPdf(folderId, spreadsheetId, fileName);
  DriveApp.getFileById(spreadsheetId).setTrashed(true);

  files.push({ url: pdf.url, name: DriveApp.getFileById(pdf.pdfFileId).getName() });

  const oldFiles = DriveApp.getFolderById(folderId).getFiles();
  while (oldFiles.hasNext()) {
    const file = oldFiles.next();
    if (crrFileRegEx.test(file.getName()) && (file.getName() != fileName)) {
      files.push({ url: file.getUrl(), name: file.getName() });
    }
  }

  return files;
}

/** */

export function accountCRRGeneratorPrivate(accountId: string, accountName: string, folderId: string): any {
  const user = Session.getEffectiveUser().getEmail();
  console.info("CRR Breakdown Private -> " + user);
  const secretSF = getSecret("ProdSF");

  const url = secretSF.sf_url + "services/data/v50.0/query";
  const params: GoogleAppsScript.URL_Fetch.URLFetchRequestOptions = {
    "method": "get",
    "contentType": "application/json",
    "headers": {
      "Authorization": "Bearer " + getTokenSF(secretSF),
      "Accept": "application/json",
    },
  };
  const query = "?q=SELECT+Id+,+Risk_Score__c+,+Sanctions2__c+,+Internal_matches__c+,+Product_List__c+,+Does_the_client_declare_regular_incomes__c+,+New_Pep__c+,+Other_matches__c+,+Negative_media__c+,+Nationality__pc+,+Delivery_Channel__c+,+Account_Country__r.Name+,+FX_Source_Funds_Rollup_CSV__c+,+FX_Destination_Funds_Rollup_CSV__c+,+Occupation_Industry__pc+,+Level_of_control__pc+,+PersonBirthdate+,+Est_FX_Turnover__c+FROM+Account+WHERE+Id+='" + accountId + "'";

  const response = UrlFetchApp.fetch(url + query, params).getContentText();
  const data = JSON.parse(response) as any;

  const spreadsheet = SpreadsheetApp.create("CRR Private Results");
  const spreadsheetId = spreadsheet.getId();
  const sheetTemplate = SpreadsheetApp.openById("1du-DxuJGwuZZIY9W_NOLFXRH-oN5pIPX1ElzLfug8mw").getSheets()[0];
  sheetTemplate.copyTo(SpreadsheetApp.openById(spreadsheetId));
  const ss = SpreadsheetApp.openById(spreadsheetId);
  const sheetToDelete = SpreadsheetApp.openById(spreadsheetId).getSheetByName("Sheet1");
  ss.deleteSheet(sheetToDelete);
  const sheet = ss.setActiveSheet(ss.getSheetByName("Copy of Template"));

  privateCRRPrintTemplate(sheet, data, accountName);

  const uniqueCountries = buildCountriesList(data.records[0].Account_Country__r.Name, data.records[0].FX_Source_Funds_Rollup_CSV__c, data.records[0].FX_Destination_Funds_Rollup_CSV__c, data.records[0].Nationality__pc);

  const isoCountries: string[] = [];
  const countries: string[] = [];
  for (let i = 0; i < uniqueCountries.length; i++) {
    if (uniqueCountries[i].length == 2) {
      isoCountries.push(uniqueCountries[i]);
    } else {
      countries.push(uniqueCountries[i]);
    }
  }

  const queryCountries = "?q=SELECT+Id+,+Name+,+Private_Risk_Score__c+FROM+Country__c+WHERE+Name+IN+(+'" + countries.join("'+,+'") + "'+)+OR+ISO_Code_2__c+IN+(+'" + isoCountries.join("'+,+'") + "'+)";
  const responseCountries = UrlFetchApp.fetch(url + queryCountries, params).getContentText();
  const dataCountries = JSON.parse(responseCountries) as any;

  const countriesListRisk = dataCountries.records;
  const riskCountries: any[] = [];

  for (let i = 0; i < countriesListRisk.length; i++) {
    let risk = null;
    switch (countriesListRisk[i].Private_Risk_Score__c) {
      case 3:
        risk = "Low";
        break;
      case 4:
        risk = "Medium";
        break;
      case 15:
        risk = "High";
        break;
      case 18:
        risk = "Very high";
        break;
    }

    riskCountries.push({ "country": countriesListRisk[i].Name, "risk": risk });
  }

  for (let i = 0; i < riskCountries.length; i++) {
    sheet.appendRow([riskCountries[i].country, riskCountries[i].risk]);
    const lastRow = sheet.getLastRow();
    const range = sheet.getRange("A" + lastRow + ":B" + lastRow);
    range.setFontWeight("normal");
    range.setBorder(true, true, true, true, true, true, "black", SpreadsheetApp.BorderStyle.SOLID).setHorizontalAlignment("left");
  }

  SpreadsheetApp.flush();

  const files = crrDocumentToPDF(accountName, folderId, spreadsheetId);

  return files;
}

function buildCountriesList(accountCountry: string, sof: string, dof: string, nationality: string): string[] {
  const countriesList: string[] = [];

  if (accountCountry.includes(", ")) {
    const list = accountCountry.split(", ");
    for (let i = 0; i < list.length; i++) {
      const country = list[i];
      countriesList.push(country);
    }
  } else {
    countriesList.push(accountCountry);
  }

  if (sof.includes(", ")) {
    const list = sof.split("', '");
    for (let i = 0; i < list.length; i++) {
      const country = list[i];
      countriesList.push(country);
    }
  } else {
    countriesList.push(sof);
  }

  if (dof.includes(", ")) {
    const list = dof.split(", ");
    for (let i = 0; i < list.length; i++) {
      const country = list[i];
      countriesList.push(country);
    }
  } else {
    countriesList.push(dof);
  }

  if (nationality.includes(", ")) {
    const list = nationality.split(", ");
    for (let i = 0; i < list.length; i++) {
      const country = list[i];
      countriesList.push(country);
    }
  } else {
    countriesList.push(nationality);
  }

  return [...new Set(countriesList)];
}

/** */

function privateCRRPrintTemplate(sheet: any, data: any, accountName: string): void {
  const date = new Date();
  const dateUTC = date.toUTCString();
  const email = Session.getEffectiveUser().getEmail();

  const replacements = [
    ["{{Person Account Name}}", accountName],
    ["{{Date captured}}", dateUTC],
    ["{{Email captured}}", email],
    ["{{Risk Rating}}", data.records[0].Risk_Score__c],
    ["{{Client Country}}", data.records[0].Account_Country__r.Name],
    ["{{SOF}}", data.records[0].FX_Source_Funds_Rollup_CSV__c],
    ["{{DOF}}", data.records[0].FX_Destination_Funds_Rollup_CSV__c],
    ["{{Reg Income}}", data.records[0].Does_the_client_declare_regular_incomes__c],
    ["{{Nationality}}", data.records[0].Nationality__pc],
    ["{{PEP}}", data.records[0].New_Pep__c],
    ["{{Negative Media}}", data.records[0].Negative_media__c],
    ["{{Sanctions}}", data.records[0].Sanctions2__c],
    ["{{Other Matches}}", data.records[0].Other_matches__c],
    ["{{Internal Matches}}", data.records[0].Internal_matches__c],
    ["{{Product Risk}}", data.records[0].Product_List__c],
    ["{{Delivery Channel}}", data.records[0].Delivery_Channel__c],
    ["{{Birth date}}", data.records[0].PersonBirthdate],
    ["{{FX Flow}}", data.records[0].Est_FX_Turnover__c],
    ["{{Industry}}", data.records[0].Occupation_Industry__pc],
    ["{{Level of control}}", data.records[0].Level_of_control__pc],
  ];

  for (let i = 0; i < replacements.length; i++) {
    replacements[i][1] = (replacements[i][1] === null) ? "None" : replacements[i][1];
    const finder = sheet.createTextFinder(replacements[i][0]);
    finder.findNext().setHorizontalAlignment("left");
    finder.replaceAllWith(replacements[i][1]);
  }

  SpreadsheetApp.flush();
}

/** */

function creeateCRRDocument(fields: any[], values: any[]): string {
  const spreadsheet = SpreadsheetApp.create("BiqQuery Results", fields.length, 1);
  const sheet = spreadsheet.getActiveSheet();

  const date = new Date();
  const dateUTC = date.toUTCString();
  const email = Session.getEffectiveUser().getEmail();

  sheet.appendRow(["Performer", email]);
  sheet.appendRow(["Execution Date", dateUTC]);
  sheet.appendRow(["  ", "  "]);

  for (let i = 0; i < fields.length; i++) {
    sheet.appendRow([fields[i].name, "'" + values[i].v]);
    sheet.setRowHeight(i + 4, 35);
  }
  sheet.autoResizeColumns(1, 2);
  sheet.getRange("B1:B" + fields.length + 3).setHorizontalAlignment("left");
  sheet.getRange("A1:A" + fields.length + 3).setFontWeight("bold");
  sheet.setHiddenGridlines(true);

  SpreadsheetApp.flush();
  const spreadsheetId = spreadsheet.getId();

  return spreadsheetId;
}

/** */

export function searchFolder(folderId: string): any[] {
  const folderParent = DriveApp.getFolderById(folderId);
  const childrens = folderParent.getFolders();
  const folders = [];

  while (childrens.hasNext()) {
    const nameFolder = "2. Nature and purpose of business";
    const auxFolder = childrens.next();
    if (auxFolder.getName() === nameFolder) {
      folders.push({ name: auxFolder.getName(), value: auxFolder.getId() });
    } else {
      const folder = auxFolder.getFoldersByName(nameFolder);
      if (folder.hasNext()) {
        const folderFound = folder.next();
        folders.push({ name: auxFolder.getName() + " -> " + folderFound.getName(), value: folderFound.getId() });
      }
    }
  }

  return folders;
}
