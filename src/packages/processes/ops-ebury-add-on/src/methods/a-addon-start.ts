/**
 * @file Call functions to launch the add on when Gsuite apps open
 * <AUTHOR> <<EMAIL>>
 */

import { cardHomeBuilder, createNavigationDriveCard } from "./b-cards-home.ts";
import { onDriveItemsSelected } from "./b-driveSelect-home.ts";

/**
 * Generic home page card builder
 *
 * @param e - Event object when the Gsuite app starts
 * @return Runs the card builder (cardHomeBuilder function) for the home card
 */
export function onAppStart(e: GoogleAppsScript.Events): any {
  console.log(e);
  return cardHomeBuilder();
}
/**
 * Creates card when addon is launched from Drive
 *
 * @param e - Event object when the Drive app starts
 * @return Runs the card builder (createNavigationDriveCard function) for the Drive starting card
 */
export function onDriveStart(e: any): any {
  console.log(e);
  const isItemSelected = e.drive.activeCursorItem;
  console.log({ isItemSelected });
  if (isItemSelected) {
    return onDriveItemsSelected(e);
  }
  return createNavigationDriveCard();
}
