/**
 * @file Card Utilities that can be used across all Card calls
 * <AUTHOR>
 */

import { helpGuideURL, iconURL } from "./x-card-config.ts";
import { gotoRootCard } from "./x-card-utils.ts";

/**
 * Creates a generic card header across all cards
 *
 * @param {string} title - Title of the header
 * @param {string} subtitle - Subtitle of the header
 * @return {object} A card object that holds the standard header
 */

export function standardSectionHeader(title: string, subtitle: string): any {
  let stdHeader = CardService.newCardHeader()
    .setTitle(title)
    .setSubtitle(subtitle)
    .setImageStyle(CardService.ImageStyle.SQUARE)
    .setImageUrl(iconURL);

  return stdHeader;
}

/**
 * Creates a generic card footer across all cards
 *
 * @return {object} A card object that holds the standard footer
 */

export function standardSectionFooter(): any {
  let gotoRootAction = CardService.newAction()
    .setFunctionName(gotoRootCard.name);

  let buttonGotoRoot = CardService.newTextButton()
    .setText("Home Screen")
    .setOnClickAction(gotoRootAction);

  let buttonUserGuide = CardService.newTextButton()
    .setText("User Guide")
    .setOpenLink(
      CardService.newOpenLink()
        .setUrl(helpGuideURL),
    )
    .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
    .setBackgroundColor("#094358");

  let stdFooter = CardService.newFixedFooter()
    .setPrimaryButton(buttonUserGuide)
    .setSecondaryButton(buttonGotoRoot);

  return stdFooter;
}

/**
 * Builds a card Action Response that navigates back to the root card
 *
 * @return {object} A card Action Response that holds the navigation to root
 */

/**
 * Creates a card section to hold the pop to root card navigation
 *
 * @return {object} A card section that that holds a button to go to root
 */

export function buildGotoRootCard(): any {
  let buttonGotoRoot = CardService.newTextButton()
    .setText("Return to Home screen")
    .setOnClickAction(
      CardService.newAction()
        .setFunctionName(gotoRootCard.name),
    );

  let sectionGotoRoot = CardService.newCardSection()
    .addWidget(buttonGotoRoot);

  return sectionGotoRoot;
}
