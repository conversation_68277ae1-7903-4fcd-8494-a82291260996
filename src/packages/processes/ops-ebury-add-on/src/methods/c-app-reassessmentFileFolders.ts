import { createNavigationDriveCard } from "./b-cards-home.ts";
import { buttonColor } from "./x-card-config.ts";
import { EburyLibraryClientFolder, insertActionOnBigQuery } from "./x-card-utils.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

export function reassessmentFolderCard(folderName: string, folderId: string): any {
  //Have you selected a Client Folder?
  const date = new Date();
  const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
  const year = date.getFullYear().toString();
  const namePart1 = folderName.toString().split("-")[1];
  const namePart2 = "Reassessment - " + months[date.getMonth()] + year.substr(2, 4);
  const reassessmentFolderName = namePart1 + " - " + namePart2;

  const folderCreateText = CardService.newDecoratedText()
    .setText('Folder\n"' + reassessmentFolderName.trim() + '"\nwill be created in \n"' + folderName + '"')
    .setTopLabel("Create Reassessment folder:")
    .setWrapText(true);

  //    const folderCreateText = CardService.newTextParagraph()
  //       .setText('Create Reassessment folder: \n' + reassessmentFolderName + '\n ');

  const subFolderAction = CardService.newAction()
    .setFunctionName(createReassessmentFolders.name)
    .setParameters({
      namePart1: namePart1,
      namePart2: namePart2,
      targetFolderId: folderId,
    });

  let buttonCallReassessment = CardService.newTextButton()
    .setText("Create folders")
    .setOnClickAction(subFolderAction)
    .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
    .setBackgroundColor(buttonColor);

  const sectionReassessment = CardService.newCardSection()
    .addWidget(folderCreateText)
    .addWidget(buttonCallReassessment);
  // ----------------------------------------------------------------------------------------------------------
  // Build elements used on both cards: Standard Home and PM Home
  // ----------------------------------------------------------------------------------------------------------

  let cardFolderHomeStd = CardService.newCardBuilder()
    .setName("Folder App Home 2")
    .setHeader(standardSectionHeader("Reassessment", "Create Folders"))
    .addSection(sectionReassessment)
    .setFixedFooter(standardSectionFooter())
    .setDisplayStyle(CardService.DisplayStyle.REPLACE);

  return cardFolderHomeStd.build();
}

export function createReassessmentFolders(e: any): any {
  let actionResponse: GoogleAppsScript.Card_Service.ActionResponseBuilder;
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const user = Session.getEffectiveUser().getEmail();
  console.info("Create Reassessment Folders -> " + user);
  console.log(e);
  const namePart1 = e.parameters.namePart1;
  const namePart2 = e.parameters.namePart2;
  const targetFolderId = e.parameters.targetFolderId;
  const accountOpening = false;
  if (namePart1 != "" && namePart2 != "" && targetFolderId != "") {
    //are all inputs present
    try {
      let folderReturns = EburyLibraryClientFolder.createSubFolderStructure(namePart1, namePart2, targetFolderId, accountOpening);

      let folderLink = "https://drive.google.com/drive/u/0/folders/" + folderReturns["folderId"];

      if (folderReturns["errorValue"]) {
        insertActionOnBigQuery("Create Reassessment Folders", "AUT04", "Success", startDate);
        //return a notification to say that it is complete
        return createCardFolderCompleteReassessment("true", folderLink);
      } else {
        //return a notification to say that the main folder requested already exists
        actionResponse = CardService.newActionResponseBuilder()
          .setNotification(
            CardService.newNotification()
              .setText("Folder already exists"),
          );
      }
    } catch (err) {
      insertActionOnBigQuery("Create Reassessment Folders", "AUT04", "Error", startDate);
      actionResponse = CardService.newActionResponseBuilder()
        .setNotification(
          CardService.newNotification()
            .setText("You do not have access to the Client File shared drives. You must have Contributor or Content Manager permissions."),
        );
    }
  } else {
    //return a notification to say that one field or multiple user inputs are missing
    actionResponse = CardService.newActionResponseBuilder()
      .setNotification(
        CardService.newNotification()
          .setText("Please contact support"),
      );
  }

  return actionResponse.build();
}

/**
 * Creates Card "Folder App Complete"
 *
 * @param {object} e - Event object from callback action when someone submits data from card Folder App Home
 * @return {object} An action response notification with a message to say that either the the folder build is complete or there is an user issue
 *
 * @example
 *
 *     testrun()
 */

function createCardFolderCompleteReassessment(createdFolderStatus: any, createdFolderLink: string): any {
  //Action calls this function action => REPLACE THIS WITH A COMPLETION CARD
  let actionRebuild = CardService.newAction()
    .setFunctionName(createNavigationDriveCard.name)
    .setLoadIndicator(CardService.LoadIndicator.SPINNER);

  let buttonReset = CardService.newTextButton().setText("Start again").setOnClickAction(actionRebuild);

  let sectionFolderLocation = CardService.newTextParagraph().setText('<a href="' + createdFolderLink + '">Go to your new folders</a>');

  let textInputSection = CardService.newCardSection()
    .addWidget(sectionFolderLocation)
    .addWidget(buttonReset);

  let card = CardService.newCardBuilder()
    .setName("Folder App Complete")
    .setHeader(standardSectionHeader("Onboarding", "Create Client Folders"))
    .addSection(textInputSection)
    .setFixedFooter(standardSectionFooter());

  return card.build();
}
