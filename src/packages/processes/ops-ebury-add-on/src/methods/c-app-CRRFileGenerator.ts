import { searchFolder } from "./x-app-CRRFileGenerator.ts";
import { buttonColor } from "./x-card-config.ts";
import { createCardByConfig, getAccountCRR, insertActionOnBigQuery } from "./x-card-utils.ts";
import { standardSectionFooter, standardSectionHeader } from "./x-cards-standardSections.ts";

export function createGetCRRCard(accountNumber: string, accountName: string, folderId: string): any {
  let cardConvertFile: any = null;
  const folders = searchFolder(folderId);
  if (folders.length > 0) {
    const textExplanation = CardService.newTextParagraph()
      .setText('File\n<b>"' + accountName + ' - CRR Breakdown"</b>\n will be created in\n<b>"' + accountNumber + " - " + accountName + '"</b>');

    const actionRunFile = CardService.newAction()
      .setFunctionName(getInputsAndConvert.name)
      .setLoadIndicator(CardService.LoadIndicator.SPINNER)
      .setParameters({ accountNumber: accountNumber, accountName: accountName });

    const dropdownGroupStd = CardService.newSelectionInput()
      .setType(CardService.SelectionInputType.DROPDOWN)
      .setTitle("Where do you want to create the CRR?")
      .setFieldName("crr_folder");

    for (let i = 0; i < folders.length; i++) {
      dropdownGroupStd.addItem(folders[i].name, folders[i].value, false);
    }

    const buttonCallStd = CardService.newTextButton()
      .setText("Get CRR PDF")
      .setOnClickAction(actionRunFile)
      .setTextButtonStyle(CardService.TextButtonStyle.FILLED)
      .setBackgroundColor(buttonColor);

    const actionDescription = CardService.newCardSection()
      .addWidget(textExplanation)
      .addWidget(dropdownGroupStd)
      .addWidget(buttonCallStd);

    cardConvertFile = CardService.newCardBuilder()
      .setName("Generate CRR PDF")
      .setHeader(standardSectionHeader("Generate CRR PDF", "Get CRR"))
      .addSection(actionDescription)
      .setFixedFooter(standardSectionFooter())
      .setDisplayStyle(CardService.DisplayStyle.REPLACE);
  } else {
    const textExplanation = CardService.newTextParagraph()
      .setText('Logging folder \n<b>"2. Nature and purpose of business"</b>\nis missing.\n\n Please review the User Guide for help.');

    const actionDescription = CardService.newCardSection()
      .addWidget(textExplanation);

    cardConvertFile = CardService.newCardBuilder()
      .setName("Generate CRR PDF")
      .setHeader(standardSectionHeader("Generate CRR PDF", "Get CRR"))
      .addSection(actionDescription)
      .setFixedFooter(standardSectionFooter())
      .setDisplayStyle(CardService.DisplayStyle.REPLACE);
  }

  return cardConvertFile.build();
}

export function getInputsAndConvert(e: any): any {
  //read parameter inputs
  const user = Session.getEffectiveUser().getEmail();
  console.info("CRR Breakdown PDF -> " + user);
  const startDate = new Date(Date.now()).toISOString().slice(0, -1);
  const accountNumber = e.parameters.accountNumber;
  const accountName = e.parameters.accountName;
  const folderId = e.commonEventObject.formInputs.crr_folder.stringInputs.value[0];

  const files = getAccountCRR(accountNumber, accountName, folderId);

  let textExplanation = null;
  if (files !== "error") {
    insertActionOnBigQuery("CRR Breakdown PDF", "AUT01", "Success", startDate);
    if (files.length === 1) {
      textExplanation = CardService.newTextParagraph()
        .setText('CRR PDF Created. <a href="' + DriveApp.getFolderById(folderId).getUrl() + '">Go to folder</a>\n<br>You can check it here:\n<br><a href="' + files[0].url + '">Go to ' + files[0].name + "</a>");
    } else if (files.length > 1) {
      let text = 'CRR PDF Created. <a href="' + DriveApp.getFolderById(folderId).getUrl() + '">Go to folder</a>\n<br><a href="' + files[0].url + '">Go to ' + files[0].name + "</a>\n<br>Important: we have found previous file(s) in the folder. Check below:\n";
      for (let i = 1; i < files.length; i++) {
        text = text + '<br><a href="' + files[i].url + '">Go to ' + files[i].name + "</a>";
      }
      textExplanation = CardService.newTextParagraph()
        .setText(text);
    } else {
      textExplanation = CardService.newTextParagraph()
        .setText("There is no CRR result for the account " + accountNumber + ".<br>Please review Salesforce and try again after a few minutes.");
    }

    const actionDescription = CardService.newCardSection()
      .addWidget(textExplanation);

    const cardConvertFileSuccess = CardService.newCardBuilder()
      .setName("Generate CRR PDF")
      .setHeader(standardSectionHeader("Generate CRR PDF", "Get CRR"))
      .addSection(actionDescription)
      .setFixedFooter(standardSectionFooter())
      .setDisplayStyle(CardService.DisplayStyle.REPLACE);

    return cardConvertFileSuccess.build();
  } else {
    insertActionOnBigQuery("CRR Breakdown PDF", "AUT01", "Error", startDate);
    return errorAccessBigQueryCard();
  }
}

export function errorAccessBigQueryCard(): any {
  console.info("ERROR: Connecting to Big Query");
  const text = "Something went wrong connecting to Big Query, please, try again. If the error persist, please contact with us.";
  const config = {
    cardName: {
      cardName: "Generate CRR PDF",
      cardTitle: "Get CRR",
    },
    text: text,
  };

  const errorBigQuery = createCardByConfig(config);
  return errorBigQuery.build();
}
