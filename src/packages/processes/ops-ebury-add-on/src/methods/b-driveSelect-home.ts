/**
 * @file Creates Drive selection cards
 * <AUTHOR>
 */

import { createNavigationDriveCard } from "./b-cards-home.ts";
import { createGetCRRCard } from "./c-app-CRRFileGenerator.ts";
import { fileRenamingMainCard } from "./c-app-fileRenaming.ts";
import { createIpCheckCardSelect } from "./c-app-ipAddress-select.ts";
import { reassessmentFolderCard } from "./c-app-reassessmentFileFolders.ts";
import { ipCheckConfig } from "./x-card-config.ts";

/**
 * Creates the Drive items selected contextual cards
 *
 * @return {cards} Builds the cards for controls based on Drive contextual location
 */
export function onDriveItemsSelected(e: GoogleAppsScript.Addons.EventObject): GoogleAppsScript.Card_Service.Card[] {
  console.log(e);
  const selectedItem = e.drive.selectedItems?.[0] || e.drive.activeCursorItem;
  const cardHome = createNavigationDriveCard();
  if (!selectedItem) {
    return [cardHome];
  }
  //identify folder location
  const rootFolderRegEx = /[0-9]+ - [a-zA-Z0-9]+/;
  const accountOpeningRegEx = /\b[a-zA-Z0-9]+ - Account opening$/;
  const accountReassessmentRegEx = /\b[a-zA-Z0-9]+ - Reassessment - [A-Z][a-z]{2}[0-9]{2}$/;
  const selectedItemId = selectedItem.id;
  const selectedItemName = selectedItem.title;
  const isFolder = selectedItem.mimeType === "application/vnd.google-apps.folder";
  if (isFolder) {
    console.log("Folder selected");
    const selectedFolder = DriveApp.getFolderById(selectedItemId);
    const parentFolder = selectedFolder.getParents().next();
    const parentFolderId = parentFolder.getId();
    const parentFolderName = parentFolder.getName();
    const logFolderArray = [];
    const isRootFolder = rootFolderRegEx.test(selectedItemName);
    const isWithinRootFolder = rootFolderRegEx.test(parentFolderName);
    //Selected a Client File in the correct format
    if (selectedFolder && isRootFolder) {
      console.log("Root folder selected");
      //Create reassessment create folder card
      const cardReassesment = reassessmentFolderCard(
        selectedItemName,
        selectedItemId,
      );
      //Create CRR File card
      const cardGetCRRFile = createGetCRRCard(
        selectedItemName.split("-")[0].trim(),
        selectedItemName.split("-")[1].trim(),
        selectedItemId,
      );
      //Create log folder card
      logFolderArray.push(...getLogFolders(selectedFolder));
      const cardIpCheck = createIpCheckCardSelect(
        selectedItemName.split("-")[0],
        selectedItemName.split("-")[1],
        logFolderArray,
      );
      return [cardReassesment, cardIpCheck, cardGetCRRFile];
    } //Selected a folder with direct parent that is a Client File
    else if (selectedFolder && isWithinRootFolder) {
      console.log("Folder within root folder selected");
      //Create reassessment create folder card
      const cardReassesment = reassessmentFolderCard(
        parentFolderName,
        parentFolderId,
      );
      //Create CRR File card
      const cardGetCRRFile = createGetCRRCard(
        parentFolderName.split("-")[0].trim(),
        parentFolderName.split("-")[1].trim(),
        parentFolderId,
      );
      //Create log folder card
      logFolderArray.push(...getLogFolders(parentFolder));
      const cardIpCheck = createIpCheckCardSelect(
        parentFolderName.split("-")[0],
        parentFolderName.split("-")[1],
        logFolderArray,
      );
      return [cardReassesment, cardIpCheck, cardGetCRRFile];
    }
    console.log("other uncatched!");
  }
  const isFile = DriveApp.getFileById(selectedItemId);
  if (isFile) {
    console.log("File selected");
    //Create renaming file card
    const cardFileRenaming = fileRenamingMainCard(selectedItemId);
    return cardFileRenaming;
  }
  console.log("Something else selected");
  //Navigate back to Home screen, as not in a triggering folder location
  return [cardHome];
}

/**
 * Loops through the folders and creates an array of objects describing potential logging folders
 *
 * @param {string} parrent - The acount ID of the IP being checked
 * @param {array} logFolderArray - An array of objects that describe the logging folders
 *
 * @return {array} An array of objects that describe the logging folders
 */

type LogFolder = {
  logFolderId: string;
  logFolderParentName: string;
  logFolderParentId: string;
};
function getLogFolders(parent: GoogleAppsScript.Drive.Folder) {
  const logFolderArray: LogFolder[] = [];
  const childrenFolders = parent.getFolders();
  while (childrenFolders.hasNext()) {
    const folder = childrenFolders.next();
    const logObj: LogFolder = { logFolderId: "", logFolderParentName: "", logFolderParentId: "" };
    if (folder.getName() === ipCheckConfig.outputLoggingFldrName) {
      logObj.logFolderId = folder.getId();
      logObj.logFolderParentName = folder.getParents().next().getName();
      logFolderArray.push(logObj);
    }
    logFolderArray.push(...getLogFolders(folder));
  }
  return logFolderArray;
}
