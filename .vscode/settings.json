{"editor.defaultFormatter": "denoland.vscode-deno", "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.formatOnPaste": false, "editor.formatOnType": false, "json.validate.enable": true, "json.schemas": [{"fileMatch": ["*/processes/**/package.json"], "url": "./src/environment/schemas/packages/processes/package.json"}, {"fileMatch": ["*/processes/**/appsscript.json"], "url": "./src/environment/schemas/packages/processes/appsscript.json"}, {"fileMatch": ["*/processes/**/manifest.json"], "url": "./src/environment/schemas/packages/processes/manifest.json"}], "files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/CVS": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/node_modules": false}, "deno.enable": true, "deno.config": "./deno_settings.json", "deno.importMap": "./deno_settings.json", "deno.lint": true, "deno.suggest.imports.autoDiscover": true, "deno.cacheOnSave": true, "deno.maxTsServerMemory": 8192, "deno.codeLens.referencesAllFunctions": true, "deno.documentPreloadLimit": 0, "deno.suggest.imports.hosts": {"https://deno.land": true}, "liveServer.settings.port": 5502, "cSpell.words": ["dalo", "Ebury", "enqueuement", "Exco", "firestore", "FXSOLUTIONS", "hibob", "Netsuite", "passwordless", "vonage"], "[typescript]": {"editor.defaultFormatter": "denoland.vscode-deno"}, "[jsonc]": {"editor.defaultFormatter": "denoland.vscode-deno"}}