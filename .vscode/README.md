# .vscode directory

This directory contains the configuration files for Visual Studio Code.

## Recommended extensions

In order to have the best experience with Visual Studio Code, we recommend installing the following extensions:

- `GitHub.vscode-pull-request-github`: with this you will be able create and review Pull Requests directly from Visual Studio Code;
- `github.vscode-github-actions`: Manage your workflows and runs without leaving your editor, keep track of your CI builds and deployments, investigate failures and view logs;
- `denoland.deno`: This extension adds support for using Deno with Visual Studio Code.
- `bierner.markdown-mermaid`: Adds Mermaid diagram and flowchart support to VS Code's builtin markdown preview

## Suggested extensions

These extension are not required but their use may be of your interest:

### Git

- `mhutchie.gitgraph`: View a Git Graph of your repository, and perform Git actions from the graph.
- `eamodio.gitlens`: GitLens supercharges the Git capabilities built into Visual Studio Code - it helps you to visualize code authorship at a glance via Git blame annotations and code lens, seamlessly navigate and explore Git repositories, gain valuable insights via powerful comparison commands, and so much more;

### AI

- `Codeium.codeium`: Modern coding superpower, a free code acceleration toolkit built on cutting edge AI technology;
- `GoogleCloudTools.cloudcode`: AI-powered coding assistant to code and deploy to your favorite Google Cloud platforms;
- `github.copilot`: Ebury grant us with licenses so let get the best of AI.

### Others

- `mattpocock.ts-error-translator`: This extension will translate TypeScript errors into human-readable messages;
- `rangav.vscode-thunder-client`: it's like Postman, but embedded in Visual Studio Code; it lets you also save your requests in a file, so you can share them with your team;
- `WallabyJs.console-ninja`: Console Ninja is a VS Code extension that shows console.log outputs and runtime errors in your editor from browser or node apps. It combines browser dev tools console or node app terminal output with your code, displaying values ergonomically without context switching.

## Settings

Most of the settings are for enabling common formatting rules.

There are also settings to enable the validation of the JSON schemas directly in the editor. To add validation rules for a new JSON schema, you need to add a new entry in the `json.schemas` array, for example like this:

```json
{
  "fileMatch": ["src/environment/schemas/packages/processes/*.json"],
  "url": "./src/environment/schemas/packages/processes/schema.json"
}
```
