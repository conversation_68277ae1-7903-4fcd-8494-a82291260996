# Automation portal flow

This document describes the automation request portal that should be used to create a request for the BPA team. https://fxsolutions.atlassian.net/servicedesk/customer/portal/18

## Structure

It is divided into three parts. Proposals for automation, notifications of an error in an already automated process or consulting and advice. In turn, the proposals can refer to a totally new process or to an improvement to a previously automated process.

- **Proposal for Automation**
  - **Automation request**: Submit a request to automate a new process
    - We create value to Ebury by mitigating risks, speeding up time consuming tasks and finding cost savings, thus we will need some information regarding our metrics to evaluate the impact of your request. Please take time to think on the saved time (hours per week) and/or the risk mitigation potentially achieved with the request.

  - **Automation enhancement**: Submit an enhancement for an already automated process
- **Report a problem**: Report a problem or an incident
- **Automation consulting and advice**: If you have a question, we can help you with automation consulting and advice

![](./assets/Automation-requests-portal.png)

## Request flow

Stakeholders will create their requests which will be analyzed by the Business Process Automation team.

For proposals that need a long follow-up time, issues will be created in turn in the BPA project. It could be a spike or a complete Epic if the change is of sufficient entity.

These requests will be the point of contact between the stakeholders and the team to announce when the changes are ready to be tested or released gradually in production.

The issues will be created in the JSM project Automation Requests [AURE](https://fxsolutions.atlassian.net/jira/servicedesk/projects/AURE/queues/custom/193)

The flow that the requests will follow is reflected in the following status:

- **To Do** - Create but not prioritized
- **Planned** - Prioritized but not started
- **In Progress** - Actively working on it
- **Blocked** - On-hold due to an impediment
- **Coming Soon** - Bug session / Live testing
- **Go Live** - Roll-out phase
- **Done** - Not longer working on it

![](./assets/Automation-requests-workflow.png)

### Stakeholders communication

There are 2 ways to communicate with stakeholders through the JSM ticket.

- **Add internal note:** Leave a comment in the request that will only be seen by an agent, not by the customer.
- **Reply to customer:** The comment will be sent to the customer and to all ticket participants. In addition, it can be viewed from the portal by them. Messages from customers will also appear in this section.

![](./assets/Automation-requests-internal-external-comments.png)

## Nuances

### Access to JSM

The access is <NAME_EMAIL> user, so you need to use it instead of your own user.

### Jira automation limitation

We created an automation to create a linked issue into BPA and notify to Slack every time a new request is created BUT as a shared project automation, when the allowed number of executions is reached, it stop working.
