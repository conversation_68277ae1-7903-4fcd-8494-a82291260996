const RAND_ARRAY_LENGTH = process.env.RAND_ARRAY_LENGTH || 1_000_000;
const MAX_EXEC_TIME_MS = process.env.MAX_EXEC_TIME_MS || 1000;

const randArray = Array.from(
  { length: RAND_ARRAY_LENGTH },
  (_) => Math.trunc(Math.random()),
);
const fixedArray = ["a", "b", "c", "d", "e"];

if (!global.gc) {
  console.warn("No GC hook! Start your program as `node --expose-gc file.js`.");
  process.exit();
}

let ops;
let start;
let execTimeSecs;
let opsPerSec;

// Concat
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  fixedArray.concat(randArray);
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`concat: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Reversed concat
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  randArray.concat(fixedArray);
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`concat reversed: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Spread
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  [...fixedArray, ...randArray];
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`spread: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Inline spread
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  ["a", "b", "c", "d", "e", ...randArray];
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`inline spread: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Concat one element
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  randArray.concat("a");
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`concat one element: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Concat array of one element
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  randArray.concat(["a"]);
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`concat array of one element: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Spread one element
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  [...randArray, "a"];
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`spread one element: ${opsPerSec.toFixed(2)} ops/sec`);

global.gc();

// Spread array of one element
ops = 0;
start = Date.now();
while (Date.now() - start < MAX_EXEC_TIME_MS) {
  [...randArray, ...["a"]];
  ops++;
}
execTimeSecs = (Date.now() - start) / 1000;
opsPerSec = ops / execTimeSecs;
console.log(`spread array of one element: ${opsPerSec.toFixed(2)} ops/sec`);
