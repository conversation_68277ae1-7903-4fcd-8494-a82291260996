# Array Concat vs Spread

What is more performant in Node.js 20 when wanting to concatenate two arrays? `Array.concat` or the spread operator?

[Here is a simple script to find the answer.](arrayConcatVsSpread.js)

And this are the results: ![Test results](results.png)

So it seems that using the concat method is faster than the spread operator by a factor of 4 for concatenating two arrays. And for concatenating one element to an array, they are about the same. Interestingly if the one element is wrapped in an array. Then the concat method becomes almost 4 times faster than the spread operator.

## How to run the tests

```bash
node --expose-gc arrayConcatVsSpread.js
```

You can modify the size of the array of random numbers and the time it spends on each test using the `RAND_ARRAY_LENGTH` and `MAX_EXEC_TIME_MS` environment variables. The default values are 1,000,000 and 1,000 respectively.

```bash
RAND_ARRAY_LENGTH=10000000 MAX_EXEC_TIME_MS=10000 node --expose-gc arrayConcatVsSpread.js
```
