# Dynamic emails

[!NOTE] **Summary:** it's not possible to implement this in Google Apps Script.

Context: finding a way to simplify the interaction with external users through email (see [Jira issue](https://fxsolutions.atlassian.net/browse/BPA-945)).

Our preferred solution would be to send such emails through Google Apps Script (GAS) as it's our default platform for automation.

## What are dynamic emails

Dynamic emails are emails that can be updated after they are sent, for instance like Google Docs permissions emails.

Gmail, Outlook and other providers support dynamic emails through AMP (Accelerated Mobile Pages) for Email. AMP is a framework for creating interactive emails, it is based on HTML and CSS, but it is not a subset of them, it is a subset of AMP HTML.

## Research results

It is not possible to send dynamic emails using GAS due to limitations of the Gmail library it uses, as explained here https://stackoverflow.com/a/59654921/2920671:

> AMP for Email requires the AMP to be a separate part in a multipart/alternative MIME tree with text/x-amp-html as the Content-Type.

Therefore the only alternatives are:

- developing the system with Cloud Functions;
- rely on some other mailing service.

An example tutorial [here](https://github.com/varunon9/amp4email).

## Conclusions

Even if technically possible, I’d discourage the use of dynamic emails other than internal use within the company for the following reasons:

- emails are more complicated to construct, thus increasing the risk of malformation;
- some company may turn off dynamic email for the entire domain;
- dynamic emails might be not supported by the receiver’s provider/tool used.

I’d suggest in any case the use of hyperlinks, possibly styled, to increase understanding and compatibility.
