# User-Friendly Databases and Interfaces

## Introduction

In this document, we look into the different options we have found thus far for creating user-friendly databases and interfaces for other teams to maintain.

We need to find a tech stack/platform to create user-friendly automation systems for other teams to operate, maintain, and possibly extend. We will use the case of the KYC Matrix Data Extraction as an example.

In this case, we have been requested to create a portal where analysts can search for the requirements to fulfill in the onboarding of clients. The requirements are extracted from the KYC Matrix, which is a spreadsheet that contains the KYC requirements for all the different types of clients. But this spreadsheet cannot be used as a reliable database to build this portal on. So we need to migrate this data into a better database and create a portal to interact with it.

Moreover, this database will be maintained and extended by the FCC, KYC and Compliance teams, so it needs to be user-friendly enough for them to do so. We should not take ownership of the system as we lack the resources and know-how of the KYC, FCC and Compliance teams.

We foresee similar cases like this occurring in the future for other teams, where some teams may want a user-friendly system to automate some of their processes. So we need to find a tech stack/platform that allows us to create these systems and that is user-friendly enough for other teams to operate, maintain, and possibly extend. And that's what we discuss in this document.

We start laying out the data model and then we discuss necessary requirements that will probably be common to all the systems we create. Then we explore the different technologies that could be used to implement the project and the pros and cons of each one. We will also discuss the security measures that could be necessary to adopt to protect the data.

## Data Modeling

For modeling the data we used an Entity-Relationship Diagram (ERD), which is a type of diagram that shows the relationship between entities in a database. The entities are represented by boxes and the relationships between them are represented by lines. This can later be used to implement a database in any of the considered technologies. Given, some of this tools will allow us to simplify the model, but we will start with the most general one.

### General Model

![Alt text](../../assets/general-KYC-ER-model.png)

## Compliance Requirements

As long as we don't store clients' sensitive data, we shouldn't have to worry about any compliance requirements. However, we should still take some security measures to protect the data.

The minimum security measure we should take is to restrict access to the portal to Ebury employees, i.e. only users with an Ebury email address should be able to access the portal. Luckily, the chosen solution for the portal, Airtable, allows us to do this easily.

If we were to store clients' sensitive data, whether temporarily or permanently, we should take additional security measures, such as encrypting the data at rest and in transit and restricting access to the data to only those who need it.

## Technical Considerations

Here we explore the different technologies that could be used to implement the project and the pros and cons of each one. We will also discuss the security measures that will be necessary to adopt to protect the data.

### Technologies

We are going to use as the root of the system the database, so we will start by exploring the different database technologies that could be used to implement the project. And for each option, we will lay out the different front-end technologies that could be used to implement the portal.

#### RDBMS

RDBMSs (Relational Database Management Systems) are the most common type of database management systems. They are based on the relational model, which organizes data into one or more tables (or "relations") of columns and rows, with a unique key identifying each row. SQL is a language used to interact with the database, and it is used to create, read, update and delete data from the database.

This option includes all the SQL-based database management systems, such as MySQL, PostgreSQL, Oracle, etc. If this option is chosen, we will have to decide which specific DBMS to use.

##### Pros

- **Structure**: The data is highly structured in an RDBMS. It's stored in tables, making it easier to understand and use.
- **ACID Compliance**: RDBMSs are ACID (Atomicity, Consistency, Isolation, Durability) compliant, which ensures data reliability in transactional systems.
  > Not required for this project, unless it scales to a large number of users that will be **adding and modifying** data.
- **Data Integrity**: RDBMS offer robust data integrity capabilities through the use of primary and foreign keys.
- **Querying**: SQL is a powerful and standard language used for querying and manipulating data.
- **Reporting**: SQL databases are excellent for generating complex reports, as they can handle a wide range of query operations.
- **Security**: RDBMS often have mature and robust security features.
- **Community and Vendor Support**: Most RDBMS have been around for a long time, so there's a large community of users and extensive documentation. Many vendors also offer professional support.

Overall, RDBMSs offer a robust and mature solution for storing structured data.

##### Cons

- **Scalability**: SQL databases can struggle to scale horizontally (across multiple servers), although modern options like Google Cloud SQL/Spanner and PlanetScale are addressing this.
  > Not required for this project, unless it scales to thousands of users (unlikely).
- **Complexity**: RDBMS can be complex to design and maintain. They require regular maintenance like backups, tuning, updates, and patching. Although fully managed options like Google Cloud SQL can reduce the complexity.
- **Rigid Schema**: Changes to the database schema (structure) can be complex and time-consuming, which might be a challenge in an environment where rapid iteration is necessary. Unless we use PlanetScale, which facilitates migrations and schema changes.
- **Handling of Unstructured Data**: RDBMS are not well-suited to handle unstructured data, such as images, audio, video, social media posts, etc.
  > Not required for this project.
- **Usability**: RDBMS can be difficult to use for non-technical users, as they require knowledge of SQL. This would force us to create views and forms to make the system easier to use and **maintain**.

##### Cost

We are going to use Google Cloud SQL, PlanetScale, Google Cloud Spanner, and AlloyDB as the reference for the cost of the RDBMS option. We will ignore the free tiers, as the chosen platform will probably be used in many other projects, and we will need to pay for it at some point.

- **Cloud SQL**:

  - Number of instances: 1
  - Location: Belgium
  - Total hours per month: 521.4
  - Instance type: db-f1-micro USD 5.48
  - SSD Storage: 10.0 GiB USD 1.70 (minimum)
  - Backup: 1.0 GiB USD 0.08
  - **Total: USD 7.26**

- **Cloud Spanner**:

  - Commitment: 1 year (minimum)
  - Spanner processing units: 100 USD 52.56 (minimum)
    - Committed Use Discount applied
  - Storage: 1 GiB per month USD 0.30
  - Backup: 1 GiB per month USD 0.10
  - Region: Belgium
  - **USD 52.96**

- **AlloyDB**:

  - Primary instance: CPU: 2 - RAM: 16 GB USD 250.02
  - Regional cluster storage: 1 GB USD 0.33
  - Backup storage: 1 GB USD 0.11
  - **USD 250.46**

- **PlanetScale**:
  - Scaler Tier: **29.00 USD/month**
    - 10 GB storage
    - 100 billion row reads/month
    - 50 million row writes/month
  - From then onwards:
    - 2.50 USD per GB/month
    - 1.00 USD per billion row reads/month
    - 1.50 USD per million row writes/month

In conclusion, we can **dismiss Cloud Spanner and AlloyDB**, as they are too expensive for this or almost any project for our team. Cloud SQL is a great option to keep costs to a minimum, but it is not as flexible as PlanetScale, which can automatically scale up and down as needed.

For this specific case, we could consider using Cloud SQL, as the data is not going to change much, and it is not going to be accessed by many users at the same time.

##### RDBMS Front-End

If we choose to use an RDBMS, we would be forced to create our own front-end to interact with the data. We could use a framework like React or Vue.js to create a web application, or simply use HTML, CSS, and JavaScript to create a static website.

An alternative solution would be to connect the database to a CMS (Content Management System) like WordPress, or a headless CMS like Strapi, and use that as the front-end. This would allow us to create a website with a user-friendly interface to interact with the data, without having to create it from scratch.

#### Relational Tables Platforms

This will include AppSheet databases and Airtable. They provide a very intuitive interface, similar to spreadsheets, to create and manage relational tables. They allow the creation of consistent and robust databases without the need to know SQL.

They also provide a very intuitive interface to create forms and views to interact with the data.

##### Pros

- **Ease of Use**: These platforms have user-friendly interfaces that make it easy for non-technical users to interact with the data, even if they don't know SQL or database design.
- **Flexibility**: They are highly flexible and allow for rapid prototyping, changes, and iterations.
- **Collaboration**: These platforms provide excellent collaboration features, allowing multiple users to work on the data at the same time.
- **Integration**: They often offer integration with other platforms and services, like Google Sheets, Slack, or various APIs, which can help streamline workflows.
  - A good example is the webhooks they provide for AppSheet and Aritable, especially for the latter, as it allows us to create custom webhooks.
- **Scalability**: They usually handle scaling automatically, which can be a big advantage for rapidly growing datasets.
- **Maintenance**: These services typically handle all the maintenance and infrastructure management, freeing up your time to focus on the data and application logic.

##### Cons

- **Complex Queries**: They don't typically handle complex queries or advanced data manipulation as well as a traditional RDBMS.
- **Cost**: While these platforms may be free or low-cost at a small scale, the cost can increase significantly as the amount of data or number of users grows.
- **Customization and Control**: You may have less control over your data and how it's stored, and there may be fewer options for customization compared to using your own RDBMS.
- **Unique Constraints**: None of the contemplated platforms allow the creation of unique constraints (beyond the autogenerated and hidden ID), which is a big disadvantage.
  - But this can be enforced through interfaces that check for duplicates before creating a new record. This is not ideal, but it is a workaround. <https://miniextensions.com/prevent-duplicates-in-miniextensions-form/>
- Performance: For large or complex datasets, performance might not be as good as a dedicated SQL database.
- **Data Security and Compliance**: Depending on your project, data security and compliance might be a concern. Some platforms might not meet all the requirements for handling sensitive or regulated data.

##### Cost

- Airtable:

  - Free tier: 1,200 records, 2 GB attachment space, 1 sync integration
  - Plus tier: 5,000 records, 5 GB attachment space, 3 sync integrations, **10 USD/month** (for 5 users)
  - Pro tier: 50,000 records, 20 GB attachment space, 7 sync integrations, **20 USD/month** (for 5 users)
  - Enterprise: 🤷

- AppSheet: 🤷
  - The pricing doesn't cover the use of the platform as a database, but as an app builder.

##### Relational Tables Front-End

Both of the options discussed here offer a very intuitive interface to interact with the data, so we wouldn't need to create a front-end from scratch.

We notice that the interfaces provided by Airtable are far easier to create and maintain than those provided by AppSheet. While AppSheet offers more customization options, it is also more complex to use. Airtable is also more flexible, as it allows us to create custom webhooks, which can be very useful for our project.

#### Conclusion

In conclusion, between a traditional RDBMS and a relational tables platform, we would choose the latter. They are easier to use, more flexible, and provide better collaboration features. Given that this project will be mostly maintained by non-technical users, this is a big advantage. And we can provide a lot more value in much less time.

Between Airtable and AppSheet, we can see that both options are very similar in terms of features and functionality. However, Airtable is more flexible and easier to use. The necessary training to get up and running with Aritable is significantly lower. But in case we need to create a more complex application, Airtable still allows us to do so through custom webhooks and their incredibly comprehensive API. So we will choose it as our solution for this project.
