# Google Apps Script IP Ranges

Sometimes you my need to whitelist the IP addresses of calls made from Google Apps Script to your API.

Unfortunately this information is quite hard to find, see [this StackOverflow question/answer](https://stackoverflow.com/a/66922436/2920671).

The list itself is available as a [json file](https://www.gstatic.com/ipranges/goog.json) or as a [text file](https://www.gstatic.com/ipranges/goog.txt).

For convenience, I've created a [Google Sheet](https://docs.google.com/spreadsheets/d/1dzGCx0xqBtCQRaRGu7llwhh9gi-z-S7aFg0CGt4lVDo/edit#gid=0) that, with a simple formula, generates in B1 a comma-separated list of the IP ranges, ready for copy-pasting.
