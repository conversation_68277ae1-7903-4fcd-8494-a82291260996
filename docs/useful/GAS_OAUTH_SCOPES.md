# Google Apps Script OAuth Scopes

In GAS usually you don't need to specify the scopes explicitly. But if you want to use some advanced services, you have to do it.

## How to specify the scopes

In the manifest file you can specify the scopes in the `oauthScopes` property. The scopes are specified as an array of strings.

```json
{
  "oauthScopes": [
    "https://www.googleapis.com/auth/drive",
    "https://www.googleapis.com/auth/script.external_request"
  ]
}
```

> **Note:** The scopes are not automatically added to the manifest file when you enable the advanced services, you have to add them manually. So for example if you enable the Drive API, you have to add the `https://www.googleapis.com/auth/drive` scope to the manifest file, even if it was already enabled in the Apps Script editor.
>
> Here is a list of Google OAuth Scopes for reference: <https://developers.google.com/identity/protocols/oauth2/scopes> (Not all possible scopes are listed), but the specific scope will be listed in the documentation of the service. For example, see this [Secret Manager API documentation](https://cloud.google.com/secret-manager/docs/reference/rest/v1/projects.secrets.versions/list#authorization-scopes).
