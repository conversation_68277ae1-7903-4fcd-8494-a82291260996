# Managing secrets

This document describes how to manage secrets in your project.

## Google Secrets Manager

The [Google Secrets Manager](https://cloud.google.com/secret-manager) is our preferred way to manage secrets. It is a fully managed service that enables you to store, access, and manage secrets.

Any secret that relates to any of our team's projects should be stored in the Google Secrets Manager. This includes:

- Keys: API keys, OAuth client IDs, OAuth client secrets, etc.
- Credential: database credentials, service account credentials, usernames/passwords, etc.
- Certificates: SSL certificates, etc.

## Principles

Follow these principles when working with secrets:

- **Always use our [default Google Cloud project](https://console.cloud.google.com/security/secret-manager?project=appscript-296515)**: all secrets should be stored in our default Google Cloud project. This way, we can easily manage them and we can easily grant access to them to other team members. In case multiple versions of a secret are needed, they should be stored in the same project.
- **Use descriptive names**: secrets should have descriptive names. For example, if a secret is a database password, it should be named `database-password` instead of `password`; there's no need to keep the name short, better to keep it as clear as possible. Possibly, add a description to the secret as well.
- **Add labels to identify the related projects**: add labels to the secrets to identify the projects that use them. For example, if a secret is used by the `offboarding-routines` project, add the `offboarding-routines` label to it. This way, we can easily identify which projects use which secrets.
- **What changes together, stays together**: if a secret is composed by multiple parts, they should be stored together. For example, if a secret is composed by a username and a password, they should be stored together. This way, if one of the parts changes, the other part will change as well. The standard way to store multiple parts is to use a JSON object.
- **Only one active version**: only one version of a secret should be active at any given time, and it should be the `latest` version. In this way we can keep within the free tier, and avoid confusion as well. Other versions should be deleted (not just disabled, as they will still count towards the free tier).

> Beware! Secrets are sensitive information and should be treated as such!

- **Never** commit secrets to the code repository.
- **Never** hardcode secrets in the code.
- **Never** share secrets via email, Slack, etc; use [YoPass](https://yopass.ebury.rocks/) instead.

## Adding a new secret to the Google Secrets Manager

To add a new secret to the Google Secrets Manager, follow these steps:

1. Go to the [Google Secrets Manager](https://console.cloud.google.com/security/secret-manager?project=appscript-296515) page.
2. Click on the `Create Secret` button.
3. Input the secret's name and description [see the principles above](#principles).
4. Input the secret's value.
5. Add the labels to identify the related projects [see the principles above](#principles); if the secret is used by multiple projects, add all of them; you can input just the label's key, no need to input the value.

## Accessing secrets from the Google Secrets Manager

As we leverage the Google Cloud Platform, we can access the secrets in a simple and secure way in our projects. It depends on the platform we are using.

### Google Apps Script

To access the secrets from a Google Apps Script project, use the [fetchSecret module](../../src/packages/modules/fetch-secret/). Follow the instructions in the module's README.

### Google Cloud Run

The secrets are automatically injected into the environment variables of the Google Cloud Run service. In local, our build scripts take care of it, fetching them from the Google Secrets Manager and injecting them at runtime.

For this to happen correctly:

- you must be logged in locally using `deno task login` with your personal user account;
- you must specify the required secrets in the package.json of the process in the `deploymentOptions.environmentVariables`, correctly pointing for each stage to the appropriate secret name.

> it's a recommended practice to add [`checkEnvVars`](../../src/packages/modules/check-env-vars/) in the root of any file that requires such secrets to ensure proper setup.
