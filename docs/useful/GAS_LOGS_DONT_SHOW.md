# GAS logs don't show

## Problem

You are using the [Google Apps Script](https://developers.google.com/apps-script/) (GAS) environment and you are trying to debug your code, but you can't see any logs in the executions console within GAS. You have tried to use the `Logger.log()` function, but nothing shows up in the console. You have also tried to use the `console.log()` function, but nothing shows up in the console.

## Solution

The GAS environment is a bit tricky to debug. If you are debugging a WebApp you will notice that sometimes you don't get any of the logs from the `doGet()` and `doPost()` functions (especially the `doPost()`).

In this case, you should be able to go to the [Cloud Logging Console](https://console.cloud.google.com/logs/viewer) and see the logs there. You can filter the logs by the `resource type` and select `Apps Script Function` and you should be able to see the logs there.

You can also click on the `VIEW IN CLOUD LOGGING` button on any execution log of any GAS function:

![Alt text](../assets/gas-cloud-logging.png)

In case you are not debugging any web app, you can still use the Cloud Logging Console to see the logs. But there may be another unknown problem, as we haven't encountered this situation yet.
