# POC Guidelines

This document outlines the guidelines for creating a Proof of Concept (POC) for a new feature or bug fix.

## Context

These guidelines stem from our previous experience with POCs, and are intended to make the process of creating and evaluating a POC as smooth as possible. Before, in fact, we used to do POCs as separate repositories; but now, as we have a monorepo, the development in a separate fashion can be cumbersome, as a lot of environment setup is needed, and porting could be difficult without any gain. Instead, we can leverage the monorepo structure, and create a POC as a branch of the main repository, and then, if the POC is evaluated as feasible, create a new branch from the POC branch, and proceed with the development as usual.

## What is a POC?

A POC is a small, self-contained piece of code that demonstrates a new feature or bug fix. It is not intended to be a complete solution, but rather a way to demonstrate the feasibility of a solution.

## When is a POC needed?

A POC is needed when a feature or bug fix is not trivial and the developer is not sure how to proceed. It is also needed when the developer is not sure if the proposed solution will work.

## What is the process for creating a POC?

> Important: also POCs should follow the [local contribution guidelines](../../CONTRIBUTING.md), as well as the [general contribution guidelines](https://github.com/Ebury/ebury-blueprints/blob/master/CONTRIBUTING.md).

1. Create a new branch for the POC, following the usual conventions; create and test the POC, committing as usual;
2. At the moment of creating the PR, add the label `POC` to the PR, and set the `draft` flag, and request review from the @Ebury/BPA team;
3. Once the POC is approved, there are 2 possible scenarios:
   1. The POC is evaluated as not feasible: the PR is closed, and the branch is deleted;
   2. The POC is evaluated as feasible: the PR is closed, but the branch is **NOT deleted**; follow the guidelines for a [POC spin-off](#poc-spin-off-guidelines).

### POC spin-off guidelines

A POC spin-off is a new branch created from the POC branch, that will contain the actual implementation of the feature or bug fix, and will be merged into the `main` branch.

In case a POC is evaluated as feasible, the following steps should be followed:

1. Create a new branch from the POC branch, following the usual conventions;
2. Delete the POC branch;
3. Proceed working on the new branch, following the usual guidelines, resulting in a PR that, once approved, will be merged into the `main` branch.
