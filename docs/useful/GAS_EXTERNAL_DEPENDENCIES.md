# GAS external dependencies, how to handle them and avoid issues

Currently to bundle GAS projects we leverage esbuild. It works fine for most cases, but there are some cases where it can be problematic.

2 issues are detected:

- importing dependencies specified in the root `imports.json` file
- importing dependencies using `npm` or `jsr` specifiers.

These issues are due to esbuild being unable to resolve such imports.

> Currently the team is in the process of consolidating the processes into Cloud Run using Deno, and given the very limited involved processes, it's agreed that fixing this issue is not worth the effort.

To avoid this, you can import directly the packages from urls, for instance:

```ts
// substitute
import { match } from "ts-pattern";
import { match } from "npm:ts-pattern";

// with
import { match } from "https://esm.sh/ts-pattern@5.5.0";
```

Some things to keep in mind:

- when writing the code, <PERSON><PERSON> will not complain if you use `npm` or `jsr` specifiers, but esbuild will fail;
- most of the time changig `npm:` to `https://esm.sh/` is enough;
- it's good practice to add the version specifier, for instance `https://esm.sh/ts-pattern@5.5.0`; <PERSON><PERSON>'s VSCode extension will show a blue highlight under the line, and hovering and clicking "Quick fix" will propose to update to the most recent version specifier;
- it's good practice to specify such dependencies in a `deps.ts` file in the process's folder, and import them from there;
