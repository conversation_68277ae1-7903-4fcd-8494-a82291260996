# How to call a GAS WebApp from a GAS Project

## Introduction

This document explains how to call a GAS WebApp that has restricted access to `only myself` from a GAS Project.

## Prerequisites

You need to execute the caller script with the same account that is executing the WebApp. In our case, this should be `automationadmin`.

## Steps

Given that you already have the WebApp ready and the caller script ready, you need to follow these steps:

1. Mirror the scopes from the WebApp. All the scopes used in the WebApp must also be used in the caller script. These scopes can be found in the `appsscript.json` file of the WebApp. If the scopes were not set explicitly in the WebApp, you will have to add them now.
2. Set special scopes to perform the call. There are some specific scopes that need to be added to the caller script in order to perform the call. These scopes are:
   - `https://www.googleapis.com/auth/script.external_request`
   - `https://www.googleapis.com/auth/script.scriptapp`
   - `https://www.googleapis.com/auth/drive.readonly` (if the WebApp is already using this or a more permissive scope, you don't need to add it)
3. Add the `Authorization` header to the fetch options. This header must contain the OAuth token of the account that is executing the WebApp. In our case, this is `automationadmin`. The token can be obtained by calling the `getOAuthToken()` method of the `ScriptApp` class. Like so:

   ```javascript
   const options = {
     method: "GET",
     headers: {
       Authorization: `Bearer ${ScriptApp.getOAuthToken()}`,
     },
   };
   ```

## Exceptions

In some cases, e.g. when the caller script is linked to a spreadsheet, you will need to share the WebApps script with the user that is trying to make the call. This won't happen to scripts that are only meant to be executed by `automationadmin`. But for the case of the caller script linked to a spreadsheet, the user that is trying to make the call will be the user that is executing the spreadsheet, not `automationadmin`. So, in this case, you will need to share the WebApp script with the user that is executing the spreadsheet. And the WebApp will have to provide access to either `anyone` or `anyone within the organization` or `anyone with a Google account`. This is because the user that is executing the spreadsheet is not `automationadmin`, so the WebApp won't be able to restrict the access to `only myself`.
