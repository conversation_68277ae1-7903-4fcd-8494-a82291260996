# Automation technical training material

## Languages / Frameworks

### Javascript

- Airbnb JavaScript Style Guide - https://github.com/airbnb/javascript

### Typescript

- ⭐ Be<PERSON><PERSON>'s TypeScript [<PERSON>] - https://www.totaltypescript.com/tutorials/beginners-typescript
- TypeScript path [Pluralsight Skills] - https://app.pluralsight.com/paths/skill/typescript

### Vue.js

> Vue is currently used in the periodic reviews interface in order to comply with the company's frontend guidelines, that are now obsolete.

- Vue.js 3 Documentation - https://vuejs.org/v2/guide/
- Vue.js 3 Intro video course - https://www.vuemastery.com/courses/intro-to-vue-js/vue-instance/
- Vue.js 3 Fundamentals - https://vueschool.io/courses/vuejs-fundamentals
- Vue.js 3 Components Fundamentals - https://vueschool.io/courses/vue-js-3-components-fundamentals

### Puppeteer

- RPA - Puppeteer Docs - https://pptr.dev/category/introduction

### Suggested:

> These languajes / framework are not used directly by the Automation Team, but are the default for the rest of Ebury Tech, so feel free to expand your knowledge in this regard.

#### Python

##### Django

- SECO as example - https://github.com/Ebury/ebury-release

#### React

- React Documentation - https://react.dev/learn

##### Material Design

- Material 3 - https://m3.material.io/foundations
- Material UI - https://mui.com/material-ui/getting-started/

## Platforms

### GCP

- Overview of Google Cloud [A Cloud Guru] - https://learn.acloud.guru/course/66ef2605-0227-437d-81c4-2e3b3b3d60ff/dashboard
- Google Cloud Skills Boost https://www.cloudskillsboost.google/

### Google Apps Script

- Google Apps Script quickstart - https://developers.google.com/classroom/quickstart/apps-script
- Advanced Google services - https://developers.google.com/apps-script/guides/services/advanced

### Deno

> Deno deploy has been accepted as third party, but we aren't using it for consistency with existing processes hosted on GCP and to avoid storing secrets anywhere else than google secret manager and github (for CI).

- Deno Docs - https://docs.deno.com/
- Deno 1: Getting Started - https://app.pluralsight.com/library/courses/deno-getting-started/table-of-contents
- Deno Deploy - https://deno.com/deploy

### Make.com

- Make academy - https://academy.make.com/

### Git / GitHub

- Copilot for GitHub - https://github.com/features/copilot - https://docs.github.com/en/copilot
- VSCode Extension for GiHub Copilot - https://marketplace.visualstudio.com/items?itemName=GitHub.copilot
- GitHub Copilot Labs - https://githubnext.com/projects/copilot-labs/
- GitHub Pull Request Reviews - https://github.com/skills/review-pull-requests
- https://github.com/education

## Third parties

### Slack

- Video tour through Slack features - [Start here](https://slackdemo.com/?_gl=1*343a01*_gcl_au*MTA3MTExMzE3Mi4xNzI4MjkzMjMx*_ga*NjU5NDU4MzMyLjE3MjgzMTEwNzM.*_ga_QTJQME5M5D*MTczMDI4MDc5NC43LjEuMTczMDI4MDc5NC42MC4wLjA.)

### Jira / Atlassian

- Atlassian Software products overview - https://www.atlassian.com/software
- Jira Features - https://www.atlassian.com/software/jira/features
- Jira Product Guide - https://www.atlassian.com/software/jira/guides/getting-started/introduction#what-is-jira-software

### HiBob

- Bob HR platform features - https://www.hibob.com/features/

### Salesforce

- Trailhead [Salesforce official training platform] - https://trailhead.salesforce.com/es/

### FenX / Fenergo

- Introduction to Fenergo SaaS Platform - https://docs.fenergox.com/user-guides/the-fenergo-saas-platform/introduction

### Vonage CC

### Netsuite

### Looker

### OneTrust

### Barx / Citi (Liquidity providers)

- BARX - Barclay's solution for foreign exchange (FX) dealing - https://www.barx.com/ms/barxcorporate.html
- Citi - One of largest banks with huge international presence - https://www.citi.com/online-services/foreign-currency-exchange

### Backupify

- Data retention for Google Workspace - https://www.backupify.com/data-retention/

## Processes

### Onboarding

- https://sites.google.com/ebury.com/tech-intranet/teams/services/bpa/onboarding-automation

## Soft skills

### Communication

- Communication Skills for Technologists - https://app.pluralsight.com/paths/skill/communication-skills-for-technologists

### Agile / Project Management
