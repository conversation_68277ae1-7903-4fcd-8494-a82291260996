# API Contracts

In this document we will describe the standard way for a process to response to a request. This is a contract that all processes should follow.

## Interface

All responses should be a JSON containing a `success` field. Other fields like `status`, `message`, or `error` are encouraged.

```typescript
interface Response {
  success: boolean;
  status?: number;
  message?: string;
  error?: string;
}
```

### Success

If a process is successful, it should return a response with `success` set to `true`.

```json
{
  "success": true
}
```

A process is successful if it follows any of the expected flows. If a process is expecting some possible exceptions and that exception occurrs as expected, the process is still successful.

Only if an unexpected exception occurs or an expected exception is managed incorrectly, the process is not successful.

### Status

The `status` field is optional. If it is present, it should be an HTTP status code.

```json
{
  "success": true,
  "status": 200
}
```

### Message

The `message` field is optional. If it is present, it should be a string.

```json
{
  "success": true,
  "message": "The process was successful"
}
```

### Error

The `error` field is optional. If it is present, it should be a string. `error` is a special field that should only be present if the process is not successful.

```json
{
  "success": false,
  "error": "The process was not successful"
}
```
