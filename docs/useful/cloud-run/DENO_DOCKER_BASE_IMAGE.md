# Deno base image for Cloud Run

> Important: currently we are not using this image, as we're compiling the code with <PERSON><PERSON>. But, as the images are quite big, we're evaluating using our own base image. Still, previous attempts to bundle the code had issues, while compiling was successful and easy to implement. In the end, let's keep this doc here for future reference as it might become relevant soon.

> The base image is not meant to be changed that often, actually this should be a one-off task. In case additional config is needed, the specific project should extend this base image and add the additional configuration.

## Setup

### Requisites

Tools:

- [gcloud CLI](https://cloud.google.com/sdk/docs/install)
- [Docker CLI](https://docs.docker.com/get-docker/)

Actions:

- [Login to GCP](https://cloud.google.com/sdk/docs/authorizing)
- Configure Docker to use Artifact Registry, see below.

## Build and tag image

In the `package.json` file there are the CLI commands needed:

- `build-image`: Builds the Docker image;
- `setup-docker-for-gcp`: Configures Docker for GCP (maybe this can be set up in a config file?);
- `upload-image-to-artifact-registry`: Pushes the image to the Artifact Registry.
