# Google Cloud Jobs Scheduler

There are some of our processes that we need to launch automatically and are not triggered by other processes or actions.

In this case, we use the Cloud Scheduler of GCP.

> Nowadays, processes are triggered by other platforms or processes that make the HTTP call, and we are implementing Pub/Sub topic events, so this will cover the rest of the cases.

## Pre-requisites

- The process must first be deployed in Cloud Run, as the HTTP endpoint will be needed to schedule the job.

- Access to [Google Cloud Console](https://console.cloud.google.com/welcome) and look for [Cloud Scheduler](https://console.cloud.google.com/cloudscheduler) or install the [gcloud CLI](https://cloud.google.com/sdk/docs/install#deb).

> The same jobs can be done in both ways. We are currently using the Cloud Console but looking forward to using the `gcloud` CLI to automate the job scheduler creation.

## Create a Job using Cloud Scheduler Console

### 1. Once in the console click on `+Create Job`

### 2. Define the schedule:

- **Name**
- **Region** -> `europe-west1 (Belgium)`
- **Frequency**: Schedules are specified using unix-cron format:

  ![](../../assets/cron-job-format.png)
- **Time Zone** -> `Central European Standard Time (CET)`

### 3. Configure the execution:

- **Target type** -> `HTTP`
- **URL** -> URL endpoint of the process
- **Auth header** -> Add OIDC token
- **Service account** -> currently using `Default compute service account`
- **Audience** -> same URL endpoint to trigger the process

### 4. Configure optional settings:

> These settings are optional to configure if a job does not complete successfully, whether it is retried, the maximun retry attemps, or the exponential backoff.
