# Import GraphQL files in code

> NOTE: this is tested only for GAS bundling as that necessity arose, it might not work for Cloud Run building.

Currently Deno does not support imports other than `.ts` and `.tsx` files, and `json` when specifying `{ type: "json" }`.

So,when we need to import GraphQL files in code, we need some small workaround.

For example, if we need to import the `organisationQuery` we can do this:

```ts
// deno-lint-ignore ban-ts-comment
// @ts-ignore
import organisationQuery from "modules/github/queries/organisation.graphql";
```

- we import the text and add some comments to disable linting/type checking errors;
- the code will be bundled by esbuild using a specific graphql plugin, that will properly parse the GraphQL file;
- the produced code will work properly, and we can leverage graphql file syntax and editor extensions.
