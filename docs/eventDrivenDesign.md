# Event-Driven architecture Basics.

## Why Event-Driven?

You can find some theory about Event-Driven architecture here. Feel free to add more or correct whatever is wrong

## Orchestration versus choreography

### What is Orchestration?

Orchestration means the automated setup, coordination, and management of complex computer systems, middleware, and services. In the context of microservices, orchestration involves a central controller or orchestrator that manages how different services interact with each other. This central entity decides the order of execution and handles all communication, making sure each service does its job in the right sequence.

![orchestration diagram](./eventDrivenAssets/orchestration.jpg)

### What is Choreography?

Choreography, on the other hand, is a decentralized approach where each service works independently and communicates with other services through event-based messages. There is no central controller; instead, each service knows what to do and when to do it based on the messages it receives. This method depends a lot on predefined rules and protocols for interaction.

![choreography diagram](./eventDrivenAssets/choreography.jpg)

### Characteristics of Choreography

Decentralized Control: No single component has control over the entire process. Event-Driven: Services communicate by emitting and listening to events. Flexible and Adaptable: Each service operates independently, allowing for more flexibility and adaptability. Complex Error Handling: Error management can be more complex due to the lack of a central controller.

### Orchestration Use Cases and Applications

Orchestration is particularly useful in scenarios where a clear and explicit workflow is necessary. Some common use cases include:

- Business Process Management: Orchestration is best for automating complex business processes that need a specific sequence of operations.
- Service Composition: When combining multiple services to create a single service, orchestration ensures each service is called in the correct order.
- Batch Processing: Managing large-scale batch jobs that need a series of dependent tasks can be efficiently handled with orchestration.
- Resource Provisioning: Automating the setup of resources in cloud environments can benefit from the centralized control provided by orchestration tools.

### Choreography Use Cases and Applications

Choreography shines in environments where services need to operate independently while still collaborating through events. Common use cases include:

- Event-Driven Architectures: Systems that use event-driven patterns, like real-time data processing or IoT applications, benefit from the loose connection provided by choreography.
- Microservices Communication: In microservices architectures where services need to stay independent and autonomous, choreography allows for more flexible interactions.
- Agile Development: Fast development and deployment cycles can use choreography for its flexibility and fewer dependency issues.

### How our onboarding and offboarding services will benefit from choreography

Let's analyze the nature of the onboarding and offboarding services. If we take a closer look at them we will shortly notice they are a conglomerate of smaller services (we call processes) that are highly independent to the point we could almost talk about microservices refering to them. There are just a few of them that depend on other processes making a good case for a parallelized approach to its design.

Another interesting property for choosing this type of architecture is how decoupled the inputs to the system are. That means there is no relation between executions of the onboarding or offboarding systems. If the onboarding runs for employee x and then is triggered for employee y there is no relation between the two executions. The lack of necessity for batch processing or handling complex relations between data makes it also very suitable for using an event-driven approach.

The last thing we are going to talk about is tackling complexity. With a quick look at the onboarding or offboarding systems you will notice the amount of overhead and patches we have in place to try to make it robust. Most errors we get are related to timeouts, or overloaded services like google sheets leading to more timeouts. Taking an event-driven approach to the system and implementing it in a platform like pub/sub allows us to have retry policies and handling of the failed messages automatically leading to a more robust system in which we will only be notified by real errors in the processes.

## How a message queue works.

![message queue diagram](./eventDrivenAssets/1_CregIuyLWrxzaGK2_cdEVA.png)

So what makes a message queue so special? Let's find out.

To understand a message queue let's introduce a couple concepts:

- Topics: a topic is a channel in which messages talking about the same subject are written and read. Imagine a factory with assembly lines, where the workers put the stuff being fabricated so it can arrive at the next step of the line. That is a topic.

- Message: a message is a communication item.

- publishers and producers: as in every communication there are people talking and people hearing and they change roles thousands of times per conversation. Well here is exactly the same: there are processes that produce messages about a topic and there are other processes that are listening to that topic.

Now let's run over how two or more processes would communicate using a message queue.

- First the producer posts a message into a topic.

- Then all processes subscribed to that topic are called with the message the producer posted.

That's all, the message queue service takes care of calling the subscribers with the message posted and also makes sure that all of them acknowledge the message (this means they have received it correctly).

## And what happens if a message can not be delivered?

That's a really good question and until now we have only explored the happy path. There are some processes posting messages in topics and there are other ones listening to them and reacting to the messages. But what happens if a message can not be delivered to a subscriber for example because of an outage in the process or connection is rejected? Well in this case using a queue service is handy too. The message queue services ensure no message is lost and have a couple of strategies for message delivery.

- Retries: You can configure automatic retries so if delivering a message to a subscriber fails it will retry again. This can be configured using instant retry or with an exponential back off algorithm.

- Dead letter topics: This is a special topic in which all undelivered messages are sent. As it's a topic you can subscribe processes to it creating processes that react to undelivered messages (for example a notification service, report generation or whatever you would want to do with these messages).

![Dead letter queue diagram](./eventDrivenAssets/Dead-Letter-Queue.jpg)

## Disadvantages of using message queues.

As you can imagine it is not always worthy to implement message queues and we are going to explore briefly why here.

- Additional complexity and set up: For small services it is not worthy to set up messages queues as they are too small and it's not worthy.

- Processes tightly coupled: If two services are coupled then it's not worthy to introduce a message queue in the middle for example for example services that are design to work together instead of one after another (or in parallel). An example of this could be an API and a front-end.

## Useful links.

For more information about message queues and other topics here are some links:

- [orchestration vs choreography](https://harish-bhattbhatt.medium.com/distributed-workflow-in-microservices-orchestration-vs-choreography-cf03cfef25db)
- [the big little guide to message queues](https://sudhir.io/the-big-little-guide-to-message-queues)
- [pub/sub documentation](https://cloud.google.com/pubsub/docs)
