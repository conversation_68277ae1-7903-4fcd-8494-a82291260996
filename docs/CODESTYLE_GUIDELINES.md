# Code Style Guidelines

> Important: the [general contribution guidelines](https://github.com/Ebury/ebury-blueprints/blob/master/CONTRIBUTING.md) should be followed in this repository as well.

We also use [AirBnB style guide](https://github.com/airbnb/javascript) as a general reference.

Also, we follow some specific guidelines that we agreed to follow in this repository with the aim of standardizing the code and making it more readable.

## Table of Contents

- [Code Style Guidelines](#code-style-guidelines)
- [Explicit conditions](#explicit-conditions)
- [For type guards use Zod](#for-type-guards-use-zod)
- [Define schemas in a separate file](#define-schemas-in-a-separate-file)
- [Test File Structure](#test-file-structure)
- [Namespaces](#namespaces)
- [Abbreviations in Variable Names](#abbreviations-in-variable-names)

## Explicit conditions

Conditions in `if` statements should be explicit, only the `!` is allowed as a negation operator.

This enhances clarity of intention, and easy comparison with implementation, making the code way quicker and easier to read, review and debug.

```typescript
// ❌Instead of this
if (arr.length) {
  // ...
}
// ✅Do this
const isArrEmpty = arr.length === 0;
if (isArrEmpty) {
  // ...
}

// ❌Instead of this
if (a > b) {
  // ...
}
// ✅Do this
const isAGreaterThanB = a > b;
if (isAGreaterThanB) {
  // ...
}

// ✅Guard clauses are allowed
if (!isAGreaterThanB) {
  return;
}
```

### For type guards use Zod

[Zod](https://github.com/colinhacks/zod) is a TypeScript-first schema declaration and validation library. It is used to validate data and to type guard.

Zod is available as a root-level dependency in this repository.

```typescript
// ❌Instead of this
if (
  typeof value === "object" &&
  "name" in value &&
  typeof value.name === "string"
) {
  // ...
}
// ✅Do this
import { z } from "zod";

const schema = z.object({
  name: z.string(),
});

const isObjectValid = schema.safeParse(value).success;
if (isObjectValid) {
  // ...
}
```

## Define schemas in a separate file

As a good practice, define the schemas in a separate file (like we do with types) and infer the type from them always.

So have schemas.ts or within the types.ts define the schemas, infer the types, and import them in any other file.

This will not only keep the code compact and easier to read, but as well will make easier to re-use the schemas.

## Test File Structure

To ensure clarity and maintainability, all test files should reside within a dedicated folder named `tests`. Furthermore, every test file must adhere to the `.test.ts` naming convention.

**Example:**

Consider a process called `a-dummy-project`. The corresponding test file should be located at `a-dummy-project/src/tests/example.test.ts`.

## Namespaces

TypeScript offers namespaces to organize code; while their use is generally discouraged in modern JavaScript and TypeScript development, in our case is a convention we're adopting, for different reasons:

- **bare imports mistaken imports**: given our monorepo setup, we have similar functions (f.i. `getUsers`) in different modules; using bare exports bears the very big risk of mistakenly import a function from the wrong module without realising (f.i. using IDE's suggestion) and to be catched in the review;
- **convenient encapsulation**: namespaces can export both identifiers (variables, functions) and types, avoiding having to duplicate imports and ensuring their cohesion;

### _Public_ and _private_ exports

Currently we don't have a way in the monorepo to restrict exports that are meant to be used only within a certain context (f.i. utility functions of a module). Therefore, we've aggreed to a convention we're calling of _public_ and _private_ exports:

- _public_ exports should be the functions that are meant to be used;
- _private_ exports should be the functions that are meant to be used only within a certain context, and should be prefixed with an underscore to make it immediatly clear that they are private;

**Example**

```ts
import { _getUsers } from "./_getUsers.ts";

export namespace MyModule {
  export const getUsers = _getUsers;
}
```

> NOTE:a potential solution to this would be [conditional exports](https://nodejs.org/api/packages.html#conditional-exports), but this requires investigation and, potentially, a refactor of the whole `modules` folder.

## Abbreviations in Variable Names

For variable names that include well-known abbreviations (e.g., GCP, URL, ID), we recommend the following approach:

- **Use camelCase:** Follow the standard camelCase naming convention for variables in JavaScript and TypeScript.
- **Treat as a "Word" with Standard Acronym Casing:** When incorporating an abbreviation, treat it as a single "word" within the camelCase structure, respecting common casing for acronyms. For example:
  - `liveGcpRegion` (for "live Google Cloud Platform Region")
  - `userId` (for "user identifier")
  - `productUrl` (for "product Uniform Resource Locator")
  - `apiEndpoint` (for "Application Programming Interface endpoint")

**Examples:**

```typescript
let liveGcpRegion: string = "us-central1";
const userId: number = 123;
const productUrl: string = "[https://example.com/product/1](https://example.com/product/1)";
```
