# Architecture

This document describes the architecture of the Business Process Automation (BPA) monorepo, and common logics and patterns used across the different processes.

![](./assets/the-monorepo.png)

## The core principles

The BPA monorepo is built on top of the following principles:

- **One-stop shop** - the goal of this mono-repo is to have everything in one place: the code, the documentation, the configuration, the tests, the build scripts, the CI/CD pipelines, and everything else; this should improve knowledge sharing (and therefore productivity) and make it easier to manage the code;
- **Everything is a package** - we should strive to build code that is as modular as possible, and that can be reused across different processes;
- **Shortest path to knowledge** - the code should be as self-explanatory as possible, and the documentation should be as short as possible, to make it easier to understand what's going on; the documentation should also live as close as possible to the code (in the form of READMEs, js-docs, comments), to make it easier to find.

> Given these principles you are encouraged to explore the code and the documentation (ideally, there should be a README in almost every folder), and to ask questions if you don't understand something.

## Structure

The folder structure of the BPA monorepo is as follows:

- `src` - Contains all the code, as well as the related configuration files (`package.json` and others) and documentation;
- `docs` - Contains additional documentation, like this one, that doesn't relate directly to the code or it's generally useful and therefore gets higher visibility;
- `.vscode` - Contains the configuration for Visual Studio Code, like the recommended extensions and the launch configurations;
- `.github` - Contains the configuration for GitHub, like the workflows for the CI/CD pipelines (it would be nice to move this to the `src` folder, but GitHub requires it to be in the root of the repository).

## Stages

The development of a process is divided into stages, each stage having its own configuration and code.

The stages are:

- `development`: it is an optional step, and refers to a safe Google Workspace environment (eburypartners.com), that will be used in order to avoid major issues in the production environment.
- `staging`: a mandatory step, developed in the production Google Workspace environment (ebury.com) in order to test that all the authorizations are in place. Most of the time, this will be the starting point for the development of a new process.
- `production`: this is the final stage, where the process is deployed to the production environment and runs the actual business logic. In the future, this should be deployed automatically in the CI/CD pipeline, but for now it is deployed manually.
