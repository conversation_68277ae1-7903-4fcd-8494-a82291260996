{"$schema": "https://deno.land/x/deno/cli/schemas/config-file.v1.json", "compilerOptions": {"useUnknownInCatchVariables": false, "lib": ["dom", "esnext", "deno.ns", "deno.window"], "types": ["types/global-types.d.ts"]}, "nodeModulesDir": "none", "tasks": {"develop": "bash ./deno.sh run src/environment/build-scripts/develop.ts --exec-develop", "login": "gcloud auth application-default login", "develop-inspect": "bash ./deno.sh task develop --inspect", "test": "bash ./deno.sh run src/environment/build-scripts/test.ts --exec-test", "deploy": "bash ./deno.sh run src/environment/build-scripts/deploy.ts --exec-deploy", "format": "bash ./deno.sh fmt", "lint": "bash ./deno.sh lint", "ts-check": "./deno.sh run src/environment/build-scripts/type-check/type-check.ts --exec-ts-check", "ts-check-changed": "bash ./deno.sh run src/environment/build-scripts/type-check/type-check-changed.ts --exec-ts-check-changed", "ts-check-debug": "bash ./deno.sh run src/environment/build-scripts/type-check/type-check.ts --exec-ts-check --ts-check-debug", "generate-root-codeowners-file": "bash ./deno.sh run src/environment/build-scripts/generate-root-codeowners-file.ts generate", "validate-packages-folder-structure": "bash ./deno.sh run src/environment/build-scripts/validate-packages-folder-structure.ts validate", "validate-package-jsons": "bash ./deno.sh run src/environment/build-scripts/check-package-json.ts", "validate-json-schemas": "bash ./deno.sh run src/environment/build-scripts/validate-json-schemas.ts validate", "clean-dev-folders": "bash ./deno.sh run src/environment/build-scripts/utilities/clean-dev-folders.ts clean-dev-folders", "clean-dependencies": "bash ./deno.sh clean", "test-build-processes": "bash ./deno.sh test src/environment/build-scripts/tests/develop.test.ts -- --exec", "test-build-all-processes": "bash ./deno.sh test src/environment/build-scripts/tests/develop-all.test.ts", "test-build-changed-processes": "bash ./deno.sh test --no-check src/environment/build-scripts/tests/develop-all-changed.test.ts", "update-impact-metrics": "bash ./deno.sh run src/environment/build-scripts/update-impact-metrics.ts"}, "imports": {"@actions/core": "npm:@actions/core@^1.10.1", "@google-cloud/storage": "npm:@google-cloud/storage@7.16.0", "@octokit/rest": "npm:@octokit/rest@^21.0.1", "@octokit/types": "npm:@octokit/types@^12.0.0", "@std/assert": "jsr:@std/assert@1.0.7", "@std/async": "jsr:@std/async@^1.0.4", "@std/dotenv": "jsr:@std/dotenv@^0.225.0", "@std/fmt": "jsr:@std/fmt@^1.0.4", "@std/http": "jsr:@std/http@^1.0.7", "@std/testing": "jsr:@std/testing@1.0.8", "@std/collections": "jsr:@std/collections@1.0.9", "@types/jsrsasign": "npm:@types/jsrsasign@^10.5.14", "@aws-sdk/client-ssm": "npm:@aws-sdk/client-ssm@3.438.0", "@ai-sdk/google": "npm:@ai-sdk/google@1.2.15", "ai": "npm:ai@4.3.13", "big.js": "npm:big.js@6.2.2", "@types/big.js": "npm:@types/big.js@6.2.2", "esbuild": "npm:esbuild@^0.23.1", "google-auth-library": "npm:google-auth-library@9.10.0", "googleapis": "npm:googleapis@140.0.1", "googleapis-common": "npm:googleapis-common@7.2.0", "gaxios": "npm:gaxios@^6.7.1", "http-status-codes": "npm:http-status-codes@^2.3.0", "jsrsasign": "npm:jsrsasign@^10.9.0", "modules/": "./src/packages/modules/", "node-zendesk": "npm:@types/node-zendesk@^2.0.15", "processes/": "./src/packages/processes/", "simple-git": "npm:simple-git@^3.25.0", "string-ts": "npm:string-ts@^2.2.0", "ts-reset": "npm:@total-typescript/ts-reset@^0.5.1", "types/": "./src/types/", "chrome-types": "npm:@types/chrome@^0.0.270", "google-apps-script-types": "npm:@types/google-apps-script@^1.0.83", "utilities/": "./src/environment/utilities/", "zod": "npm:zod@3.23.8", "totp-generator": "npm:totp-generator@^1.0.0", "load-env": "jsr:@std/dotenv/load", "Buffer": "jsr:@std/io/buffer", "firebase-admin": "npm:firebase-admin@^12.7.0", "streams": "jsr:@std/streams", "winston": "npm:winston@3.17.0", "@jsforce": "npm:@types/jsforce@1.11.0", "string-similarity-js": "npm:string-similarity-js@2.1.4", "flat": "npm:flat@6.0.1", "zod-openapi": "npm:zod-openapi@4.2.4", "luxon": "npm:luxon@3.6.1", "prompts": "npm:prompts@2.4.2"}, "fmt": {"useTabs": false, "lineWidth": 9999, "indentWidth": 2, "semiColons": true, "singleQuote": false, "proseWrap": "never", "exclude": ["deno.lock", ".github/", "dist/", "node_modules/", "deno_cache/"]}, "lint": {"include": ["**/*.ts"], "rules": {"exclude": ["no-unused-vars", "no-namespace", "no-explicit-any", "require-await", "no-window", "no-window-prefix", "ban-types"]}}, "exclude": ["deno.lock", "dist/", "**/dist/", "node_modules/", "deno_cache/", "google-cloud-sdk/", "src/packages/processes/fenx-backfill-mass-update", "src/packages/processes/periodic-reviews-interface", "src/packages/processes/self-declaration-t-and-cs-interface", "src/packages/processes/ops-ebury-add-on"]}